/*

#include "FaceScanner.h"

//FaceScanner faceScanner;


void loop() {
  // Test reset command
  Serial.println("Testing reset command...");
  if (faceScanner.reset()) {
    Serial.println("Reset command succeeded.");
  } else {
    Serial.println("Reset command failed.");
  }
  delay(10000); // 10-second delay

  // Test getStatus command
  uint8_t status;
  Serial.println("Testing getStatus command...");
  if (faceScanner.getStatus(&status)) {
    Serial.print("Status: 0x");
    Serial.println(status, HEX);
  } else {
    Serial.println("getStatus command failed.");
  }
  delay(10000);

  // Test verify command
  uint16_t userId;
  char userName[33];
  bool isAdmin;
  Serial.println("Testing verify command...");
  if (faceScanner.verify(false, 10, 0, &userId, userName, &isAdmin)) {
    Serial.print("User ID: ");
    Serial.println(userId);
    Serial.print("User Name: ");
    Serial.println(userName);
    Serial.print("Is Admin: ");
    Serial.println(isAdmin ? "Yes" : "No");
  } else {
    Serial.println("Verify command failed.");
  }
  delay(10000);

  // Test enroll command
  Serial.println("Testing enroll command...");
  if (faceScanner.enroll(false, "TestUser", 0, 10, &userId)) {
    Serial.print("Enroll succeeded. User ID: ");
    Serial.println(userId);
  } else {
    Serial.println("Enroll command failed.");
  }
  delay(10000);

  // Test snapImage command
  Serial.println("Testing snapImage command...");
  if (faceScanner.snapImage(1, 0)) {
    Serial.println("snapImage command succeeded.");
  } else {
    Serial.println("snapImage command failed.");
  }
  delay(10000);

  // Test getSavedImageSize command
  uint16_t imageSize;
  Serial.println("Testing getSavedImageSize command...");
  if (faceScanner.getSavedImageSize(0, &imageSize)) {
    Serial.print("Image Size: ");
    Serial.println(imageSize);
  } else {
    Serial.println("getSavedImageSize command failed.");
  }
  delay(10000);

  // Test uploadImage command
  uint8_t imageData[1024]; // Example buffer for image data
  Serial.println("Testing uploadImage command...");
  if (faceScanner.uploadImage(0, 1024, imageData)) {
    Serial.println("uploadImage command succeeded.");
  } else {
    Serial.println("uploadImage command failed.");
  }
  delay(10000);

  // Test enrollSingle command
  Serial.println("Testing enrollSingle command...");
  if (faceScanner.enrollSingle(false, "TestUserSingle", 0, 10, &userId)) {
    Serial.print("enrollSingle succeeded. User ID: ");
    Serial.println(userId);
  } else {
    Serial.println("enrollSingle command failed.");
  }
  delay(10000);

  // Test deleteUser command
  Serial.println("Testing deleteUser command...");
  if (faceScanner.deleteUser(userId, 0)) {
    Serial.println("deleteUser command succeeded.");
  } else {
    Serial.println("deleteUser command failed.");
  }
  delay(10000);

  // Test deleteAllUsers command
  Serial.println("Testing deleteAllUsers command...");
  if (faceScanner.deleteAllUsers(0)) {
    Serial.println("deleteAllUsers command succeeded.");
  } else {
    Serial.println("deleteAllUsers command failed.");
  }
  delay(10000);

  // Test getUserInfo command
  Serial.println("Testing getUserInfo command...");
  if (faceScanner.getUserInfo(userId, userName, &isAdmin)) {
    Serial.print("User Name: ");
    Serial.println(userName);
    Serial.print("Is Admin: ");
    Serial.println(isAdmin ? "Yes" : "No");
  } else {
    Serial.println("getUserInfo command failed.");
  }
  delay(10000);

  // Test faceReset command
  Serial.println("Testing faceReset command...");
  if (faceScanner.faceReset()) {
    Serial.println("faceReset command succeeded.");
  } else {
    Serial.println("faceReset command failed.");
  }
  delay(10000);

  // Test getAllUserIds command
  uint8_t userCount;
  uint16_t userIds[100];
  Serial.println("Testing getAllUserIds command...");
  if (faceScanner.getAllUserIds(0, &userCount, userIds)) {
    Serial.print("User Count: ");
    Serial.println(userCount);
    for (uint8_t i = 0; i < userCount; i++) {
      Serial.print("User ID ");
      Serial.print(i);
      Serial.print(": ");
      Serial.println(userIds[i]);
    }
  } else {
    Serial.println("getAllUserIds command failed.");
  }
  delay(10000);

  // Test enrollITG command
  Serial.println("Testing enrollITG command...");
  if (faceScanner.enrollITG(false, "TestUserITG", 0, 0, false, 10, &userId)) {
    Serial.print("enrollITG succeeded. User ID: ");
    Serial.println(userId);
  } else {
    Serial.println("enrollITG command failed.");
  }
  delay(10000);

  // Test getVersion command
  char versionInfo[17];
  Serial.println("Testing getVersion command...");
  if (faceScanner.getVersion(versionInfo)) {
    Serial.print("Version: ");
    Serial.println(versionInfo);
  } else {
    Serial.println("getVersion command failed.");
  }
  delay(10000);

  // Test startOTA command
  Serial.println("Testing startOTA command...");
  if (faceScanner.startOTA(1, 0, 0, 0)) {
    Serial.println("startOTA command succeeded.");
  } else {
    Serial.println("startOTA command failed.");
  }
  delay(10000);

  // Test stopOTA command
  Serial.println("Testing stopOTA command...");
  if (faceScanner.stopOTA()) {
    Serial.println("stopOTA command succeeded.");
  } else {
    Serial.println("stopOTA command failed.");
  }
  delay(10000);

  // Test getOTAStatus command
  uint8_t otaStatus;
  uint16_t nextPid;
  Serial.println("Testing getOTAStatus command...");
  if (faceScanner.getOTAStatus(&otaStatus, &nextPid)) {
    Serial.print("OTA Status: ");
    Serial.println(otaStatus);
    Serial.print("Next PID: ");
    Serial.println(nextPid);
  } else {
    Serial.println("getOTAStatus command failed.");
  }
  delay(10000);

  // Test otaHeader command
  uint8_t md5Sum[8] = {0};
  Serial.println("Testing otaHeader command...");
  if (faceScanner.otaHeader(1024, 10, 128, md5Sum)) {
    Serial.println("otaHeader command succeeded.");
  } else {
    Serial.println("otaHeader command failed.");
  }
  delay(10000);

  // Test otaPacket command
  uint8_t otaData[128] = {0};
  Serial.println("Testing otaPacket command...");
  if (faceScanner.otaPacket(0, 128, otaData)) {
    Serial.println("otaPacket command succeeded.");
  } else {
    Serial.println("otaPacket command failed.");
  }
  delay(10000);

  // Test initEncryption command
  uint8_t seed[8] = {0};
  uint8_t critime[8] = {0};
  uint8_t deviceId[8];
  Serial.println("Testing initEncryption command...");
  if (faceScanner.initEncryption(seed, 0, critime, deviceId)) {
    Serial.println("initEncryption command succeeded.");
  } else {
    Serial.println("initEncryption command failed.");
  }
  delay(10000);

  // Test configBaudrate command
  Serial.println("Testing configBaudrate command...");
  if (faceScanner.configBaudrate(0)) {
    Serial.println("configBaudrate command succeeded.");
  } else {
    Serial.println("configBaudrate command failed.");
  }
  delay(10000);

  // Test setReleaseEncKey command
  uint8_t encKeyNumber[8] = {0};
  Serial.println("Testing setReleaseEncKey command...");
  if (faceScanner.setReleaseEncKey(encKeyNumber)) {
    Serial.println("setReleaseEncKey command succeeded.");
  } else {
    Serial.println("setReleaseEncKey command failed.");
  }
  delay(10000);

  // Test setDebugEncKey command
  Serial.println("Testing setDebugEncKey command...");
  if (faceScanner.setDebugEncKey(encKeyNumber)) {
Serial.println("setDebugEncKey command succeeded.");
  } else {
    Serial.println("setDebugEncKey command failed.");
  }
  delay(10000);

  // Test getLogfile command
  uint32_t logSize;
  Serial.println("Testing getLogfile command...");
  if (faceScanner.getLogfile(0, 0, &logSize)) {
    Serial.print("Log Size: ");
    Serial.println(logSize);
  } else {
    Serial.println("getLogfile command failed.");
  }
  delay(10000);

  // Test uploadLogfile command
  uint8_t logData[1024] = {0};
  Serial.println("Testing uploadLogfile command...");
  if (faceScanner.uploadLogfile(0, 1024, logData)) {
    Serial.println("uploadLogfile command succeeded.");
  } else {
    Serial.println("uploadLogfile command failed.");
  }
  delay(10000);

  // Test uvcDir command
  Serial.println("Testing uvcDir command...");
  if (faceScanner.uvcDir(0)) {
    Serial.println("uvcDir command succeeded.");
  } else {
    Serial.println("uvcDir command failed.");
  }
  delay(10000);

  // Test transFilePacket command
  uint8_t userCountTrans;
  uint16_t userIdsTrans[100];
  uint8_t transData[128] = {0};
  Serial.println("Testing transFilePacket command...");
  if (faceScanner.transFilePacket(0, 1024, 0, 128, transData, &userCountTrans, userIdsTrans)) {
    Serial.print("User Count: ");
    Serial.println(userCountTrans);
    for (uint8_t i = 0; i < userCountTrans; i++) {
      Serial.print("User ID ");
      Serial.print(i);
      Serial.print(": ");
      Serial.println(userIdsTrans[i]);
    }
  } else {
    Serial.println("transFilePacket command failed.");
  }
  delay(10000);

  // Test enrollFromImage command
  Serial.println("Testing enrollFromImage command...");
  if (faceScanner.enrollFromImage(false, "TestUserImage", 0, 10, &userId)) {
    Serial.print("enrollFromImage succeeded. User ID: ");
    Serial.println(userId);
  } else {
    Serial.println("enrollFromImage command failed.");
  }
  delay(10000);

  // Test getUID command
  uint8_t deviceIdGet[8];
  Serial.println("Testing getUID command...");
  if (faceScanner.getUID(deviceIdGet)) {
    Serial.print("Device ID: ");
    for (uint8_t i = 0; i < 8; i++) {
      Serial.print(deviceIdGet[i], HEX);
      Serial.print(" ");
    }
    Serial.println();
  } else {
    Serial.println("getUID command failed.");
  }
  delay(10000);

  // Test cameraFlip command
  Serial.println("Testing cameraFlip command...");
  if (faceScanner.cameraFlip(1)) {
    Serial.println("cameraFlip command succeeded.");
  } else {
    Serial.println("cameraFlip command failed.");
  }
  delay(10000);

  // Test funcCtrl command
  Serial.println("Testing funcCtrl command...");
  if (faceScanner.funcCtrl(1)) {
    Serial.println("funcCtrl command succeeded.");
  } else {
    Serial.println("funcCtrl command failed.");
  }
  delay(10000);

  // Test setThresholdLevel command
  Serial.println("Testing setThresholdLevel command...");
  if (faceScanner.setThresholdLevel(50, 50)) {
    Serial.println("setThresholdLevel command succeeded.");
  } else {
    Serial.println("setThresholdLevel command failed.");
  }
  delay(10000);

  // Test enterDemoMode command
  Serial.println("Testing enterDemoMode command...");
  if (faceScanner.enterDemoMode(true)) {
    Serial.println("enterDemoMode command succeeded.");
  } else {
    Serial.println("enterDemoMode command failed.");
  }
  delay(10000);

  // Test snapImage2 command
  Serial.println("Testing snapImage2 command...");
  if (faceScanner.snapImage2(1, 10)) {
    Serial.println("snapImage2 command succeeded.");
  } else {
    Serial.println("snapImage2 command failed.");
  }
  delay(10000);

  Serial.println("Demo completed. Restarting...");
  delay(10000); // Wait before restarting the demo
}


*/
