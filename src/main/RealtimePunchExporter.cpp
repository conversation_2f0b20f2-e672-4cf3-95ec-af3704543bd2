/**
 * @file RealtimePunchExporter.cpp
 * @brief Standalone class for exporting punch data in real-time to a server
 *
 * This class is designed to be independent of ESP-IDF specific dependencies
 * and can be used in both ESP-IDF and Arduino environments.
 *
 * Dependencies:
 * - HTTPClient.h (Arduino HTTP Client)
 */

#include <HTTPClient.h>
#include <string.h>
#include "Arduino.h"
#include "util.h"

// Define the punch data structure to match the existing system
typedef struct __attribute__((packed))
{
    char timestamp[17];      /*dd-mm-yy HH:mm:ss*/
    char empcode[12];        /* Alphanumeric employee code (12 bytes) */
    uint8_t in_out;          /* 0=In, 1=Out (1 byte) */
    uint8_t accepted_denied; /* 0=Denied, 1=Accepted (1 byte) */
    uint8_t verified_by;     /* Verification method (e.g., 0=card) (1 byte) */
} punchData;

class RealtimePunchExporter
{
private:
    String serverUrl;
    int timeoutMs;

    /**
     * @brief Convert timestamp from dd-mm-yy HH:mm:ss to yyyyMMddHHmmss format
     *
     * @param timestamp Input timestamp in dd-mm-yyyy HH:mm:ss format
     * @return String Converted timestamp in yyyyMMddHHmmss format
     */
    String convertTimestamp(const char *timestamp)
    {
        // Input format: dd-mm-yyyy HH:mm:ss i.e. 19-05-2025 14:53:22
        // Output format: yyyyMMddHHmmss i.e. 20250519145322

        char output[15]; // yyyyMMddHHmmss + null terminator

        // Extract components from timestamp
        Log("[convertTimestamp] Extracting date and time components");
        Log("[convertTimestamp] Timestamp: " + String(timestamp));

        int day = (timestamp[0] - '0') * 10 + (timestamp[1] - '0');
        int month = (timestamp[3] - '0') * 10 + (timestamp[4] - '0');
        int year = (timestamp[6] - '0') * 1000 + (timestamp[7] - '0') * 100 + (timestamp[8] - '0') * 10 + (timestamp[9] - '0');

        // Extract time components
        int hour = (timestamp[11] - '0') * 10 + (timestamp[12] - '0');
        int minute = (timestamp[14] - '0') * 10 + (timestamp[15] - '0');
        int second = 0; // (timestamp[17] - '0') * 10 + (timestamp[18] - '0');

        // Format the output string
        sprintf(output, "%04d%02d%02d%02d%02d%02d", year, month, day, hour, minute, second);

        Log("[convertTimestamp] Converted timestamp: " + String(output));
        return String(output);
    }

    /**
     * @brief Get verification mode string based on verified_by code
     *
     * @param verified_by Verification method code
     * @return String Human-readable verification mode
     */
    String getVerifyMode(uint8_t verified_by)
    {
        switch (verified_by)
        {
        case 0:
            return "Card";
        case 1:
            return "Password";
        case 2:
            return "Fingerprint";
        case 3:
            return "Face";
        default:
            return "Other";
        }
    }

    /**
     * @brief Get in/out mode string based on in_out code
     *
     * @param in_out In/Out code (0=In, 1=Out)
     * @return String "In" or "Out"
     */
    String getInOutMode(uint8_t in_out)
    {
        return in_out == 0 ? "In" : "Out";
    }

public:
    String dev_id = "";
    String dev_model = "OASIS Mini";
    String token = "1234567890";
    /**
     * @brief Constructor for RealtimePunchExporter
     *
     * @param url Server URL for punch data
     * @param timeout HTTP request timeout in milliseconds
     */
    RealtimePunchExporter(const String &url, const String &deviceid, int timeout = 5000)
        : serverUrl(url), timeoutMs(timeout)
    {
        this->dev_id = deviceid;
        Log("[exportPunch] Dev_id: " + dev_id);
    }

    /**
     * @brief Export punch data to the server in real-time
     *
     * @param data Punch data structure
     * @return bool true if successful, false otherwise
     */
    bool exportPunch(const punchData &data)
    {
        Log("[exportPunch] Starting punch export with struct data");
        Log("[exportPunch] Timestamp: " + String(data.timestamp));
        Log("[exportPunch] Employee code: " + String(data.empcode));
        Log("[exportPunch] In/Out: " + String(data.in_out));
        Log("[exportPunch] Accepted/Denied: " + String(data.accepted_denied));
        Log("[exportPunch] Verified by: " + String(data.verified_by));
        Log("[exportPunch] Server URL: " + serverUrl);

        HTTPClient http;
        http.setTimeout(timeoutMs);
        Log("[exportPunch] HTTP timeout set to " + String(timeoutMs) + "ms");

        // Begin HTTP connection
        Log("[exportPunch] Beginning HTTP connection to: " + serverUrl);
        http.begin(serverUrl);

        // Set headers
        Log("[exportPunch] Setting HTTP headers");
        http.addHeader("Content-Type", "application/json");
        http.addHeader("request_code", "realtime_glog");
        http.addHeader("dev_id", dev_id);
        http.addHeader("dev_model", dev_model);
        http.addHeader("token", token);

        // Preparing for JSON creation
        Log("[exportPunch] Creating JSON string");

        // Convert timestamp to required format
        String convertedTime = convertTimestamp(data.timestamp);
        Log("[exportPunch] Converted timestamp: " + convertedTime);

        // Get verification mode string
        String verifyMode = getVerifyMode(data.verified_by);
        Log("[exportPunch] Verification mode: " + verifyMode);

        // Get in/out mode string
        String inOutMode = getInOutMode(data.in_out);
        Log("[exportPunch] In/Out mode: " + inOutMode);

        // Get door mode
        String doorMode = data.accepted_denied == 1 ? "Open" : "Closed";
        Log("[exportPunch] Door mode: " + doorMode);

        // Build JSON string manually through concatenation
        String jsonString = "{";
        jsonString += "\"userId\":\"" + String(data.empcode) + "\",";
        jsonString += "\"time\":\"" + convertedTime + "\",";
        jsonString += "\"verifyMode\":\"" + verifyMode + "\",";
        jsonString += "\"ioMode\":" + String(data.in_out) + ",";
        jsonString += "\"inOut\":\"" + inOutMode + "\",";
        jsonString += "\"doorMode\":\"" + doorMode + "\"";
        jsonString += "}";
        Log("[exportPunch] JSON payload: " + jsonString);

        // Send POST request
        Log("[exportPunch] Sending POST request");
        int httpResponseCode = http.POST(jsonString);
        Log("[exportPunch] HTTP response code: " + String(httpResponseCode));

        bool success = false;

        if (httpResponseCode > 0)
        {
            // Check response headers for confirmation
            String responseCode = http.header("response_code");
            String transId = http.header("trans_id");

            // Log("[exportPunch] Response header - response_code: " + responseCode);
            // Log("[exportPunch] Response header - trans_id: " + transId);

            // Check if server confirmed the transaction
            if (responseCode.equals("OK") && httpResponseCode == 200)
            {
                success = true;
                Log("[exportPunch] Transaction successful");
            }
            else
            {
                Log("[exportPunch] Transaction failed - Invalid server confirmation");
            }

            // For debugging purposes
            String responseBody = http.getString();
            Log("[exportPunch] Response body: " + responseBody);
        }
        else
        {
            Log("[exportPunch] HTTP request failed");
        }

        // Close connection
        Log("[exportPunch] Closing HTTP connection");
        http.end();

        Log("[exportPunch] Export result: " + String(success ? "SUCCESS" : "FAILURE"));
        return success;
    }

    /**
     * @brief Set server URL
     *
     * @param url New server URL
     */
    void setServerUrl(const String &url)
    {
        serverUrl = url;
    }

    /**
     * @brief Set HTTP timeout
     *
     * @param timeout Timeout in milliseconds
     */
    void setTimeout(int timeout)
    {
        timeoutMs = timeout;
    }

    /**
     * @brief Export punch data to the server using individual parameters
     *
     * @param empcode Employee code (string)
     * @param datetime Date and time in format "dd-mm-yy HH:mm" (string)
     * @param inout In/Out status ("I" or "O")
     * @param verifyBy Verification method ("card", "face", "palm", etc.)
     * @param accepted Whether punch was accepted (default: true)
     * @return bool true if successful, false otherwise
     */
    bool exportPunch(const String &empcode, const String &datetime,
                     const String &inout, const String &verifyBy,
                     bool accepted = true)
    {
        Log("[exportPunch-params] Starting punch export with parameters");
        Log("[exportPunch-params] Employee code: " + empcode);
        Log("[exportPunch-params] Datetime: " + datetime);
        Log("[exportPunch-params] In/Out: " + inout);
        Log("[exportPunch-params] Verify By: " + verifyBy);
        Log("[exportPunch-params] Accepted: " + String(accepted ? "true" : "false"));
        Log("[exportPunch-params] Server URL: " + serverUrl);

        HTTPClient http;
        http.setTimeout(timeoutMs);
        Log("[exportPunch-params] HTTP timeout set to " + String(timeoutMs) + "ms");

        // Begin HTTP connection
        Log("[exportPunch-params] Beginning HTTP connection to: " + serverUrl);
        http.begin(serverUrl);

        // Set headers
        Log("[exportPunch-params] Setting HTTP headers");
        http.addHeader("Content-Type", "application/json");
        http.addHeader("request_code", "realtime_glog");
        http.addHeader("dev_id", dev_id);
        http.addHeader("dev_model", dev_model);
        http.addHeader("token", token);

        // Preparing for JSON creation
        Log("[exportPunch-params] Creating JSON string");

        // Convert datetime to required format (add seconds if missing)
        String timeStr = datetime;
        if (timeStr.length() == 14)
        { // "dd-mm-yy HH:mm" format, need to add seconds
            timeStr += ":00";
            Log("[exportPunch-params] Added seconds to timestamp: " + timeStr);
        }

        // Convert inout from I/O to numeric and string formats
        int ioMode = (inout.equals("Out") || inout.equals("out")) ? 1 : 0;
        String inOutStr = (ioMode == 0) ? "In" : "Out";
        Log("[exportPunch-params] Converted I/O: " + inout + " → ioMode=" + String(ioMode) + ", inOutStr=" + inOutStr);

        // Map verification method to proper format if needed
        String verifyModeStr = verifyBy;
        verifyModeStr.trim();
        // Capitalize first letter
        if (verifyModeStr.length() > 0)
        {
            verifyModeStr.setCharAt(0, toupper(verifyModeStr.charAt(0)));
        }
        Log("[exportPunch-params] Formatted verification method: " + verifyModeStr);

        // Convert timestamp to server format
        String convertedTime = convertTimestamp(timeStr.c_str());
        Log("[exportPunch-params] Converted timestamp: " + convertedTime);

        // Get door mode
        String doorMode = accepted ? "Open" : "Closed";
        Log("[exportPunch-params] Door mode: " + doorMode);

        // Build JSON string manually through concatenation
        String jsonString = "{";
        jsonString += "\"userId\":\"" + empcode + "\",";
        jsonString += "\"time\":\"" + convertedTime + "\",";
        jsonString += "\"verifyMode\":\"" + verifyModeStr + "\",";
        jsonString += "\"ioMode\":" + String(ioMode) + ",";
        jsonString += "\"inOut\":\"" + inOutStr + "\",";
        jsonString += "\"doorMode\":\"" + doorMode + "\"";
        jsonString += "}";
        Log("[exportPunch-params] JSON payload: " + jsonString);

        // Send POST request
        Log("[exportPunch-params] Sending POST request");
        int httpResponseCode = http.POST(jsonString);
        Log("[exportPunch-params] HTTP response code: " + String(httpResponseCode));

        bool success = false;

        if (httpResponseCode > 0)
        {

            // Check response headers for confirmation
            // String responseCode = http.header("response_code");
            // String transId = http.header("trans_id");

            // Log("[exportPunch-params] Response header - response_code: " + responseCode);
            // Log("[exportPunch-params] Response header - trans_id: " + transId);

            // Check if server confirmed the transaction
            if (httpResponseCode == 200)
            {
                success = true;
                Log("[exportPunch-params] Transaction successful");
            }
            else
            {
                Log("[exportPunch-params] Transaction failed - Invalid server confirmation");
            }

            // For debugging purposes
            String responseBody = http.getString();
            Log("[exportPunch-params] Response body: " + responseBody);
        }
        else
        {
            Log("[exportPunch-params] HTTP request failed");
        }

        // Close connection
        Log("[exportPunch-params] Closing HTTP connection");
        http.end();

        Log("[exportPunch-params] Export result: " + String(success ? "SUCCESS" : "FAILURE"));
        return success;
    }
};

// Example usage (commented out, for reference only):
/*
void example() {
    // Create exporter instance
    RealtimePunchExporter exporter("http://example.com/api/punch");

    // Create punch data
    punchData data;
    strcpy(data.timestamp, "20-05-25 09:30:15");
    strcpy(data.empcode, "EMP12345");
    data.in_out = 0; // In
    data.accepted_denied = 1; // Accepted
    data.verified_by = 3; // Face

    // Export punch data
    bool success = exporter.exportPunch(data);

    if (success) {
        Serial.println("Punch exported successfully!");
    } else {
        Serial.println("Failed to export punch data");
    }
}
*/
