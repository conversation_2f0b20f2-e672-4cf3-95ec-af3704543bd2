#include <Preferences.h>
#include <Arduino.h>
#include <SPIFFS.h>
#include <main.h>
#include <HTTPClient.h>


//for touch
int16_t  px = 0;
int16_t  py = 0;

// Auto-selection mode (0=manual, 1=auto IN, 2=auto OUT)
int inoutMode = 0;

// ADDED: Ensure displayMutex is declared extern if not already visible here
extern SemaphoreHandle_t displayMutex;

/*
// Function to transmit attendance record to remote server
bool transmitAttendanceRecord(const String& empId, const String& name, const String& status,
                             const String& dateStr, const String& timeStr, const String& trIdStr, const String& device_type) {
    Preferences prefs;
    prefs.begin("wifi-config", false);
    String apiEndpoint = prefs.getString("api_endpoint", "");
    prefs.end();

    if(apiEndpoint.isEmpty()) {
        Serial.println("WARNING: No API endpoint configured");
        return false;
    }

    HTTPClient http;
    http.begin(apiEndpoint);
    http.addHeader("Content-Type", "application/json");

    // Create JSON payload
    String payload = "{\"empId\":\"" + empId + "\",";
    payload += "\"name\":\"" + name + "\",";
    payload += "\"date\":\"" + dateStr + "\",";
    payload += "\"time\":\"" + timeStr + "\",";
    payload += "\"status\":\"" + status + "\",";
    payload += "\"deviceType\":\"" + device_type + "\",";
    payload += "\"transactionId\":\"" + trIdStr + "\"}";

    // Try transmission up to 3 times
    for(int attempt = 0; attempt < 3; attempt++) {
        int httpCode = http.POST(payload);
        if(httpCode == HTTP_CODE_OK) {
            http.end();
            return true;
        }
        Serial.printf("Attempt %d: HTTP error %d\n", attempt+1, httpCode);
        delay(1000 * (attempt+1)); // Exponential backoff
    }

    http.end();
    return false;
}
 */

void saveAttendanceRecord(const String &empId, const String &name, const String &status, const String &device_type)
{
  DateTime now = rtc.now();

  // Format date and time
  char dateStr[11];
  char timeStr[6];
  sprintf(dateStr, "%02d-%02d-%02d", now.day(), now.month(), now.year());
  sprintf(timeStr, "%02d:%02d", now.hour(), now.minute());

  // Remove leading zeros from empId
  String formattedEmpId = String(empId.toInt()); // Convert to int and back to String removes leading zeros

  // Create the record string
  String record = formattedEmpId + "," + name + "," + String(dateStr) + "," + String(timeStr) + "," + status + "," + device_type + "\n";

  // Get current date for filename
  String filename = "/" + String(dateStr) + ".csv";

  // Open file in append mode
  File file = SPIFFS.open(filename, "a");
  if (!file)
  {
    Serial.println("ERROR: Failed to open attendance file");
    return;
  }

  // Write header if file is empty
  if (file.size() == 0)
  {
    file.print("Employee ID,Name           ,Date      ,Time ,Status,Device\n");
  }

  // Format record with fixed-width columns
  char formattedRecord[150];
  snprintf(formattedRecord, sizeof(formattedRecord),
           "%-10s,%-15s,%10s,%5s,%6s,%s\n",
           formattedEmpId.c_str(),
           name.c_str(),
           dateStr,
           timeStr,
           status.c_str(),
           device_type.c_str());

  // Write the record
  if (file.print(formattedRecord))
  {
    Serial.printf("Attendance record written: %s", record.c_str());

    // Additional logging to attlogs.csv if WEBMIS is enabled
    if (WEBMIS)
    {
      // Initialize Preferences for Tr_Id persistence
      Preferences prefs;
      prefs.begin("attlog", false);

      // Format Tr_Id as 8-digit zero-padded number
      char trIdStr[9];
      snprintf(trIdStr, sizeof(trIdStr), "%08lu", Tr_Id);

      // Create attlogs record
      String attlogRecord = formattedEmpId + "," + String(trIdStr) + "," + String(dateStr) + "," + String(timeStr) + "," + status + "," + device_type + "\n";

      // Write to attlogs.csv
      File attlogFile = SPIFFS.open("/attlogs.csv", "a");
      if (attlogFile)
      {
        if (attlogFile.size() == 0)
        {
          attlogFile.print("Employee ID,Tr_ID,Date,Time,Status,Device\n");
        }
        if (attlogFile.print(attlogRecord))
        {
          Serial.printf("attlogs.csv record written: %s", attlogRecord.c_str());
          Tr_Id++;                        // Increment transaction ID
          prefs.putULong("Tr_Id", Tr_Id); // Save new value
          prefs.end();
        }
        else
        {
          Serial.println("ERROR: Failed to write attlogs.csv record");
        }
        attlogFile.close();
      }
      else
      {
        Serial.println("ERROR: Failed to open attlogs.csv");
      }
    }
  }
  else
  {
    Serial.println("ERROR: Failed to write attendance record");
  }

  file.close();
}

// Add this function to clear employee data
void clearEmployeeData()
{
  currentEmpId = "";
  currentEmpName = "";
  isValidEmployee = false;
}

void showMenuScreen(const String &device_type)
{
  // Add exhastive comments to debug this function
  Serial.println("in showMenuScreen");
  Serial.print("currentEmpId: ");
  Serial.println(currentEmpId);
  Serial.print("currentEmpName: ");
  Serial.println(currentEmpName);
  Serial.print("isValidEmployee: ");
  Serial.println(isValidEmployee);
  Serial.print("device_type: ");
  Serial.println(device_type);
  // Check if we have valid employee data
  if (currentEmpId.isEmpty() || currentEmpName.isEmpty())
  {
    Serial.println("ERROR: No valid employee data");
    ERROR_BEEP(0.5, 2);

    return;
  }

  // Take mutex before drawing IN/OUT boxes
  if (xSemaphoreTake(displayMutex, portMAX_DELAY))
  {
    tft.setFreeFont(&FreeSans12pt7b);
    tft.setTextSize(1);
    // Only show IN/OUT buttons if INOUT is 0
    Serial.print("INOUT In showMenuScreen:::::::::::");
    Serial.println(INOUT);
    if (INOUT == 0)
    {
      // Calculate position 20px above Box4
      int boxTop = BAND4_Y - 20 - 50; // 50 is box height
      
      // Draw "IN" box
      delay(100);
      tft.fillRect(30, boxTop, 85, 50, TFT_GREEN);
      tft.setTextColor(TFT_WHITE);
      tft.setCursor(65, boxTop + 30); // 30 is text offset
      tft.print("IN");

      // Draw "OUT" box
      tft.fillRect(200, boxTop, 85, 50, TFT_GREEN);
      tft.setTextColor(TFT_WHITE);
      tft.setCursor(220, boxTop + 30);
      tft.print("OUT");
    }

    // Release mutex after drawing initial boxes
    xSemaphoreGive(displayMutex);
  }
  Serial.println("Waiting for touch event or auto-select based on INOUT");

  // Wait for touch event or auto-select based on inoutMode
    unsigned long start = millis();
    while (millis() - start < 5000) {
        String status;
        bool validTouch = false;
        
        if (inoutMode == 1 || inoutMode == 2) {
            // Auto-select status based on inoutMode
            status = (inoutMode == 1) ? "I" : "O";
            validTouch = true;
            
            if (xSemaphoreTake(displayMutex, portMAX_DELAY)) {
                tft.fillRect(0, 90, 240, 160, TFT_BLACK);
                xSemaphoreGive(displayMutex);
                delay(100);
                writetotft(32, (inoutMode == 1) ? "WELCOME" : "GOODBYE");
                START_RELAY1(10); //APPX 5 sec.
                break; // Exit the loop immediately
            }
        }
        else ts.read(); 
            if (ts.isTouched) {
                 for (int i=0; i<ts.touches; i++){
          px = ts.points[i].x;
          py = ts.points[i].y;
                 }
            if (px > 40 && px < 100 && py > (BAND4_Y - 20 - 50) && py < (BAND4_Y - 20)) {
                Serial.print("IN box touched");
                if (xSemaphoreTake(displayMutex, portMAX_DELAY)) {
                    tft.fillRect(0, 90, 240, 160, TFT_BLACK);
                    xSemaphoreGive(displayMutex);
                    delay(100);
                    writetotft(32, "WELCOME");
                    status = "I";
                    validTouch = true;
                    START_RELAY1(10); //APPX 5 sec.
                }
            }
            else if (px > 150 && px < 235 && py > (BAND4_Y - 20 - 50) && py < (BAND4_Y - 20)) {
                Serial.print("OUT box touched");
                if (xSemaphoreTake(displayMutex, portMAX_DELAY)) {
                    tft.fillRect(0, 90, 240, 160, TFT_BLACK);
                    xSemaphoreGive(displayMutex);
                    delay(100);
                    writetotft(32,"GOODBYE");
                    status = "O";
                    validTouch = true;
                    START_RELAY1(10); //APPX 5 sec.
                }
            }
        }
        
        if (validTouch) {
            // Save the attendance record
            saveAttendanceRecord(currentEmpId, currentEmpName, status, device_type);

      clearEmployeeData(); // Clear the data after processing

      delay(2000);
      card_band();
      break; // Exit the loop after valid touch
    }

    delay(50);
    face = 0;
  }

  card_band();
}
