// enrollment.ino
#include "main.h"

// Menu 2 Data (Enroll Menu)
const ButtonInfo menu2Buttons[] = {
    {"FACE", "/M2B1"},   // Triggers Face Enroll via handleCommand
    {"PALM", "/M2B2"},   // Triggers Palm Enroll via handleCommand
    {"FINGER", "/M2B3"}, // Triggers Finger Enroll via handleCommand
    {"CARD", "/M2B4"}    // Triggers Card Enroll via handleCommand
};
const char *menu2Title = "ENROLL MENU";
const char *menu2BackUrl = "/menu1"; // Back to Main Menu

// --- Enrollment Menu Handler ---
void handleMenu2()
{
  resetCommandState(); // Reset command state when entering enroll menu
  sendMenuPage(menu2Title, menu2Buttons, sizeof(menu2Buttons) / sizeof(menu2Buttons[0]), menu2BackUrl);
}

// --- Employee Enrollment Handlers ---
void handleEmployeeDetails()
{
  if (!isLoggedIn)
  {
    server.sendHeader("Location", "/");
    server.send(302);
    return;
  }
  lastActivityTime = millis();
  String page = FPSTR(empIdPage); // Get HTML from html_templates.ino
  page.replace("%STYLE%", FPSTR(htmlStyle));
  page.replace("%TIMER_SCRIPT%", FPSTR(timerScript));
  server.send(200, "text/html", page);
}

void handleSaveEmpDetails()
{
  if (!isLoggedIn)
  {
    server.sendHeader("Location", "/");
    server.send(302);
    return;
  }
  lastActivityTime = millis();

  if (server.hasArg("empid") && server.hasArg("empname"))
  {
    EMP_ID = server.arg("empid").toInt();
    EMP_NAME = server.arg("empname");
    EMP_NAME.trim();                      // Ensure no leading/trailing spaces
    is_admin = server.hasArg("is_admin"); // Check if the admin checkbox was checked

    if (EMP_ID < 1 || EMP_ID > 999999 || EMP_NAME.length() == 0 || EMP_NAME.length() > 16)
    {
      server.send(400, "text/plain", "Invalid EMP_ID or Name.");
      return;
    }

    // Create zero-padded EMP_ID string (6 digits)
    char paddedEmpId[7];
    snprintf(paddedEmpId, sizeof(paddedEmpId), "%06d", EMP_ID);

    // Initialize new record with zeros for biometric IDs
    EmployeeRecord record = {0}; // Important: Zero out the struct
    strncpy(record.empId, paddedEmpId, sizeof(record.empId) - 1);
    record.empId[sizeof(record.empId) - 1] = '\0'; // Ensure null termination
    strncpy(record.userName, EMP_NAME.c_str(), sizeof(record.userName) - 1);
    record.userName[sizeof(record.userName) - 1] = '\0'; // Ensure null termination
    record.faceId = 0;
    record.palmId = 0;
    record.fingerId = 0;
    memset(record.cardCsn, 0, sizeof(record.cardCsn)); // Clear card CSN

    // Attempt to load existing record to preserve biometric data
    if (!SPIFFS.begin(false))
    {
      server.send(500, "text/plain", "Failed to mount filesystem");
      return;
    }

    bool recordExists = false;
    File file = SPIFFS.open("/MASTER.csv", "r");
    if (file)
    {
      String line;
      // Skip header
      if (file.available())
        file.readStringUntil('\n');

      while (file.available())
      {
        line = file.readStringUntil('\n');
        line.trim();
        if (line.length() > 0)
        {
          String currentEmpId = line.substring(0, line.indexOf(','));
          if (currentEmpId == String(paddedEmpId))
          {
            recordExists = true;
            // Parse existing biometric IDs and CSN
            int idx1 = line.indexOf(',');
            int idx2 = line.indexOf(',', idx1 + 1);
            int idx3 = line.indexOf(',', idx2 + 1);
            int idx4 = line.indexOf(',', idx3 + 1);
            int idx5 = line.indexOf(',', idx4 + 1);

            if (idx1 != -1 && idx2 != -1 && idx3 != -1 && idx4 != -1 && idx5 != -1)
            {
              // Preserve existing IDs, only update name/admin status if needed
              record.faceId = line.substring(idx2 + 1, idx3).toInt();
              record.palmId = line.substring(idx3 + 1, idx4).toInt();
              record.fingerId = line.substring(idx4 + 1, idx5).toInt();
              String csn = line.substring(idx5 + 1);
              csn.trim();
              strncpy(record.cardCsn, csn.c_str(), sizeof(record.cardCsn) - 1);
              record.cardCsn[sizeof(record.cardCsn) - 1] = '\0';
            }
            break; // Found the record
          }
        }
      }
      file.close();
    }
    else
    {
      Serial.println("MASTER.csv not found, creating new.");
      // Ensure header is written if creating the file via updateMasterRecord
      recordExists = false; // Treat as new record if file doesn't exist
    }

    // Save the updated or new record
    // updateMasterRecord handles file creation/header if needed
    if (updateMasterRecord(record, !recordExists))
    {
      Serial.printf("Employee details %s in MASTER.csv: %s,%s,%d,%d,%d,%s\n",
                    recordExists ? "updated" : "saved",
                    record.empId, record.userName, record.faceId, record.palmId,
                    record.fingerId, record.cardCsn);

      // Create face_string for scanner enrollment (used by handleCommand)
      face_string = String(paddedEmpId) + EMP_NAME;
      flag_enroll = true; // Signal that details are saved, ready for bio/card step

      // Redirect back to menu2
      server.sendHeader("Location", "/menu2?status=saved&empid=" + String(paddedEmpId));
      server.send(302, "text/plain", "Details Saved. Select Enrollment Type.");
    }
    else
    {
      Serial.println("Error saving employee details to MASTER.csv!");
      flag_enroll = false; // Reset flag on failure
      server.send(500, "text/plain", "Failed to save employee details to storage.");
    }
  }
  else
  {
    server.send(400, "text/plain", "Missing required fields (EMP ID or Name)");
  }
}

// --- Data Management Functions --- (Moved from original file)
bool updateMasterRecord(const EmployeeRecord &record, bool isNewRecord /* Parameter indicates if we know it's new */)
{
  if (!SPIFFS.begin(false))
  { // Ensure SPIFFS is mounted
    Serial.println("SPIFFS not mounted, cannot access MASTER.csv");
    return false;
  }

  String tempFilePath = "/MASTER.tmp";
  String masterFilePath = "/MASTER.csv";
  bool recordFoundAndUpdated = false;

  File tempFile = SPIFFS.open(tempFilePath, "w");
  if (!tempFile)
  {
    Serial.println("Failed to create temp file /MASTER.tmp");
    return false;
  }

  // Write header to temp file
  tempFile.println("EmpId,UserName,FaceId,PalmId,FingerId,CardCSN");

  // Process existing master file if it exists
  if (SPIFFS.exists(masterFilePath))
  {
    File masterFile = SPIFFS.open(masterFilePath, "r");
    if (masterFile)
    {
      // Skip existing header
      if (masterFile.available())
        masterFile.readStringUntil('\n');

      // Read lines, update or copy to temp file
      while (masterFile.available())
      {
        String line = masterFile.readStringUntil('\n');
        line.trim();
        if (line.length() == 0)
          continue;

        // Check if this line is the one to update
        String currentEmpId = line.substring(0, line.indexOf(','));
        if (currentEmpId == String(record.empId))
        {
          // Write the updated record to the temp file
          tempFile.printf("%s,%s,%d,%d,%d,%s\n",
                          record.empId, record.userName,
                          record.faceId, record.palmId, record.fingerId,
                          record.cardCsn);
          recordFoundAndUpdated = true;
        }
        else
        {
          // Copy the existing line to the temp file
          tempFile.println(line);
        }
      }
      masterFile.close();
    }
    else
    {
      Serial.println("Failed to open MASTER.csv for reading, but it exists?");
      // Proceeding as if creating a new file, header already written
    }
  }
  else
  {
    Serial.println("MASTER.csv does not exist. Creating new file.");
    // Header already written to tempFile
  }

  // If the record wasn't found in the existing file, add it now
  if (!recordFoundAndUpdated)
  {
    tempFile.printf("%s,%s,%d,%d,%d,%s\n",
                    record.empId, record.userName,
                    record.faceId, record.palmId, record.fingerId,
                    record.cardCsn);
    Serial.printf("Added new record for %s to temp file.\n", record.empId);
  }

  tempFile.close(); // Close the temp file to flush writes

  // Replace the original master file with the temp file
  if (SPIFFS.exists(masterFilePath))
  {
    if (!SPIFFS.remove(masterFilePath))
    {
      Serial.println("Failed to remove original MASTER.csv");
      SPIFFS.remove(tempFilePath); // Clean up temp file
      return false;
    }
  }
  if (!SPIFFS.rename(tempFilePath, masterFilePath))
  {
    Serial.println("Failed to rename temp file to MASTER.csv");
    SPIFFS.remove(tempFilePath); // Clean up temp file
    return false;
  }

  Serial.println("MASTER.csv updated successfully.");
  return true;
}

// Add extern declaration for newFileReceived
extern bool newFileReceived;

void updateEnrollmentRecord(int enrollType, int id, const char *csn)
{
  EmployeeRecord record = {0}; // Initialize record with zeros

  // Format EMP_ID as 6-digit string
  snprintf(record.empId, sizeof(record.empId), "%06d", EMP_ID); // Use the global EMP_ID set during save details

  // Don't overwrite userName here - it will be loaded from existing record
  record.userName[sizeof(record.userName) - 1] = '\0'; // Ensure null termination

  // --- Load existing record first to preserve other data ---
  bool recordFound = false;
  if (!SPIFFS.begin(false))
  {
    Serial.println("SPIFFS not mounted in updateEnrollmentRecord");
    return; // Cannot proceed
  }

  File masterFile = SPIFFS.open("/MASTER.csv", "r");
  if (masterFile)
  {
    // Skip header
    if (masterFile.available())
      masterFile.readStringUntil('\n');

    while (masterFile.available())
    {
      String line = masterFile.readStringUntil('\n');
      line.trim();
      if (line.length() > 0)
      {
        String currentEmpId = line.substring(0, line.indexOf(','));
        if (currentEmpId == String(record.empId))
        {
          recordFound = true;
          // Parse existing biometric IDs and CSN
          int idx1 = line.indexOf(',');
          int idx2 = line.indexOf(',', idx1 + 1);
          int idx3 = line.indexOf(',', idx2 + 1);
          int idx4 = line.indexOf(',', idx3 + 1);
          int idx5 = line.indexOf(',', idx4 + 1);

          if (idx1 != -1 && idx2 != -1 && idx3 != -1 && idx4 != -1 && idx5 != -1)
          {
            // Load existing values including userName
            String existingName = line.substring(idx1 + 1, idx2);
            existingName.trim();
            strncpy(record.userName, existingName.c_str(), sizeof(record.userName) - 1);
            record.userName[sizeof(record.userName) - 1] = '\0';
            record.faceId = line.substring(idx2 + 1, idx3).toInt();
            record.palmId = line.substring(idx3 + 1, idx4).toInt();
            record.fingerId = line.substring(idx4 + 1, idx5).toInt();
            String existingCsn = line.substring(idx5 + 1);
            existingCsn.trim();
            strncpy(record.cardCsn, existingCsn.c_str(), sizeof(record.cardCsn) - 1);
            record.cardCsn[sizeof(record.cardCsn) - 1] = '\0';
          }
          break; // Found the record
        }
      }
    }
    masterFile.close();
  }
  else
  {
    Serial.println("MASTER.csv not found during enrollment update. Cannot load existing data.");
    // Proceed with potentially overwriting if updateMasterRecord creates a new file
  }

  // --- Now update the specific field based on enrollment type ---
  switch (enrollType)
  {
  case 1: // Face
    record.faceId = id;
    Serial.printf("Updating Face ID for %s to %d\n", record.empId, id);
    break;
  case 2: // Palm
    record.palmId = id;
    Serial.printf("Updating Palm ID for %s to %d\n", record.empId, id);
    break;
  case 3: // Finger
    record.fingerId = id;
    Serial.printf("Updating Finger ID for %s to %d\n", record.empId, id);
    break;
  case 4: // Card
    if (csn)
    {
      strncpy(record.cardCsn, csn, sizeof(record.cardCsn) - 1);
      record.cardCsn[sizeof(record.cardCsn) - 1] = '\0';
      Serial.printf("Updating Card CSN for %s to %s\n", record.empId, csn);
    }
    else
    {
      Serial.println("Warning: Card enrollment requested but no CSN provided.");
    }
    break;
  default:
    Serial.printf("Unknown enrollment type: %d\n", enrollType);
    return; // Don't update if type is unknown
  }

  // Update the record in MASTER.csv (passing false as it's an update or known new)
  if (!updateMasterRecord(record, !recordFound))
  {
    Serial.printf("Failed to update MASTER.csv for enrollment type %d\n", enrollType);
  }
  else
  {
    Serial.printf("Successfully updated MASTER.csv for enrollment type %d\n", enrollType);
    // After successful update, set flag to refresh LUT
    if (enrollType == 4)
    { // Only for card enrollments
      newFileReceived = true;
      Serial.println("Set newFileReceived flag to refresh LUT");
    }
  }
}
