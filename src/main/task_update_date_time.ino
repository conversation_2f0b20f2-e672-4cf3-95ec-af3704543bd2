#include <main.h>
#include <Arduino.h>
#include <RTClib.h>

extern RTC_DS3231 rtc;

extern bool colonVisible;

// Global variables
String prevDateStr = "";
String prevTimeStr = "";

bool toggleColon = true; // for colonVisible

// Variables to track changes TFT_eSPI
String lastDate = "";
String lastTime = "";
String lastIP = "";
String lastVersion = "";

// Add these tracking variables with other last* variables
String lastReboot = "";
String lastTotal = "";
String lastPending = "";
String lastTID = "";
String lastINOUT = "";
int lastMinute = -1; // Track last minute to optimize time updates

/**
 * RTOS task to update display when values change TFT_eSPI
 */
void displayUpdateTask(void *parameter)
{
  delay(1000);

  String currentTID = String(TID, HEX);
  currentTID.toUpperCase();
  if (currentTID != lastTID)
  {
    writetotft(43, currentTID.c_str());
    lastTID = currentTID;
    //  Serial.println("TID updated: " + currentTID);
  }

  String currentReboot = String(Reboot);
  // Add new integer value updates
  if (currentReboot != lastReboot)
  {
    writetotft(41, currentReboot.c_str());
    lastReboot = currentReboot;
    //  Serial.println("Reboot updated: " + currentReboot);
  }

  String currentVersion = "V: " + String(version); // Convert external version
  writetotft(5, currentVersion.c_str());

  for (;;)
  {
    // Get current values
    String currentDate = getCurrentDate();
    String currentTime = getCurrentTime(toggleColon);
    IPAddress ip = WiFi.localIP();
    // Convert integers to strings for comparison and display
    String currentIP = "IP: " + String(ip[0]) + "." + String(ip[1]) + "." + String(ip[2]) + "." + String(ip[3]);

    Preferences prefs;
    prefs.begin("attlog", false);
    uint32_t currentTrId = prefs.getULong("Tr_Id", 1);
    uint32_t exportedTrId = prefs.getULong("ExportedTrId", 0);
    prefs.end();

    String currentTotal = String(currentTrId) + "  /  ";
    String currentPending = String((currentTrId)-exportedTrId);
    if ((currentTrId) < exportedTrId)
    {
      currentPending = String(0);
    }

    // Handle IN/OUT display
    String currentINOUT = "";
    if (INOUT == 1)
    {
      currentINOUT = "IN";
    }
    else if (INOUT == 2)
    {
      currentINOUT = "OUT";
    }

    if (currentINOUT != lastINOUT)
    {
      writetotft(33, currentINOUT.c_str());
      lastINOUT = currentINOUT;
    }

    // Update display only if values have changed
    if (currentDate != lastDate)
    {

      writetotft(1, currentDate.c_str());
      lastDate = currentDate;
      // Serial.println("Date updated: " + currentDate);
    }

    // Update time display when minute changes
    DateTime now = rtc.now();
    if (now.minute() != lastMinute)
    {
      String staticTime = getCurrentTime(true); // Always show colon for static update
      writetotft(2, staticTime.c_str());
      lastTime = staticTime;
      lastMinute = now.minute();
      getTimeFromNTP();
      // Serial.println("Time updated: " + staticTime);
    }

    // Handle colon blinking every 800ms
    static unsigned long lastBlink = 0;
    unsigned long currentMillis = millis();
    if (currentMillis - lastBlink >= 800)
    {
      lastBlink = currentMillis;
      toggleColon = !toggleColon;
      String blinkTime = getCurrentTime(toggleColon);
      writetotft(2, blinkTime.c_str());
    }

    // Update IP display (you already have this in your code)
    if (currentIP != lastIP)
    {
      writetotft(6, currentIP.c_str()); // Position 6 for IP as in your original code
      lastIP = currentIP;
      // Serial.println("IP updated: " + currentIP);
    }

    if (currentTotal != lastTotal || currentPending != lastPending)
    {
      writetotft(421, currentTotal.c_str());
      lastTotal = currentTotal;
      writetotft(422, currentPending.c_str());
      lastPending = currentPending;
      //  Serial.println("Pending updated: " + currentPending);
    }

    // Delay for 1 second
    vTaskDelay(1000 / portTICK_PERIOD_MS);
  }
}

/**
 * Get the current date string from RTC
 * @return String in format "day, DD-MMMM-YYYY"
 */
String getCurrentDate()
{
  DateTime now = rtc.now();

  const char *days[] = {"Sun", "Mon", "Tue", "Wed", "Thu", "Fri", "Sat"};
  const char *months[] = {"Jan", "Feb", "Mar", "Apr", "May", "Jun", "Jul", "Aug", "Sep", "Oct", "Nov", "Dec"};

  char dateStr[24];
  sprintf(dateStr, "%s, %02d-%s-%04d",
          days[now.dayOfTheWeek()],
          now.day(),
          months[now.month() - 1],
          now.year());

  return String(dateStr);
}

/** IF CRASHES, change : time to 2 sec.???
 * Get the current time string from RTC
 * @return String in format "HH: mm"
 */
String getCurrentTime(bool showColon)
{
  DateTime now = rtc.now();
  char timeStr[16];
  sprintf(timeStr, "%02d%s%02d", now.hour(), showColon ? ":" : " ", now.minute());
  return String(timeStr);
}

/**
 * Get the current IP address from W5500
 * @return String with IP address
 */
String getIPAddress()
{
  IPAddress ip = Ethernet.localIP();
  return "IP: " + String(ip[0]) + "." + String(ip[1]) + "." + String(ip[2]) + "." + String(ip[3]);
}

// Add array for days of week
const char *dayStr(uint8_t day)
{
  const char *days[] = {"Sun", "Mon", "Tue", "Wed", "Thu", "Fri", "Sat"};
  return days[day];
}

const char *monthShortStr(uint8_t month)
{
  const char *months[] = {"Jan", "Feb", "Mar", "Apr", "May", "Jun", "Jul", "Aug", "Sep", "Oct", "Nov", "Dec"};
  return months[month - 1];
}
