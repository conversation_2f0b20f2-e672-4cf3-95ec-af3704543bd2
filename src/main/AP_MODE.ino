

#include <main.h>
//#include <ESP.h>

// Add Preferences object
Preferences preferences;

// HTML Templates
const char mainPage[] PROGMEM = R"rawliteral(
<!DOCTYPE html>
<html>
<head>
    <title>OASYS-NG Configuration</title>
    <meta name='viewport' content='width=device-width, initial-scale=1'>
    <style>
        /* General body styling */
        body { 
            font-family: Arial, sans-serif; 
            margin: 0; 
            padding: 20px; 
            background-color: #f4f4f4; 
        }

        /* Center the content */
        .container { 
            max-width: 400px; 
            margin: 0 auto; 
            text-align: center; 
        }

        /* Styling for buttons */
        .button { 
            background-color: #4CAF50; /* Green */
            color: white;
            padding: 15px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            margin: 20px 0; /* Increased vertical spacing between buttons */
            text-decoration: none;
            display: block; /* Make buttons stack vertically */
            width: 85%; /* Reduced width to 85% of container */
            margin-left: auto; /* Center the button */
            margin-right: auto; /* Center the button */
        }

        /* Hover effect for green buttons */
        .button:hover { 
            background-color: #45a049; 
        }

        /* Styling for red button */
        .button-red {
            background-color: #ff0000; /* Red */
            color: white;
            padding: 15px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            margin: 20px 0; /* Increased vertical spacing between buttons */
            text-decoration: none;
            display: block; /* Make buttons stack vertically */
            width: 85%; /* Reduced width to 85% of container */
            margin-left: auto; /* Center the button */
            margin-right: auto; /* Center the button */
        }

        /* Hover effect for red button */
        .button-red:hover { 
            background-color: #cc0000; 
        }

        /* Responsive design: Adjust padding for smaller screens */
        @media (max-width: 600px) {
            body {
                padding: 10px;
            }
            .button, .button-red {
                padding: 12px 15px; /* Reduce padding for smaller screens */
            }
        }
    </style>
</head>
<body>
    <div class='container'>
        <h1>OASYS-NG Setup</h1>
        <p>Version: <span id='version'></span></p>
        <!-- Buttons with vertical alignment, spacing, and reduced width -->
        <a href='/wifi' class='button'>WiFi Setup</a>
        <a href='/firmware' class='button'>Firmware Update</a>
        <a href='/files' class='button'>Files</a>
        <a href='/exit' class='button'>Exit Setup</a>
        <a href='/config' class='button' style='background-color: #2196F3;'>Configuration</a>
        <a href='/factory_reset' class='button-red'>Factory Reset</a> <!-- Single red button for Factory Reset -->
    </div>
    <script>
        document.getElementById('version').textContent = '%VERSION%';
    </script>
</body>
</html>
)rawliteral";

const char wifi_page[] PROGMEM = R"rawliteral(
<!DOCTYPE html>
<html>
<head>
    <title>WiFi Networks</title>
    <meta name='viewport' content='width=device-width, initial-scale=1'>
    <style>
        body { font-family: Arial; margin: 0; padding: 20px; }
        .container { max-width: 600px; margin: 0 auto; }
        .network-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 10px;
            border-bottom: 1px solid #ddd;
        }
        .connect-btn {
            background-color: #4CAF50;
            color: white;
            padding: 5px 10px;
            border-radius: 4px;
            text-decoration: none;
        }
        .signal-strength { color: #666; font-size: 0.8em; }
    </style>
</head>
<body>
    <div class='container'>
        <h1>Available Networks</h1>
        <ul style='list-style: none; padding: 0;'>
)rawliteral";

const char wifi_page_end[] PROGMEM = R"rawliteral(
        </ul>
        <a href='/' style='display: inline-block; margin-top: 20px; color: #4CAF50;'>Back to Main Menu</a>
    </div>
</body>
</html>
)rawliteral";

const char PASSWORD_page[] PROGMEM = R"rawliteral(
<!DOCTYPE html>
<html>
<head>
    <title>Enter Password</title>
    <meta name='viewport' content='width=device-width, initial-scale=1'>
    <style>
        body { font-family: Arial; margin: 0; padding: 20px; }
        .container { max-width: 400px; margin: 0 auto; }
        input[type='password'] {
            width: 100%;
            padding: 12px 20px;
            margin: 8px 0;
            box-sizing: border-box;
            border: 2px solid #ccc;
            border-radius: 4px;
        }
        .submit-btn {
            background-color: #4CAF50;
            color: white;
            padding: 14px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            width: 100%;
        }
        .submit-btn:hover { background-color: #45a049; }
    </style>
</head>
<body>
    <div class='container'>
        <h1>Connect to %SSID%</h1>
        <form action='/save' method='POST'>
            <input type='hidden' name='ssid' value='%SSID%'>
            <input type='password' name='password' required placeholder='Enter WiFi Password'>
            <button type='submit' class='submit-btn'>Connect</button>
        </form>
        <a href='/wifi' style='display: inline-block; margin-top: 20px; color: #4CAF50;'>Back to Networks</a>
    </div>
</body>
</html>
)rawliteral";

const char updatePage[] PROGMEM = R"rawliteral(
<!DOCTYPE html>
<html>
<head>
    <title>Firmware Update</title>
    <meta name='viewport' content='width=device-width, initial-scale=1'>
    <style>
        body { font-family: Arial; margin: 0; padding: 20px; }
        .container { max-width: 400px; margin: 0 auto; }
        .progress { 
            width: 100%;
            height: 20px;
            background-color: #f0f0f0;
            border-radius: 10px;
            margin: 10px 0;
        }
        .progress-bar {
            width: 0%;
            height: 100%;
            background-color: #4CAF50;
            border-radius: 10px;
            transition: width 0.3s ease-in-out;
        }
    </style>
</head>
<body>
    <div class='container'>
        <h1>Firmware Update</h1>
        <form method='POST' action='/update' enctype='multipart/form-data'>
            <input type='file' name='update' accept='.bin'>
            <input type='submit' value='Update Firmware'>
        </form>
        <div class='progress'>
            <div class='progress-bar' id='progress'></div>
        </div>
        <p id='status'>Select firmware file...</p>
        <a href='/' style='display: inline-block; margin-top: 20px; color: #4CAF50;'>Back to Main Menu</a>
    </div>
</body>
</html>
)rawliteral";

// Updated HTML template for the files page
const char filesPage[] PROGMEM = R"rawliteral(
<!DOCTYPE html>
<html>
<head>
    <title>File List</title>
    <meta name='viewport' content='width=device-width, initial-scale=1'>
    <style>
        body { font-family: Arial; margin: 0; padding: 20px; }
        .container { max-width: 600px; margin: 0 auto; }
        .file-list { list-style: none; padding: 0; }
        .file-list li { 
            padding: 10px;
            border-bottom: 1px solid #ddd;
        }
        .file-list a {
            color: #4CAF50;
            text-decoration: none;
        }
        .back-button {
            background-color: #4CAF50;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            text-decoration: none;
            display: inline-block;
            margin-top: 20px;
        }
    </style>
</head>
<body>
    <div class='container'>
        <h1>Files in Storage</h1>
        <ul class='file-list'>
            %FILE_LIST%
        </ul>
        <a href='/' class='back-button'>Back to Main Menu</a>
    </div>
</body>
</html>
)rawliteral";

// HTML template for the factory reset page
const char adminPasswordPage[] PROGMEM = R"rawliteral(
<!DOCTYPE html>
<html>
<head>
    <title>Admin Password Setup</title>
    <meta name='viewport' content='width=device-width, initial-scale=1'>
    <style>
        body { font-family: Arial; margin: 0; padding: 20px; }
        .container { max-width: 400px; margin: 0 auto; }
        input[type='password'], input[type='text'] {
            width: 100%;
            padding: 12px 20px;
            margin: 8px 0;
            box-sizing: border-box;
            border: 2px solid #ccc;
            border-radius: 4px;
        }
        .submit-btn {
            background-color: #4CAF50;
            color: white;
            padding: 14px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            width: 100%;
        }
        .submit-btn:hover { background-color: #45a049; }
    </style>
</head>
<body>
    <div class='container'>
        <h1>Admin Password Setup</h1>
        <p>Please set a new admin password:</p>
        <form action='/save_admin_password' method='POST'>
            <input type='password' name='password' required placeholder='Enter Admin Password'>
            <input type='password' name='confirm_password' required placeholder='Confirm Admin Password'>
            <h3>Security Question</h3>
            <p>Enter your Mother's Surname before marriage:</p>
            <input type='text' name='security_question' required placeholder='Enter Surname'>
            <input type='text' name='confirm_security_question' required placeholder='Confirm Surname'>
            <button type='submit' class='submit-btn'>Save Password</button>
        </form>
    </div>
</body>
</html>
)rawliteral";

const char factoryResetPage[] PROGMEM = R"rawliteral(
<!DOCTYPE html>
<html>
<head>
    <title>Factory Reset</title>
    <meta name='viewport' content='width=device-width, initial-scale=1'>
    <style>
        body { font-family: Arial; margin: 0; padding: 20px; }
        .container { max-width: 400px; margin: 0 auto; }
        input[type='password'] {
            width: 100%;
            padding: 12px 20px;
            margin: 8px 0;
            box-sizing: border-box;
            border: 2px solid #ccc;
            border-radius: 4px;
        }
        .submit-btn {
            background-color: #f4444; /* Red */
            color: white;
            padding: 14px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            width: 100%;
        }
        .submit-btn:hover { background-color: #da190b; }
        .back-btn {
            background-color: #4CAF50;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            text-decoration: none;
            display: inline-block;
            margin-top: 20px;
        }
        .back-btn:hover { background-color: #45a049; }
    </style>
</head>
<body>
    <div class='container'>
        <h1>Factory Reset</h1>
        <p>Enter the PIN to perform a factory reset:</p>
        <form action='/factory_reset_confirm' method='POST'>
            <input type='password' name='pin' required placeholder='Enter PIN'>
            <button type='submit' class='submit-btn'>Reset</button>
        </form>
        <a href='/' class='back-btn'>Back to Main Menu</a>
    </div>
</body>
</html>
)rawliteral";

const char configPage[] PROGMEM = R"rawliteral(
<!DOCTYPE html>
<html>
<head>
    <title>System Configuration</title>
    <meta name='viewport' content='width=device-width, initial-scale=1'>
    <style>
        body { 
            font-family: Arial, sans-serif; 
            margin: 0; 
            padding: 20px; 
            background-color: #f4f4f4; 
        }
        .container { 
            max-width: 400px; 
            margin: 0 auto; 
            text-align: center; 
        }
        .button {
            background-color: #4CAF50;
            color: white;
            padding: 15px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            margin: 10px 0;
            text-decoration: none;
            display: block;
            width: 85%;
            margin-left: auto;
            margin-right: auto;
        }
        .button:hover {
            opacity: 0.9;
        }
        #countdown {
            margin-top: 20px;
            color: #666;
        }
        .back-button {
            background-color: #666;
        }
        #backup-btn { background-color: #4CAF50; }
        #restore-btn { background-color: #2196F3; }
        #edit-btn { background-color: #ff9800; }
    </style>
</head>
<body>
    <div class='container'>
        <h1>System Configuration</h1>
        <a href='/backup' class='button' id='backup-btn'>Backup Configuration</a>
        <form action='/restore' method='post' enctype='multipart/form-data'>
            <input type='file' name='config' accept='.csv' style='display: none;' id='restore-file'>
            <a href='#' class='button' id='restore-btn' onclick='document.getElementById("restore-file").click();'>Restore Configuration</a>
        </form>
        <a href='/edit_config' class='button' id='edit-btn'>Edit Configuration</a>
        <a href='/' class='button back-button'>Back to Main Menu</a>
        <div id='countdown'>Time remaining: 120s</div>
    </div>
    <script>
        let timeLeft = 120;
        const countdownElem = document.getElementById('countdown');
        
        setInterval(() => {
            timeLeft--;
            countdownElem.textContent = `Time remaining: ${timeLeft}s`;
            if(timeLeft <= 0) window.location.href = '/';
        }, 1000);

        // Handle file selection for restore
        document.getElementById('restore-file').addEventListener('change', function() {
            if(this.files.length > 0) {
                const formData = new FormData();
                formData.append('config', this.files[0]);
                fetch('/restore', {
                    method: 'POST',
                    body: formData
                }).then(response => response.text())
                  .then(text => {
                      alert(text);
                      if(text.includes('successful')) window.location.reload();
                  });
            }
        });
    </script>
</body>
</html>
)rawliteral";

const char editConfigPage[] PROGMEM = R"rawliteral(
<!DOCTYPE html>
<html>
<head>
    <title>Edit Configuration</title>
    <meta name='viewport' content='width=device-width, initial-scale=1'>
    <style>
        body { 
            font-family: Arial, sans-serif; 
            margin: 0; 
            padding: 20px; 
            background-color: #f4f4f4; 
        }
        .container { 
            max-width: 600px; 
            margin: 0 auto; 
        }
        .config-item {
            margin: 10px 0;
            padding: 10px;
            background: white;
            border-radius: 4px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .config-item label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        .config-item input {
            width: 100%;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            box-sizing: border-box;
        }
        .button {
            background-color: #4CAF50;
            color: white;
            padding: 15px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            margin: 10px 0
            width: 100%;
        }
        .button:hover {
            opacity: 0.9;
        }
        .back-button {
            background-color: #666;
        }
    </style>
</head>
<body>
    <div class='container'>
        <h1>Edit Configuration</h1>
        <form id='config-form' action='/save_config' method='post'>
            %CONFIG_ITEMS%
            <button type='submit' class='button'>Save Changes</button>
            <a href='/config' class='button back-button'>Back</a>
        </form>
    </div>
</body>
</html>
)rawliteral";

// Function declarations
void handleConfig();
void handleConfigBackup();
void handleRestore();
void handleEditConfig();
void handleSaveConfig();
void handleSaveAdminPassword();

// Structure definition
struct PreferenceItem
{
  String key;
  String description;
  String defaultValue;
};

// Handler for saving admin password
void handleSaveAdminPassword()
{
  String password = serverAP.arg("password");
  String confirmPassword = serverAP.arg("confirm_password");
  String securityQuestion = serverAP.arg("security_question");
  String confirmSecurityQuestion = serverAP.arg("confirm_security_question");

  // Validate password match
  if (password != confirmPassword)
  {
    serverAP.send(400, "text/html", "<html><body><h1>Error: Passwords don't match!</h1><a href='/'>Try Again</a></body></html>");
    return;
  }

  // Validate security question match
  if (securityQuestion != confirmSecurityQuestion)
  {
    serverAP.send(400, "text/html", "<html><body><h1>Error: Security answers don't match!</h1><a href='/'>Try Again</a></body></html>");
    return;
  }

  // Save to preferences
  preferences.begin("admin-creds", false);
  preferences.putString("PW1", password);
  preferences.putString("SECQ1", securityQuestion);
  preferences.putInt("FR", 1); // Set FR flag to 1
  preferences.end();

  // Save to preferences for config data NBP
  preferences.begin("dev-config", false);
  /* preferences.putString("admin_password", password);
    preferences.putString("security_answer", securityQuestion);
    preferences.putInt("FR", 1); // Set FR flag to 1 */
  preferences.end();

  // Send success response
  String response = "<html><body style='font-family: Arial, sans-serif; padding: 20px;'>";
  response += "<h1>Admin Password Saved Successfully!</h1>";
  response += "<p>System will restart in 3 seconds...</p>";
  response += "<script>setTimeout(function(){ window.location.href = '/'; }, 3000);</script>";
  response += "</body></html>";

  serverAP.send(200, "text/html", response);

  // Restart to apply changes
  delay(3000);
  // ESP.restart(); //NBP
}

// PrefArea: Apmode config
// Function to get all preference keys and their descriptions
std::vector<PreferenceItem> getAllPreferenceKeys()
{
    std::vector<PreferenceItem> preferences = {
        {"api_endpoint", "BMS SERVER location", "http://***********/StarService/NGV3"}, 
        {"ssid", "WiFi SSID", ""},
        {"password", "WiFi Password", ""},
        {"INOUT", "InOut Mode(0:Show in-out dialog, 1: In, 2: Out)", "0"},
        {"ntpserver", "NTP Server", "time.google.com"},
        {"facemodver", "Face Module Version (2, 5, or 20)", "2"},
        {"verify_thres", "Face Verification Threshold (0-4)", "2"},
        {"livenes_thres", "Face Liveness Check Threshold (0-4)", "2"},
        {"timezone", "Time Zone", "IST-5:30"},
        {"displaymode", "Display Mode", "0"},
        {"doordelay", "Door Open Delay (ms)", "3000"},
        {"alarmdelay", "Alarm Delay (ms)", "10000"},
        {"autolock", "Auto Lock Enable", "1"},
        {"syncinterval", "Data Sync Interval (min)", "30"},
        {"logretention", "Log Retention Days", "30"},
        {"powersave", "Power Save Mode", "0"},
    };
    //Saved in handlesaveconfig();
    return preferences;
  }

  // WiFi Server Task for AP mode
  void wifiServerTask(void *parameter)
  {
    while (1)
    {
      if (WiFi.status() == WL_CONNECTED)
      {
        // Handle WiFi server operations
        serverAP.handleClient();
      }
      vTaskDelay(10 / portTICK_PERIOD_MS);
    }
  }

  // Handler for the root page
  void handleRootAP()
  {
    String html = FPSTR(mainPage);
    html.replace("%VERSION%", version);

    serverAP.send(200, "text/html", html);
  }

  // Add a new handler for factory reset
  void handleFactoryReset()
  {
    serverAP.send(200, "text/html", FPSTR(factoryResetPage));
  }

  // Handler to confirm factory reset
  void handleFactoryResetConfirm()
  {
    String pin = serverAP.arg("pin");
    if (pin == "20201010")
    {
      // Perform factory reset actions
      Serial.println("Performing factory reset...");

      // Delete all files from SPIFFS
      Serial.println("Deleting all files from SPIFFS...");

      File root = SPIFFS.open("/");
      if (!root || !root.isDirectory())
      {
        Serial.println("Failed to open directory");
        serverAP.send(500, "text/html", "Failed to access SPIFFS");
        return;
      }

      std::vector<String> filesToDelete;
      File file = root.openNextFile();

      while (file)
      {
        if (!file.isDirectory())
        {
          String filePath = String(file.path());
          filesToDelete.push_back(filePath);
          Serial.print("Found file to delete: ");
          Serial.println(filePath);
        }
        file = root.openNextFile();
      }
      root.close();

      int filesDeleted = 0;
      int totalFiles = filesToDelete.size();

      for (const String &filePath : filesToDelete)
      {
        Serial.print("Attempting to delete: ");
        Serial.println(filePath);

        File fileToDelete = SPIFFS.open(filePath, "r");
        if (fileToDelete)
        {
          fileToDelete.close();
        }

        if (SPIFFS.remove(filePath))
        {
          filesDeleted++;
          Serial.print("Successfully deleted: ");
          Serial.println(filePath);
        }
        else
        {
          Serial.print("Failed to delete: ");
          Serial.println(filePath);
        }
      }

      Serial.printf("Deleted %d out of %d files\n", filesDeleted, totalFiles);

      if (filesDeleted < totalFiles)
      {
        Serial.println("Attempting to format SPIFFS...");
        if (SPIFFS.format())
        {
          Serial.println("SPIFFS formatted successfully");
          filesDeleted = totalFiles;
        }
        else
        {
          Serial.println("SPIFFS format failed");
        }
      }

      preferences.begin("wifi-config", false);
      preferences.clear();
      preferences.end();

      preferences.begin("admin-creds", false);
      preferences.clear();
      preferences.putInt("FR", 0);
      preferences.end();

      preferences.begin("attlog", false);
      preferences.clear();
      preferences.end();

      preferences.begin("dev-config", false);
      preferences.clear();
      preferences.end(); // NBP

      Serial.println("Preferences cleared and FR flag set to 0.");

      writeEEPROM(0x110, 0);
      writeEEPROM(0x112, 0);
      writeEEPROM(0x108, 0);
      Serial.println("EEPROM values reset.");

      faceScanner.deleteAllUsers(0);
      Serial.println("Face users deleted.");

      String response = "<html><body style='font-family: Arial, sans-serif; padding: 20px;'>";
      response += "<h2>Factory Reset Complete</h2>";
      response += "<p>Files found: " + String(totalFiles) + "</p>";
      response += "<p>Files deleted: " + String(filesDeleted) + "</p>";
      response += "<p>SPIFFS status: " + String(SPIFFS.totalBytes() - SPIFFS.usedBytes()) + " bytes free</p>";
      response += "<p style='color: red;'>System will reboot in 3 seconds...</p>";
      response += "<script>setTimeout(function(){ window.close(); }, 3000);</script>";
      response += "</body></html>";

      serverAP.send(200, "text/html", response);

      delay(3000);
      ESP.restart();
    }
    else
    {
      serverAP.send(200, "text/html", "<html><body><h1>Incorrect PIN!</h2><a href='/factory_reset'>Try Again</a></body></html>");
    }
  }

  // Modify the start_ap_mode() function
  void start_ap_mode()
  {
    preferences.begin("admin-creds", true);
    int FR = preferences.getInt("FR", 0);
    preferences.end();
    Serial.print("GOT FR as:::::::::::");
    Serial.println(FR);

    Preferences prefs;
    prefs.begin("attlog", false);
    Tr_Id = prefs.getULong("Tr_Id", 1); // Default to 1 if not found
    prefs.end();
    Serial.printf("Initialized Tr_Id: %lu\n", Tr_Id);
    // While adding new preferences, there are 3 main areas to add 
    // I have marked it with suffix PrefArea:
    // Also check the original defination of the variable,
    // if it is constant then remove the const keyword from the definition
    // Next check all the usages of the variable in the code



    // PrefArea: On boot
    Log("Loading preferences...");
    INOUT = getPrefInt("wifi-config", "INOUT", 0);
    Log("INOUT Mode: " + String(INOUT));
    ntpServer = getPrefString("wifi-config", "ntpserver", "time.google.com");
    Log("NTP Server: " + ntpServer);
    // Load face module settings
    facemoduleversion = getPrefInt("wifi-config", "facemodver", 2);
    Log("Face Module Version: " + String(facemoduleversion));
    // Load face verification thresholds
    verifyThreshold = getPrefInt("wifi-config", "verify_thres", 2);
    livenessThreshold = getPrefInt("wifi-config", "livenes_thres", 2);
    Log("Verify Threshold: " + String(verifyThreshold));
    Log("Liveness Threshold: " + String(livenessThreshold));



    Log("Loading preferences... done");

    
    uint64_t chipID = ESP.getEfuseMac();
    char chipIDStr[13];
    String baseSSID = "OASYS-NG_";
    snprintf(chipIDStr, sizeof(chipIDStr), "%04X%08X", (uint16_t)(chipID >> 32), (uint32_t)chipID);
    String uniqueSSID = baseSSID + String(chipIDStr).substring(8); // Use last 8 characters

    Log("AP MAC Address: " + uniqueSSID);
    ap_ssid = uniqueSSID; //

    if (FR == 0)
    {
      WiFi.softAP(ap_ssid, ap_password);
      IPAddress myIP = WiFi.softAPIP();
      Serial.print("AP IP address: ");
      Serial.println(myIP);

      serverAP.on("/", []()
                  { serverAP.send(200, "text/html", FPSTR(adminPasswordPage)); });
      serverAP.on("/save_admin_password", handleSaveAdminPassword);
      serverAP.begin();
      return;
    }

    WiFi.softAP(ap_ssid, ap_password);
    IPAddress myIP = WiFi.softAPIP();
    Serial.print("AP IP address: ");
    Serial.println(myIP);

    tft.setCursor(20, 30);
    String ipString = "http://" + myIP.toString();
    tft.print(ipString);

    if (!SPIFFS.begin(true))
    {
      Serial.println("SPIFFS Mount Failed");
      return;
    }

    serverAP.on("/", handleRootAP);
    serverAP.on("/wifi", show_wifi);
    serverAP.on("/save", handleSaveCredentials);
    serverAP.on("/connect", handleConnect);
    serverAP.on("/exit", handleExit);
    serverAP.on("/firmware", handleFirmwareUpdate);
    serverAP.on("/files", handleFiles);
    serverAP.on("/download", handleDownload);
    serverAP.on("/config", handleConfig);
    serverAP.on("/backup", handleConfigBackup);
    serverAP.on(
        "/restore", HTTP_POST, []() {}, handleRestore);
    serverAP.on("/edit_config", handleEditConfig);
    serverAP.on("/save_config", HTTP_POST, handleSaveConfig);
    serverAP.on("/factory_reset", handleFactoryReset);
    serverAP.on("/factory_reset_confirm", handleFactoryResetConfirm);
    serverAP.on("/save_admin_password", handleSaveAdminPassword); // Add admin password handler

    // Update handler
    serverAP.on(
        "/update", HTTP_POST, []()
        {
      serverAP.sendHeader("Connection", "close");
      serverAP.send(200, "text/plain", (Update.hasError()) ? "Update Failed!" : "Update Success!");
      ESP.restart(); },
        handleUpload);

    serverAP.begin();
    setup_ota();
    startMillis = millis();
    lastKeyPressMillis = millis();
  }

  // Modified handler functions
  void show_wifi()
  {
    String html = FPSTR(wifi_page);
    int n = WiFi.scanNetworks();

    for (int i = 0; i < n; ++i)
    {
      html += "<li class='network-item'>";
      html += "<div><strong>" + WiFi.SSID(i) + "</strong>";
      html += "<div class='signal-strength'>Signal: " + String(WiFi.RSSI(i)) + " dBm</div></div>";
      html += "<a href='/connect?ssid=" + WiFi.SSID(i) + "' class='connect-btn'>Connect</a>";
      html += "</li>";
    }

    html += FPSTR(wifi_page_end);
    serverAP.send(200, "text/html", html);
  }

  void handleConnect()
  {
    String ssid = serverAP.arg("ssid");
    String html = FPSTR(PASSWORD_page);
    html.replace("%SSID%", ssid);
    serverAP.send(200, "text/html", html);
  }

  void handleSaveCredentials()
  {
    String ssid = serverAP.arg("ssid");
    String password = serverAP.arg("password");

    preferences.begin("wifi-config", false);
    preferences.putString("ssid", ssid);
    preferences.putString("password", password);
    preferences.end();

    // Directly show the main page after saving credentials
    String html = FPSTR(mainPage);
    html.replace("%VERSION%", version);
    serverAP.send(200, "text/html", html);
  }

  void handleFirmwareUpdate()
  {
    serverAP.send(200, "text/html", FPSTR(updatePage));
  }

  void handleExit()
  {
    serverAP.send(200, "text/html", "<html><body><h2>Exiting Setup Mode...</h2><script>setTimeout(function(){ window.close(); }, 2000);</script></body></html>");

    // Add a small delay to allow the response to be sent
    delay(500);
    tft.init();
    // tft.fillScreen(TFT_BLACK); //NBP
    // Properly terminate AP mode instead of restarting
    terminateAPMode();
  }

  // Handler function definitions
  void handleConfig()
  {
    serverAP.send(200, "text/html", FPSTR(configPage));
  }

  // Modified handleConfigBackup function
  void handleConfigBackup()
  {
    preferences.begin("wifi-config", true);

    String csvContent = "key,description,value\n";
    auto prefItems = getAllPreferenceKeys();

    for (const auto &item : prefItems)
    {
      String value = preferences.getString(item.key.c_str(), item.defaultValue);
      csvContent += item.key + "," + item.description + "," + value + "\n";
    }

    preferences.end();

    serverAP.sendHeader("Content-Disposition", "attachment; filename=config.csv");
    serverAP.send(200, "text/csv", csvContent);
  }

  // Modified handleRestore function
  void handleRestore()
  {
    HTTPUpload &upload = serverAP.upload();

    if (upload.status == UPLOAD_FILE_START)
    {
      Serial.println("Restore: Starting file upload");
    }
    else if (upload.status == UPLOAD_FILE_WRITE)
    {
      String data = String((char *)upload.buf);
      preferences.begin("wifi-config", false);

      int pos = 0;
      bool isFirstLine = true;
      while (pos < data.length())
      {
        int nextLinePos = data.indexOf('\n', pos);
        if (nextLinePos == -1)
          nextLinePos = data.length();

        if (!isFirstLine)
        { // Skip header line
          String line = data.substring(pos, nextLinePos);
          int firstComma = line.indexOf(',');
          int secondComma = line.indexOf(',', firstComma + 1);

          if (firstComma != -1)
          {
            String key = line.substring(0, firstComma);
            String value;

            if (secondComma != -1)
            {
              // If there's a description field, get value after second comma
              value = line.substring(secondComma + 1);
            }
            else
            {
              // If no description field, get value after first comma
              value = line.substring(firstComma + 1);
            }

            // Trim any whitespace or newline characters
            value.trim();

            // Verify key exists in our known preferences before saving
            auto prefItems = getAllPreferenceKeys();
            for (const auto &item : prefItems)
            {
              if (item.key == key)
              {
                preferences.putString(key.c_str(), value);
                Serial.printf("Restored: %s = %s\n", key.c_str(), value.c_str());
                break;
              }
            }
          }
        }
        isFirstLine = false;
        pos = nextLinePos + 1;
      }

      preferences.end();
    }
    else if (upload.status == UPLOAD_FILE_END)
    {
      serverAP.send(200, "text/plain", "Configuration restored successfully!");
    }
  }

  // Modified handleEditConfig function
  void handleEditConfig()
  {
    preferences.begin("wifi-config", true);

    String html = FPSTR(editConfigPage);
    String configItems;

    auto prefItems = getAllPreferenceKeys();

    for (const auto &item : prefItems)
    {
      String value = preferences.getString(item.key.c_str(), item.defaultValue);

      configItems += "<div class='config-item'>";
      configItems += "<label for='" + item.key + "'>" + item.description + "</label>";
      configItems += "<input type='text' id='" + item.key + "' name='" + item.key + "' value='" + value + "' placeholder='" + item.defaultValue + "'>";
      configItems += "</div>";
    }

    preferences.end();

    html.replace("%CONFIG_ITEMS%", configItems);
    serverAP.send(200, "text/html", html);
  }

  // Modified handleSaveConfig function
  void handleSaveConfig()
  {
    preferences.begin("wifi-config", false);

    // Get all known preference keys
    auto prefItems = getAllPreferenceKeys();

    // Save only known preferences
    for (int i = 0; i < serverAP.args(); i++)
    {
      String key = serverAP.argName(i);
      String value = serverAP.arg(i);

      // Verify key exists in our known preferences before saving
      for (const auto &item : prefItems)
      {
        if (item.key == key)
        {
          preferences.putString(key.c_str(), value);
          Serial.printf("Saved: %s = %s\n", key.c_str(), value.c_str());
          break;
        }
      }
    }

    preferences.end();
    // PrefArea: On saving preferences

    INOUT = getPrefInt("wifi-config", "INOUT", 0);
    ntpServer = getPrefString("wifi-config", "ntpserver", "time.google.com");
    // Update face module settings
    facemoduleversion = getPrefInt("wifi-config", "facemodver", 2);
    // Update face verification thresholds
    verifyThreshold = getPrefInt("wifi-config", "verify_thres", 2);
    livenessThreshold = getPrefInt("wifi-config", "livenes_thres", 2);
    // Redirect back to config page
    serverAP.sendHeader("Location", "/config");
    serverAP.send(303);
  }

  // Add this handler for OTA file uploads
  void handleUpload()
  {
    HTTPUpload &upload = serverAP.upload();

    if (upload.status == UPLOAD_FILE_START)
    {
      Serial.printf("Upload: %s\n", upload.filename.c_str());
      // Open the file for writing
      if (!Update.begin(UPDATE_SIZE_UNKNOWN))
      {
        Serial.println("Could not begin update!");
      }
    }
    else if (upload.status == UPLOAD_FILE_WRITE)
    {
      // Write the received bytes to the file
      if (Update.write(upload.buf, upload.currentSize) != upload.currentSize)
      {
        Serial.println("Could not write update!");
      }
    }
    else if (upload.status == UPLOAD_FILE_END)
    {
      // Close the file
      if (Update.end(true))
      {
        Serial.printf("Update Success: %u bytes\n", upload.totalSize);
        tft.print("UPDATE SUCCESS");
        delay(2000);
      }
      else
      {
        Serial.println("Error Occurred. Error #: " + String(Update.getError()));
      }
    }
  }

  void fw_update()
  {
    unsigned long dynamicTimeout = apTimeout; // Start with the default timeout

    startMillis = millis();  // Reset the start time
    dynamicTimeout = 120000; // Set the new timeout duration (e.g., 120 seconds)

    String html = R"(
    <!DOCTYPE html>
    <html>
    <head>
        <title>Firmware Update</title>
        <style>
            body {
                font-family: Arial, sans-serif;
                margin: 0;
                padding: 20px;
                background-color: #f0f2f5;
                text-align: center;
            }
            .container {
                max-width: 500px;
                margin: 0 auto;
                background: white;
                padding: 20px;
                border-radius: 10px;
                box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
            }
            h1 {
                color: #1a73e8;
            }
            .warning {
                color: red;
                margin: 20px 0;
            }
            input[type="file"] {
                margin: 20px 0;
            }
            input[type="submit"] {
                background-color: #4CAF50;
                color: white;
                padding: 10px 20px;
                border: none;
                border-radius: 4px;
                cursor: pointer;
            }
            input[type="submit"]:hover {
                background-color: #45a049;
            }
            .exit-btn {
                background-color: #ff4444;
                color: white;
                padding: 10px 20px;
                border: none;
                border-radius: 4px;
                cursor: pointer;
                margin-top: 20px;
            }
            .exit-btn:hover {
                background-color: #cc0000;
            }
            .countdown {
                margin-top: 20px;
                font-size: 18px;
                color: #333;
            }
        </style>
    </head>
    <body>
        <div class="container">
            <h1>Firmware Update</h1>
            <p class="warning">Warning: Do not interrupt the update process!</p>
            <form method="POST" action="/update" enctype="multipart/form-data">
                <input type="file" name="update" accept=".bin"><br>
                <input type="submit" value="Update Firmware">
            </form>
            <button class="exit-btn" onclick="window.location.href='/exit'">Exit AP Mode</button>
            <p class="countdown" id="countdown">Inactivity Timer: 120 seconds</p>
        </div>
        <script>
            let countdown = 120; // Countdown in seconds
            const countdownElement = document.getElementById('countdown');

            function updateCountdown() {
                countdown--;
                countdownElement.textContent = "Inactivity Timer: " + countdown + " seconds";
                if (countdown <= 0) {
                    window.location.href = "/"; // Redirect to main page
                }
            }

            setInterval(updateCountdown, 1000); // Update countdown every second
        </script>
    </body>
    </html>
    )";

    serverAP.send(200, "text/html", html);
  }

  // OTA Handler Class Definition
  class OTAHandler
  {
  public:
    static WebServer *mainServer;   // Pointer to the main server
    static bool otaActive;          // Flag to indicate OTA mode
    static const char *serverIndex; // HTML page for firmware update

    // Initialize OTA functionality
    static void initOTA(WebServer *server)
    {
      mainServer = server;
      otaActive = false;

      // Handle the `/firmware` endpoint
      mainServer->on("/firmware", HTTP_GET, []()
                     {
      if (!otaActive) {
        activateOTA();
      }
      mainServer->sendHeader("Connection", "close");
      mainServer->send(200, "text/html", serverIndex); });

      // Handle the `/update` endpoint for firmware upload
      mainServer->on(
          "/update", HTTP_POST, []()
          {
        mainServer->sendHeader("Connection", "close");
        mainServer->send(200, "text/plain",
                         (Update.hasError()) ? "Update Failed!" : "Update Success! Restarting...");
        delay(1000);
        ESP.restart(); },
          []()
          {
            HTTPUpload &upload = mainServer->upload();

            if (upload.status == UPLOAD_FILE_START)
            {
              Serial.printf("[OTA] Update: %s\n", upload.filename.c_str());
              if (!Update.begin(UPDATE_SIZE_UNKNOWN))
              {
                Update.printError(Serial);
              }
            }
            else if (upload.status == UPLOAD_FILE_WRITE)
            {
              if (Update.write(upload.buf, upload.currentSize) != upload.currentSize)
              {
                Update.printError(Serial);
              }
            }
            else if (upload.status == UPLOAD_FILE_END)
            {
              if (Update.end(true))
              {
                Serial.printf("[OTA] Success: %u bytes\n", upload.totalSize);
              }
              else
              {
                Update.printError(Serial);
              }
            }
          });
    }

  private:
    // Activate OTA mode
    static void activateOTA()
    {
      Serial.println("[OTA] Firmware update mode activated");
      otaActive = true;
    }
  };

  // Initialize static members
  WebServer *OTAHandler::mainServer = nullptr;
  bool OTAHandler::otaActive = false;
  const char *OTAHandler::serverIndex = R"(
<!DOCTYPE html>
<html>
<head>
    <title>ESP32 Firmware Update</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            text-align: center;
        }
        .container {
            max-width: 500px;
            margin: auto;
            padding: 20px;
            border: 1px solid #ccc;
            border-radius: 5px;
        }
        .warning {
            color: red;
            margin: 20px 0;
        }
        input[type="file"] {
            margin: 20px 0;
        }
        input[type="submit"] {
            background-color: #4CAF50;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
        }
        input[type="submit"]:hover {
            background-color: #45a049;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>ESP32 Firmware Update</h1>
        <p class="warning">Warning: Do not interrupt the update process!</p>
        <form method='POST' action='/update' enctype='multipart/form-data'>
            <input type='file' name='update' accept='.bin'><br>
            <input type='submit' value='Update Firmware'>
        </form>
        <p><a href="/">Back to Home</a></p>
    </div>
</body>
</html>
)";

  // Function to initialize OTA in your AP mode setup
  void setup_ota()
  {
    OTAHandler::initOTA(&serverAP); // Pass the address of serverAP
  }

  bool isSupportedFile(const String &fileName)
  {
    int dotIndex = fileName.lastIndexOf('.');
    if (dotIndex == -1)
      return false;
    String extension = fileName.substring(dotIndex + 1);
    return (extension == "html" || extension == "txt" || extension == "csv");
  }

  String getContentType(String filename)
  {
    if (filename.endsWith(".html") || filename.endsWith(".htm"))
      return "text/html";
    else if (filename.endsWith(".txt"))
      return "text/plain";
    else if (filename.endsWith(".csv"))
      return "text/csv";
    return "application/octet-stream";
  }

  // Function to format file size into human-readable format
  String formatFileSize(size_t bytes)
  {
    if (bytes < 1024)
      return String(bytes) + " B";
    else if (bytes < (1024 * 1024))
      return String(bytes / 1024.0, 2) + " KB";
    else
      return String(bytes / (1024.0 * 1024.0), 2) + " MB";
  }

  // Function to list files with size
  String listFiles()
  {
    String fileList = "";
    File root = SPIFFS.open("/");
    if (!root || !root.isDirectory())
      return "";

    File file = root.openNextFile();
    while (file)
    {
      if (!file.isDirectory())
      {
        String fileName = String(file.name());
        size_t fileSize = file.size();

        // Add file details to the list
        fileList += "<li>";
        fileList += "<strong>" + fileName + "</strong> ";
        fileList += "(" + formatFileSize(fileSize) + ")";
        fileList += " - <a href=\"/download?file=" + fileName + "\">Download</a>";
        fileList += "</li>";
      }
      file = root.openNextFile();
    }
    return fileList;
  }

  // Handler to display the list of files
  void handleFiles()
  {
    String fileList = listFiles();
    String pageContent = FPSTR(filesPage);
    pageContent.replace("%FILE_LIST%", fileList);
    serverAP.send(200, "text/html", pageContent);
  }

  void handleDownload()
  {
    if (!serverAP.hasArg("file"))
    {
      serverAP.send(400, "text/plain", "400: Missing file parameter");
      return;
    }

    // Decode URL-encoded file path and ensure it starts with /
    String filePath = serverAP.arg("file");
    filePath = "/" + filePath;    // Ensure leading slash
    filePath.replace("%20", " "); // Handle spaces in filenames

    Serial.print("Download requested for: ");
    Serial.println(filePath);

    if (!SPIFFS.exists(filePath))
    {
      Serial.print("File not found: ");
      Serial.println(filePath);
      serverAP.send(404, "text/plain", "404: File not found");
      return;
    }

    File file = SPIFFS.open(filePath, "r");
    if (!file)
    {
      Serial.print("Failed to open file: ");
      Serial.println(filePath);
      serverAP.send(500, "text/plain", "500: Could not read file");
      return;
    }

    if (!isSupportedFile(filePath))
    {
      Serial.print("Unsupported file type: ");
      Serial.println(filePath);
      serverAP.send(403, "text/plain", "403: Unsupported file type");
      file.close();
      return;
    }

    String contentType = getContentType(filePath);
    serverAP.sendHeader("Content-Disposition", "attachment; filename=\"" + String(filePath.substring(1)) + "\"");
    serverAP.streamFile(file, contentType);
    file.close();

    Serial.print("Successfully served file: ");
    Serial.println(filePath);
  }

  // Function to check if touch is detected
  bool isTouchDetected()
  {
    if (ts.isTouched) {
		 Serial.print("Touch detected : ");
      for (int i=0; i<ts.touches; i++){
      Serial.print("Touch ");Serial.print(i+1);Serial.print(": ");;
      Serial.print("  x: ");Serial.print(ts.points[i].x);
      Serial.print("  y: ");Serial.print(ts.points[i].y);
      Serial.print("  size: ");Serial.println(ts.points[i].size);
      Serial.println(' ');
    }
        return true;  // Touch is detected
    }
    return false;  // No touch detected
  }

  extern void BeepControlTask(void *parameter);

  // Function to terminate AP Mode
  void terminateAPMode()
  {
    WiFi.softAPdisconnect(true);
    Serial.println("Local hotspot disconnected");
    serverAP.stop();

    ap_mode_done = 1; // to stop AP_MODE
    delay(500);

    // NOW START TASKS

    // Create RTOS task for display updates TFT_eSPI
    xTaskCreatePinnedToCore(displayUpdateTask, "DisplayUpdate", 4096, NULL, 1, NULL, 0);
    delay(200);

    // Initialize TFT_eSPI display for BANDS and default values.
    initDisplay();
    delay(200);

    // Create the Wi-Fi server task
    xTaskCreatePinnedToCore(wifiServerTask, "WiFiServer", 8192, NULL, 1, NULL, 0);
    delay(200);

    // Create Tasks to start webserver
    xTaskCreatePinnedToCore(webServerTask, "WebServerTask", 10000, NULL, 1, NULL, 0);
    delay(200);

    // NBP   xTaskCreatePinnedToCore(TaskUpdateDateTime, "TaskUpdateDateTime", 4096, NULL, 1, NULL, 1);
    xTaskCreatePinnedToCore(TaskHandleCardDetection, "TaskHandleCardDetection", 4096, NULL, 1, NULL, 1);
    delay(200);

    xTaskCreatePinnedToCore(TaskFetchAndSaveCSV, "TaskFetchAndSaveCSV", 8192, NULL, 1, NULL, 0);
    delay(200);

    xTaskCreatePinnedToCore(TaskHandleFaceVerification, "TaskFaceVerify", 4096, NULL, 1, NULL, 1);
    delay(200);

    // Function to start the GPIO task
    xTaskCreatePinnedToCore(GPIOControlTask, "GPIOControl", 2048, NULL, 1, NULL, 0);

    // xTaskCreatePinnedToCore( AttlogTask, "AttlogTask",  4096,  NULL ,1, NULL, 0    );

    // Create network export task
   // xTaskCreatePinnedToCore(TaskNetworkExport, "NetworkExport", 4096, NULL, 1, NULL, 0);
  }

  // vTaskDelay(pdMS_TO_TICKS(100)); // Give time to RTOS tasks???
  // ESP.restart();

  //  }
