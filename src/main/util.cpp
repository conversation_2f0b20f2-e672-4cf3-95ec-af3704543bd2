#include "util.h"

Preferences pref;

String getPrefString(String group, String key, String defaultValue)
{
    pref.begin(group.c_str(), false);
    String val = pref.getString(key.c_str(), defaultValue.c_str());
    pref.end();
    return val;
}
void setPrefString(String group, String key, String value)
{
    pref.begin(group.c_str(), false);
    pref.putString(key.c_str(), value.c_str());
    pref.end();
}

int getPrefInt(String group, String key, int defaultValue)
{
    pref.begin(group.c_str(), false);
    int val = pref.getInt(key.c_str(), defaultValue);
    if (val == defaultValue)
    {
        String valStr = pref.getString(key.c_str(), "__NF__");
        if (valStr == "__NF__")
        {
            return defaultValue;
        }
        val = valStr.toInt();
    }
    pref.end();
    return val;
}
void setPrefInt(String group, String key, int value)
{
    pref.begin(group.c_str(), false);
    pref.putInt(key.c_str(), value);
    pref.end();
}
uint32_t getPrefULong(String group, String key, uint32_t defaultValue)
{
    pref.begin(group.c_str(), false);
    uint32_t val = pref.getULong(key.c_str(), defaultValue);
    pref.end();
    return val;
}
void setPrefULong(String group, String key, uint32_t value)
{
    pref.begin(group.c_str(), false);
    pref.putULong(key.c_str(), value);
    pref.end();
}
bool getPrefBool(String group, String key, bool defaultValue)
{
    pref.begin(group.c_str(), false);
    bool val = pref.getBool(key.c_str(), defaultValue);
    pref.end();
    return val;
}
void setPrefBool(String group, String key, bool value)
{
    pref.begin(group.c_str(), false);
    pref.putBool(key.c_str(), value);
    pref.end();
}
void Log(const String &message)
{
    if (LOG_ENABLED)
        Serial.println(message);
}
