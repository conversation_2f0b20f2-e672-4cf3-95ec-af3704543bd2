#ifndef HTML_TEMPLATES_INO
#define HTML_TEMPLATES_INO

#include "main.h" // Access PROGMEM etc.

// --- HTML/CSS/JS Constants ---

// Definitions (kept here, declarations moved to main.h)
const char *htmlStyle PROGMEM = R"(
<style>
    body {
        font-family: Arial, sans-serif; margin: 0; padding: 20px;
        display: flex; flex-direction: column; align-items: center;
        background-color: #f0f0f0;
    }
    .container {
        background-color: white; padding: 20px; border-radius: 8px;
        box-shadow: 0 2px 4px rgba(0,0,0,0.1); width: 95%;
        max-width: 500px; margin: 10px auto;
    }
    h1 { color: #333; text-align: center; }
    .button {
        display: block; width: 100%; padding: 15px; margin: 20px 0;
        background-color: #4CAF50; color: white; border: none;
        border-radius: 8px; cursor: pointer; font-size: 16px;
        text-align: center; text-decoration: none; /* For <a> tags */
    }
    .back-button { background-color: #ff4444; }
    .timer { text-align: center; margin-top: 10px; color: #666; }
    input[type="text"], input[type="password"], input[type="number"] {
        width: 100%; padding: 8px; margin: 5px 0; box-sizing: border-box;
        border: 1px solid #ddd; border-radius: 4px;
    }
    /* Add other specific styles from original templates if needed */
    .form-container { /* From empIdPage */
        max-width: 400px; margin: 20px auto; padding: 20px; background: white;
        border-radius: 5px; box-shadow: 0 0 10px rgba(0,0,0,0.1);
    }
     h2 { text-align: center; color: #333; } /* From empIdPage etc. */
    .input-group { margin-bottom: 15px; } /* From empIdPage */
    .checkbox-group { margin: 15px 0; display: flex; align-items: center; } /* From empIdPage */
    .checkbox-group input[type="checkbox"] { margin-right: 10px; width: auto; } /* From empIdPage */
    .checkbox-group label { color: #333; font-weight: bold; } /* From empIdPage */
    .save-btn { background: #4CAF50; width:100%; padding: 10px; color:white; border:none; border-radius:4px; cursor:pointer;} /* From empIdPage */
    .save-btn:hover { background: #45a049; } /* From empIdPage */
    .form-back-btn { background: #f44336; margin-top: 10px; width:100%; padding: 10px; color:white; border:none; border-radius:4px; cursor:pointer;} /* From empIdPage */
    .form-back-btn:hover { background: #d32f2f; } /* From empIdPage */
    .error { color: red; font-size: 0.8em; display: none; } /* From empIdPage */
    .hint { color: #666; font-size: 0.8em; margin-top: 2px; } /* From empIdPage */
    .chars-left { color: #666; font-size: 0.8em; float: right; } /* From empIdPage */

    .delete-container { /* From deleteEmployeePage */
        max-width: 400px; margin: 20px auto; padding: 20px; background: white;
        border-radius: 5px; box-shadow: 0 0 10px rgba(0,0,0,0.1);
    }
    .delete-btn, .delete-all-btn { /* From deleteEmployeePage */
        background: #dc3545; color: white; width:100%; padding: 12px; border:none; border-radius:4px; cursor:pointer; font-size: 16px;
    }
    .delete-btn:hover, .delete-all-btn:hover { background: #c82333; } /* From deleteEmployeePage */
    .delete-back-btn { /* From deleteEmployeePage */
         background: #6c757d; color: white; width:100%; padding: 12px; border:none; border-radius:4px; cursor:pointer; font-size: 16px;
    }
    .delete-back-btn:hover { background: #5a6268; } /* From deleteEmployeePage */
    .btn-group { display: flex; flex-direction: column; gap: 10px; } /* From deleteEmployeePage */

     .loader { /* From delete confirmation pages */
        border: 5px solid #f3f3f3; border-radius: 50%;
        border-top: 5px solid #dc3545; width: 50px; height: 50px;
        animation: spin 1s linear infinite; margin: 20px auto;
    }
    @keyframes spin { 0% { transform: rotate(0deg); } 100% { transform: rotate(360deg); } }

    .csv-container { /* From csvDisplayPage */
         max-width: 95%; margin: 20px auto; padding: 20px; background: white;
         border-radius: 5px; box-shadow: 0 0 10px rgba(0,0,0,0.1); overflow-x: auto;
    }
    .csv-data { /* From csvDisplayPage */
        font-family: 'Courier New', monospace; white-space: pre; overflow-x: auto;
    }
    .csv-back-btn { /* From csvDisplayPage */
        display: block; width: 100%; padding: 10px; margin: 20px 0; background: #f44336;
        color: white; border: none; border-radius: 4px; cursor: pointer;
        text-align: center; text-decoration: none;
    }
    table { width: 100%; border-collapse: collapse; margin-bottom: 20px; } /* From report tables */
    th, td { padding: 12px; text-align: left; border-bottom: 1px solid #ddd; } /* From report tables */
    th { background-color: #f5f5f5; } /* From report tables */
    tr:hover { background-color: #f9f9f9; } /* From report tables */

@media screen and (max-width: 480px) {
    .button { padding: 20px; margin: 25px 0; font-size: 20px; }
    .container { width: 90%; padding: 15px; }
}
</style>
)";

const char *timerScript PROGMEM = R"(
<script>
    var timeLeft = 60; // Session timeout in seconds
    var timerInterval;
    function updateTimer() {
        var timerElement = document.getElementById('timer');
        if (timerElement) {
            timerElement.innerHTML = 'Time remaining: ' + timeLeft + ' seconds';
            if(timeLeft <= 0) {
                 clearInterval(timerInterval); // Stop timer
                 window.location.href = '/logout';
            } else {
                timeLeft--;
            }
        } else {
             clearInterval(timerInterval); // Stop timer if element not found
        }
    }
     function startTimer() {
         if (document.getElementById('timer')) { // Only start if timer element exists
             timeLeft = 60; // Reset time on page load/action
             clearInterval(timerInterval); // Clear previous interval if any
             updateTimer(); // Update immediately
             timerInterval = setInterval(updateTimer, 1000); // Start new interval
         }
     }
    // Start timer when the page loads
    window.addEventListener('load', startTimer);
    // Optionally reset timer on user activity (e.g., clicks)
    // document.addEventListener('click', function() { timeLeft = 60; });
</script>
)";

// --- HTML Templates for Unique Pages ---

const char empIdPage[] PROGMEM = R"rawliteral(
<!DOCTYPE html><html><head><title>Employee Details</title>
<meta name="viewport" content="width=device-width,initial-scale=1">
%STYLE%
<script>
function validateEmpId(input) {
    input.value = Math.min(999999, Math.max(1, input.value.slice(0, 6)));
    document.getElementById('empIdError').style.display = (input.value >= 1 && input.value <= 999999) ? 'none' : 'block';
}
function validateName(input) {
    input.value = input.value.replace(/[^A-Za-z\s]/g, '').replace(/\s+/g, ' ').slice(0, 16);
    document.getElementById('charsLeft').textContent = 'Chars left: ' + (16 - input.value.length);
    document.getElementById('nameError').style.display = /^[A-Za-z][A-Za-z\s]*$/.test(input.value) ? 'none' : 'block';
}
function validateForm() {
    var empId = document.getElementById('empid').value;
    var empName = document.getElementById('empname').value.trim();
    if(empId < 1 || empId > 999999) {
        document.getElementById('empIdError').style.display = 'block';
        return false;
    }
    if (!empName || !/^[A-Za-z][A-Za-z\s]*$/.test(empName) || empName.length > 16) {
        document.getElementById('nameError').style.display = 'block';
        return false;
    }
    return true;
}
</script>
%TIMER_SCRIPT%
</head>
<body>
<div class="form-container">
  <h2>Employee Details</h2>
  <form action="/saveEmpDetails" method="POST" onsubmit="return validateForm()">
    <div class="input-group">
      <label for="empid">EMP_ID:</label>
      <input type="number" id="empid" name="empid" required min="1" max="999999" onkeyup="validateEmpId(this)">
      <div class="hint">Enter up to 6 digits (1-999999)</div>
      <span id="empIdError" class="error">Invalid EMP_ID</span>
    </div>
    <div class="input-group">
      <label for="empname">Name:</label>
      <input type="text" id="empname" name="empname" required onkeyup="validateName(this)" maxlength="16"> <!-- Corrected maxlength -->
      <div class="hint">Letters and spaces only (Max 16 chars)</div>
      <span id="nameError" class="error">Invalid name</span>
      <span id="charsLeft" class="chars-left">Chars left: 16</span>
    </div>
    <div class="checkbox-group">
      <input type="checkbox" id="is_admin" name="is_admin">
      <label for="is_admin">Admin</label>
    </div>
    <button type="submit" class="save-btn">SAVE</button>
  </form>
  <button type="button" onclick="location.href='/menu1'" class="form-back-btn">Back</button>
  <div id="timer"></div>
</div>
</body></html>
)rawliteral";

const char deleteEmployeePage[] PROGMEM = R"rawliteral(
<!DOCTYPE HTML><html>
<head>
    <title>Delete Employee</title>
    <meta name="viewport" content="width=device-width, initial-scale=1">
    %STYLE%
    %TIMER_SCRIPT%
    <script>
        function validateInput(input) {
            input.value = input.value.replace(/[^0-9]/g, '');
            if(input.value.length > 6) { input.value = input.value.slice(0, 6); }
            document.getElementById('errorMsg').style.display =
                (input.value.length > 0 && input.value > 0) ? 'none' : 'block';
        }
        function confirmDelete(all = false) {
            let idInput = document.getElementById('empid');
            if(!all && (!idInput.value || idInput.value <= 0)) {
                document.getElementById('errorMsg').style.display = 'block'; return;
            }
            let message = all ? "DELETE ALL %TYPE% employees? This cannot be undone!" : "Delete %TYPE% employee ID: " + idInput.value + "?";
            if(confirm(message)) {
                if(all) { window.location.href = '/deleteAllEmployees?type=%TYPE%'; }
                else { window.location.href = '/deleteEmployee?type=%TYPE%&id=' + idInput.value; }
            }
        }
    </script>
</head>
<body>
    <div class="delete-container">
        <h2>Delete %TYPE% Employee</h2>
        <div class="input-group">
            <label for="empid">ID_No:</label>
            <input type="number" id="empid" name="empid" min="1" max="999999"
                   oninput="validateInput(this)" placeholder="Enter employee ID">
            <span id="errorMsg" class="error">Please enter a valid employee ID</span>
        </div>
        <div class="btn-group">
            <button class="delete-btn" onclick="confirmDelete(false)">Delete Employee</button>
            <button class="delete-all-btn" onclick="confirmDelete(true)">DELETE ALL</button>
            <button class="delete-back-btn" onclick="location.href='/M1B2'">Back</button> <!-- Back to Delete Menu -->
        </div>
        <div id="timer"></div>
    </div>
</body>
</html>
)rawliteral";

// Add other HTML templates (like CSV display if dynamic, confirmation pages etc.) here

#endif // HTML_TEMPLATES_INO
