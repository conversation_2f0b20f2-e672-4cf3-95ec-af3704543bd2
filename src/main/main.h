#ifndef MAIN_H
#define MAIN_H

// TFT_eSPI ver 1.0
// ADDED SWITCH IN GPIO task
extern String version = "NBP_2.0.91_S3_FACE_GT";
//extern String version = "2.0.91_S3_FACE";
// ORIG  version = "AI-3.9_8_PW_SEC_SW4_TRY";
// ALSO ON GIT AS AI_PROD
// ADDED facescanner.reset for proper deletion
//  Added constants
//optimised facescanner.cpp to execute 0x60,0x61

const int MAX_RETRY_ATTEMPTS = 3; // Max retries for operations

// Added Motion Sensor
// Webserver ino optimised
// Modified REPORTS options
//  edited response in cpp from 2 to 3 for enroll success,also modified for response of 0x60
// Added 2 new options in REPORTS
// BROKEN WEBPAGES.. TO TEST
// DOOR LOCK ..TO TEST
// FR=0/1 to test
// CARD CSN,LUT and CARD display added.. To test proper
// OTA UPDATE WORKS ONLY FOR wrover Kit, 16MB....

// Added Device to transaction like face,palm etc...
//  Made single file attendlogs.csv for export with Tr_Id
//  IN OUT to show on unit
//  SHow card during enroll card
// NETWORK export JSON
// CSN delete from MASTER and refresh LUT
// export to webservice. To alter device ID

// TO DO :

// LIC
// DASHBOARD
// TEMPLATE TRANSFER
// ETHERNET
// LAN-WIFI flag
// ESPAsyncweb

// Common includes
#include <Arduino.h>
#include <Wire.h>
#include <SPI.h>
#include <WiFi.h>
#include <WebServer.h>
#include <Ethernet.h>
#include <EthernetUdp.h>
#include <MFRC522.h>
#include <RTClib.h>
#include <TFT_eSPI.h>
#include <Fonts/GFXFF/gfxfont.h>

#include <EEPROM.h>
#include <Dns.h>
#include <SPIFFS.h>
#include "Lan_images.h"
#include <time.h>
#include "FaceScanner.h"
#include "Preferences.h"
#include <Update.h>
#include <TAMC_GT911.h>
#include <SD.h>
#include "FS.h"
#include "PCF8574.h"
#include <vector>
#include "util.h"

// main.h
#include <freertos/semphr.h>
extern SemaphoreHandle_t arrayMutex; // Declaration

// Add these prototypes
void TaskHandleFaceVerification(void *pvParameters);
void TaskHandleCardDetection(void *parameter);
void TaskUpdateDateTime(void *parameter);
void TaskFetchAndSaveCSV(void *parameter);
void TaskNetworkExport(void *parameter);
void webServerTask(void *parameter);
void GPIOControlTask(void *parameter);
void wifiServerTask(void *parameter);

// Pin definitions
#define TFT_CS 5
#define TFT_RST -1
#define TFT_DC  

#define RFID_SS  47
#define RFID_RST 21
#define W5500_CS 14

//GT911
#define TOUCH_SDA  8
#define TOUCH_SCL  9
#define TOUCH_INT 46
#define TOUCH_RST -1
#define TOUCH_WIDTH  320
#define TOUCH_HEIGHT 480



// Display dimensions
#define TFT_WIDTH 320
#define TFT_HEIGHT 480

// Display band heights (as fractions of total height)
#define BAND1_HEIGHT (TFT_HEIGHT * 0.10) // BLUE
#define BAND2_HEIGHT (TFT_HEIGHT * 0.20) // RED
#define BAND3_HEIGHT (TFT_HEIGHT * 0.50) // WHITE
#define BAND4_HEIGHT (TFT_HEIGHT * 0.08) // YELLOW
#define BAND5_HEIGHT (TFT_HEIGHT * 0.06) // GREEN
#define BAND6_HEIGHT (TFT_HEIGHT * 0.06) // GREEN

// Display band positions (Y coordinates)
#define BAND1_Y 0
#define BAND2_Y (BAND1_Y + BAND1_HEIGHT)
#define BAND3_Y (BAND2_Y + BAND2_HEIGHT)
#define BAND4_Y (BAND3_Y + BAND3_HEIGHT)
#define BAND5_Y (BAND4_Y + BAND4_HEIGHT)
#define BAND6_Y (BAND5_Y + BAND5_HEIGHT)

// Band colors
#define BAND1_COLOR TFT_BLUE
#define BAND2_COLOR TFT_MAGENTA
#define BAND3_COLOR TFT_BLACK
#define BAND4_COLOR TFT_YELLOW
#define BAND5_COLOR TFT_GREEN
#define BAND6_COLOR TFT_ORANGE

// Text colors
#define TEXT_COLOR_DARK TFT_BLACK
#define TEXT_COLOR_LIGHT TFT_WHITE

// Text arrays for display
char array_line1[24];   // Date: "day, DD-MMMM-YYYY"
char array_line2[16];   // Time: "HH: mm"
char array_line3_1[16]; // "CARD/FINGER"
char array_line3_2[16]; // "FACE"
char array_line3_3[16]; // "*****"
char array_line4[32];   // "Reboot", "Pending"/"Total", "TID"
char array_line5[32];   // "V: value"
char array_line6[32];   // "IP: value"

// EEPROM settings
#define EEPROM_ADDRESS 0x50
// Function declarations
void Pragati_Init_gpio(void);
void START_BEEP(float seconds);
void ERROR_BEEP(float seconds, int times);
void gpio_task(void *pvParameters);
void GPIOControlTask(void *parameter);

// GPIO definitions
#define OK_LED P0
#define ERR_LED P1
#define BEEP P2
#define RELAY P3
#define SWITCH P4
#define EXT_INPUT P6 // default high
#define RELAY2 P7

extern PCF8574 pcf;
extern int beep;
extern bool cardScanned; // Flag to indicate card was scanned during enrollment

// Define the ButtonInfo struct for webserver.ino
struct ButtonInfo
{
  const char *text;
  const char *url;
};

// for new EMPMST
struct EmployeeRecord
{
  char empId[7];     // 6 chars + null terminator
  char userName[17]; // 20 chars + null terminator
  int faceId;        // Face ID number
  int palmId;        // Palm ID number
  int fingerId;      // Finger ID number
  char cardCsn[9];   // 8 chars + null terminator
  char CardCSN[9];   // Uppercase version used in some files
};

// Function declarations
void updateEnrollmentRecord(int enrollType, int id, const char *csn = nullptr);
bool updateMasterRecord(const EmployeeRecord &record, bool isNewRecord);

// For EA100 Motion sensor
#define MOTION_SENSOR_PIN 39 // GPIO36

// Common variables
extern int ap_mode_done;
extern bool ntpSuccess;
extern String version;
extern uint16_t value;
extern uint8_t wifi_mac[6];
extern uint16_t TID;
extern uint16_t Reboot;
// extern uint16_t Pending;
// extern uint16_t Total;
extern int firsttime;
String sta_ssid;
String sta_password;

// Common objects
extern TFT_eSPI tft;
extern MFRC522 mfrc522;
extern RTC_DS3231 rtc;
extern WebServer serverAP;
extern FaceScanner faceScanner;
extern SemaphoreHandle_t displayMutex;

// Function declarations
void writeEEPROM(uint16_t location, uint16_t value);
uint16_t readEEPROM(uint16_t location);
void start_ap_mode();
// void setupDefaultDisplay();
void draw_logo();
void setupServerRoutes();
uint16_t printMacAddress(uint8_t mac[6]);

// Add to main.h
extern const char mainPage[] PROGMEM;
extern const char wifi_page[] PROGMEM;
extern const char wifi_page_end[] PROGMEM;
extern const char PASSWORD_page[] PROGMEM;
extern const char updatePage[] PROGMEM;
extern Preferences preferences;
extern uint8_t INOUT; // Global INOUT mode (0=select, 1=IN only, 2=OUT only)
uint8_t INOUT = 0;    // Initialize INOUT mode to 0 (select)

// HTML Template declarations
extern const char *htmlStyle PROGMEM;
extern const char *timerScript PROGMEM;
extern const char empIdPage[] PROGMEM;
extern const char deleteEmployeePage[] PROGMEM;

// Initialize global variables
extern int ap_mode_done = 0;
bool ntpSuccess = false;
uint16_t value = 0;
uint8_t wifi_mac[6];
uint16_t TID;
uint16_t Reboot;
// uint16_t Pending;
// uint16_t Total;
int firsttime = 1;

// Initialize objects
TFT_eSPI tft = TFT_eSPI();
MFRC522 mfrc522(RFID_SS, RFID_RST);
RTC_DS3231 rtc;
WebServer serverAP(80);
FaceScanner faceScanner;
SemaphoreHandle_t displayMutex = NULL;
PCF8574 pcf(0x20); // I2C address for PCF8574

// MFRC522 mfrc522(SS, RST_PIN);
//FT6236 ts = FT6236();

//========================== FOR TESTING FROM WEBSERVER ino

// Add extern declaration for newFileReceived
extern bool newFileReceived;

// Global Variables
extern int EMP_ID = 0;
extern String EMP_NAME = "";
extern int face = 0;            // AUTO IDENTIFY FACE/PALM
extern String face_string = ""; // To store combined EMP_ID + EMP_NAME

// Add these global flag variables at the beginning with other global variables
extern bool flag_enroll = false;
extern bool flag_delete = false;
extern bool flag_list = false;
extern bool flag_enroll_face = false;
extern bool flag_enroll_palm = false;
extern bool WEBMIS = true;        // Flag to control additional logging to attlogs.csv
extern bool FR = true;            // Flag to control Tr_Id reset behavior
extern uint32_t Tr_Id = 0;        // Transaction ID counter for attlogs.csv
extern String enroll_val = "   "; // for face enroll 1 st #, for Palm enroll 2nd# and for finger enroll 3rd # ..NBP
extern int is_admin = 0;
extern uint8_t fmt = 0;

// Add these global variables at the top with other global variables
extern bool commandExecuted = false; // Flag to prevent command debounce

// Add these declarations to your main.h file
extern String currentEmpId;
extern String currentEmpName;
extern bool isValidEmployee;
extern char CardCSN[9]; // For card CSN storage

// NTP Settings
String ntpServer = "time.google.com";
const int NTP_PACKET_SIZE = 48;
byte packetBuffer[NTP_PACKET_SIZE];
WiFiUDP Udp;
unsigned int localPort = 8888;

// Global Objects
extern WebServer server(80);

// Login Credentials
const char *valid_username = "admin";
const char *valid_password = "admin";

// Global Variables for Task Communication
bool isLoggedIn = false;
int currentPage = 0; // 0: login, 1-3: menu pages
unsigned long lastActivityTime = 0;
const unsigned long TIMEOUT_DURATION = 60000; // 60 seconds in milliseconds
extern int faceIdForDeletion = 0;             // Stores faceId for deletion

// Enrollment record handling functions
bool writeEmployeeRecord(const EmployeeRecord &record);
void handleEnrollmentRecord(uint16_t userId, const String &empId, const String &userName,
                            bool isAdmin, const char *type, const char *cardCSN = "");

struct DisplayLine
{
  char *text;
  uint16_t startX;
  uint16_t startY;
  bool needsUpdate;
  size_t maxLength;
};

// --- Function Prototypes (Declare functions defined in .ino files) ---
// Core Web Server
void webServerTask(void *parameter);
void timeoutTask(void *parameter);
void setupServerRoutes();
void handleRoot();
void handleLogin();
void handleLogout();
void handleNotFound();

// Menu Handlers
void handleMenu1();
void handleMenu2();
void handleDeleteMenu();
void handleReportsMenu();
void handleMenu3(); // If used

// Enrollment
void handleEmployeeDetails();
void handleSaveEmpDetails();

// Deletion
void handleDeleteFace();
void handleDeletePalm();
void handleDeleteEmployee();
void handleDeleteAllEmployees();

// Reports
void handleViewMaster();
void handleReportDateSelection(const char *reportTitle, const char *actionUrl);
void handleViewAttendance();
void handleViewTimesheet();
String calculateWorkHours(const String &timeIn, const String &timeOut);

// Command Handling
void handleCommand(String cmd);

// Utilities / Helpers (Declare if called across files)
void connectWiFi();
uint16_t printMacAddress(uint8_t mac[6]);
bool loadWiFiCredentials();
void sendMenuPage(const char *title, const ButtonInfo buttons[], size_t buttonCount, const char *backUrl);
bool initSPIFFS(); // If called from multiple places
bool updateMasterRecord(const EmployeeRecord &record, bool isNewRecord);
void updateEnrollmentRecord(int enrollType, int id, const char *csn);
String generateHtmlHeader(const char *title);
String generateHtmlFooter(const char *backUrl);
void resetCommandState();
// Add prototypes for writetotft, START_BEEP etc. if called from webserver files

void START_RELAY1(int duration);

uint8_t verifyThreshold;
uint8_t livenessThreshold;
// Face module version (2, 5, or 20)
// Default: 2 (standard version)
uint8_t facemoduleversion;
#endif
