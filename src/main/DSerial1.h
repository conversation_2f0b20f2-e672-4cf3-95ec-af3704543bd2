#ifndef DSERIAL1_H
#define DSERIAL1_H

#include <Arduino.h>

class DSerial1Class {
public:
    // Public methods that wrap Serial1
    void begin(unsigned long baud, uint32_t config = SERIAL_8N1, int8_t rxPin = -1, int8_t txPin = -1, bool invert = false) {
        log("begin", "Baud: " + String(baud) + ", Config: " + String(config, HEX) + 
            ", RX: " + String(rxPin) + ", TX: " + String(txPin), false);
        Serial1.begin(baud, config, rxPin, txPin, invert);
    }

    int available() {
        int avail = Serial1.available();
        //log("available", String(avail));
        return avail;
    }

    int read() {
        int data = Serial1.read();
        if (data != -1) {
            String dataStr = String(data, HEX);
            //pad to 2 characters
            if(dataStr.length() == 1)
            {
                dataStr = "0" + dataStr;
            }
            log("read", dataStr, true);
        } else {
            log("read", "No data available", true);
        }
        return data;
    }

    size_t write(uint8_t b) {
        String dataStr = String(b, HEX);
        //pad to 2 characters
        if(dataStr.length() == 1)
        {
            dataStr = "0" + dataStr;
        }
        log("write(byte)", dataStr, true);
        return Serial1.write(b);
    }

    size_t write(const uint8_t *buffer, size_t size) {
        String data = "[";
        for (size_t i = 0; i < size && i < 16; i++) {  // Limit to first 16 bytes for logging
            if (i > 0) data += " ";
            if (buffer[i] < 0x10) data += "0";
            data += String(buffer[i], HEX);
        }
        if (size > 16) data += "...";
        data += "]";
        log("write(buffer)", "Size: " + String(size) + ", Data: " + data);
        return Serial1.write(buffer, size);
    }
    
    size_t readBytes(uint8_t* buffer, size_t length) {
        size_t count = 0;
        uint32_t startTime = millis();
        const uint32_t timeout = 1000; // 1 second timeout
        
    log("readBytes", "Requested " + String(length) + " bytes");
        
        while (count < length) {
            if (available()) {
                int c = read();
                if (c >= 0) {
                    buffer[count++] = (uint8_t)c;
                }
            }
            
            if (millis() - startTime > timeout) {
                log("readBytes", "Timeout after " + String(count) + " bytes");
                break;
            }
        }
        
        log("readBytes", "Read " + String(count) + " bytes");
        return count;
    }

    void flush() {
        log("flush", "");
        Serial1.flush();
    }

    operator bool() {
        bool result = static_cast<bool>(Serial1);
        log("operator bool", result ? "true" : "false");
        return result;
    }

    void enableLogging(bool enable) {
        loggingEnabled = enable;
        log("Logging", enable ? "Enabled" : "Disabled");
    }

private:
    bool loggingEnabled = false;
    String lastFunction = "";
    String messageBuffer = "";
    


    void log(const String& function, const String& message, bool buffer = false) {
        if (loggingEnabled) {
            if (lastFunction == function && buffer) {
                //Serial.print(message);
                messageBuffer += " "+ message;
            }
            else {
                if(messageBuffer.length() > 0)
                {
                    Serial.println();
                    Serial.print("[DSerial1::");
                    Serial.print(lastFunction);
                    Serial.print("] ");
                    Serial.println(messageBuffer);
                    messageBuffer = "";
                }
                if(!buffer)
                {
                    Serial.println();
                    Serial.print("[DSerial1::");
                    Serial.print(function);
                    Serial.print("] ");
                    Serial.println(message);
                }
                lastFunction = function;
            }

        }
    }
};

// Global instance
extern DSerial1Class DSerial1;

#endif // DSERIAL1_H
