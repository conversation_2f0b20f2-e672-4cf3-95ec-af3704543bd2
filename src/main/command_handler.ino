// command_handler.ino
#include "main.h"

// --- Command Handler ---
void handleCommand(String cmd)
{
  if (!isLoggedIn)
  {
    server.sendHeader("Location", "/");
    server.send(302);
    return;
  }
  lastActivityTime = millis(); // Reset timer on any command interaction

  Serial.print("Web Command received: ");
  Serial.println(cmd);

  // --- Menu 1 Commands ---
  if (cmd == "M1B1")
  { // ENROLL (Entry Point)
    Serial.println("Redirecting to Employee Details page for Enrollment...");
    flag_enroll = false; // Ensure enroll flag is false before starting
    face = 0;            // Reset scanner operation state
    server.sendHeader("Location", "/employeeDetails");
    server.send(302, "text/plain", "Redirecting to Employee Details...");
  }
  // M1B2 (DELETE) -> Handled by setupServerRoutes pointing to handleDeleteMenu
  // M1B4 (REPORTS) -> Handled by setupServerRoutes pointing to handleReportsMenu

  // --- Menu 2 Commands (Enroll Types) ---
  // These commands are expected AFTER handleSaveEmpDetails has set flag_enroll=true
  else if (cmd == "M2B1")
  { // FACE Enroll Type Selected
    if (flag_enroll)
    {
      Serial.println("Setting up for FACE enrollment (ITG)...");
      face = 6; // Signal face enroll (ITG) for background task
      // face_string is already set in handleSaveEmpDetails
      server.sendHeader("Location", "/menu2?status=enroll_face"); // Go back to Enroll Menu with status
      server.send(302, "text/plain", "Initiating Face Enrollment... Watch device screen.");
      // Background task (TaskHandleFaceVerification) should detect face=6 and perform scanner actions
    }
    else
    {
      goto EnrollError;
    }
  }
  else if (cmd == "M2B2")
  { // PALM Enroll Type Selected
    if (flag_enroll)
    {
      Serial.println("Setting up for PALM enrollment (ITG)...");
      face = 8; // Signal palm enroll (ITG) for background task
      // face_string is already set
      server.sendHeader("Location", "/menu2?status=enroll_palm");
      server.send(302, "text/plain", "Initiating Palm Enrollment... Watch device screen.");
    }
    else
    {
      goto EnrollError;
    }
  }
  else if (cmd == "M2B3")
  { // FINGER Enroll Type Selected
    if (flag_enroll)
    {
      Serial.println("Setting up for FINGER enrollment...");
      face = 7; // Signal finger enroll for background task
      // face_string is already set
      server.sendHeader("Location", "/menu2?status=enroll_finger");
      server.send(302, "text/plain", "Initiating Finger Enrollment... (Not Implemented)"); // Update message if implemented
    }
    else
    {
      goto EnrollError;
    }
  }
  else if (cmd == "M2B4")
  { // CARD Enroll Type Selected
    if (flag_enroll)
    {
      Serial.println("Setting up for CARD enrollment...");
      face = 11; // Signal card enroll (background task waits for scan)
      // face_string is already set
      server.sendHeader("Location", "/menu2?status=enroll_card");
      server.send(302, "text/plain", "Present Card for Enrollment...");
      // Background task (TaskHandleCardDetection) waits for cardScanned=true when face==11
    }
    else
    {
      goto EnrollError;
    }
  }

  // --- Fallback for Unhandled Commands ---
  else
  {
    Serial.print("Unhandled command: ");
    Serial.println(cmd);
    server.sendHeader("Location", "/menu1"); // Go back to main menu
    server.send(302, "text/plain", "Unknown command");
  }
  return; // Exit handler

EnrollError: // Label for common enrollment error handling
  Serial.println("Error: Enroll type command received, but employee details not saved first (flag_enroll=false).");
  server.sendHeader("Location", "/menu1"); // Go back to main menu
  server.send(302, "text/plain", "Error: Please enter and save employee details first.");
}
