#include <main.h>
// Constants
#define MAX_EMPLOYEES 200
#define CSV_BUFFER_SIZE 256

// Structure for LUT
struct LutEntry
{
  uint16_t srNo;
  uint16_t empId;
  char cardCsn[9];  // used in Face Detection (8 chars + null terminator)
  char empName[32]; // Used for UserName field
};

// Global variables (MFRC522 defined in main.h)
LutEntry *lutTable = NULL; // Will be allocated in PSRAM
int lutCount = 0;
bool newFileReceived = false; // Flag to indicate new file received from web
String currentEmpId = "";
String currentEmpName = "";
bool isValidEmployee = false;
String currentCardCSN = ""; // Global string to store the latest card CSN read

// Function to show flash size in human readable format
void showFlashSize()
{
  uint32_t flashSize = ESP.getFlashChipSize();
  float size;
  const char *unit;

  if (flashSize >= 1024 * 1024)
  {
    size = flashSize / (1024.0 * 1024.0);
    unit = "MB";
  }
  else if (flashSize >= 1024)
  {
    size = flashSize / 1024.0;
    unit = "KB";
  }
  else
  {
    size = flashSize;
    unit = "B";
  }

  Serial.printf("Internal Flash Size: %.2f %s\n", size, unit);
}

// Function to display file contents
void displayFileContents(const char *filename)
{
  File file = SPIFFS.open(filename, "r");
  if (!file)
  {
    Serial.printf("Failed to open %s\n", filename);
    return;
  }

  Serial.printf("\nContents of %s:\n", filename);
  while (file.available())
  {
    String line = file.readStringUntil('\n');
    Serial.println(line);
  }
  Serial.println();

  file.close();
}

// Function to create LUT in PSRAM
bool createLUTInPSRAM()
{
  if (lutTable != NULL)
  {
    free(lutTable);
    lutTable = NULL;
  }

  lutTable = (LutEntry *)ps_malloc(MAX_EMPLOYEES * sizeof(LutEntry));
  if (lutTable == NULL)
  {
    Serial.println("Failed to allocate PSRAM for LUT");
    return false;
  }

  lutCount = 0;
  File file = SPIFFS.open("/MASTER.csv", "r");
  if (!file)
  {
    Serial.println("Failed to open MASTER.csv");
    return false;
  }

  // Skip header
  String header = file.readStringUntil('\n');

  char buffer[CSV_BUFFER_SIZE];
  int idx = 0;

  while (file.available() && lutCount < MAX_EMPLOYEES)
  {
    char c = file.read();
    if (c == '\n' || !file.available())
    {
      buffer[idx] = '\0';

      char *ptr = buffer;
      // First field is EmpId
      lutTable[lutCount].empId = atoi(ptr);

      // Skip to UserName (1st comma)
      while (*ptr != ',' && *ptr)
        ptr++;
      ptr++;

      // Extract UserName until next comma
      int name_idx = 0;
      while (*ptr != ',' && *ptr && name_idx < 31)
      {
        lutTable[lutCount].empName[name_idx++] = *ptr++;
      }
      lutTable[lutCount].empName[name_idx] = '\0';

      // Skip to FaceId (2nd comma)
      while (*ptr != ',' && *ptr)
        ptr++;
      ptr++;

      // Skip to PalmId (3rd comma)
      while (*ptr != ',' && *ptr)
        ptr++;
      ptr++;

      // Skip to FingerId (4th comma)
      while (*ptr != ',' && *ptr)
        ptr++;
      ptr++;

      // Skip to CardCSN (5th comma)
      while (*ptr != ',' && *ptr)
        ptr++;
      if (*ptr == ',')
        ptr++; // Move past the 5th comma

      // Extract CSN until end of line (not comma)
      int csn_idx = 0;
      while (*ptr && *ptr != '\n' && *ptr != '\r' && csn_idx < 8)
      {
        lutTable[lutCount].cardCsn[csn_idx++] = *ptr++;
      }
      lutTable[lutCount].cardCsn[csn_idx] = '\0';

      // Debug output
      Serial.printf("Loaded LUT entry %d: EmpId=%d, UserName='%s', CSN='%s'\n",
                    lutCount, lutTable[lutCount].empId, lutTable[lutCount].empName, lutTable[lutCount].cardCsn);

      lutCount++;
      idx = 0;
    }
    else if (idx < CSV_BUFFER_SIZE - 1)
    {
      buffer[idx++] = c;
    }
  }

  file.close();
  Serial.printf("LUT created in PSRAM with %d entries\n", lutCount);
  newFileReceived = false; // Reset the flag after creating LUT
  return true;
}

// Function to find employee by Card CSN
void findEmployeeByCard(const char *cardCsn)
{
  if (lutTable == NULL)
  {
    Serial.println("LUT not initialized");
    return;
  }

  Serial.printf("Looking for card CSN: '%s'\n", cardCsn);
  for (int i = 0; i < lutCount; i++)
  {
    Serial.printf("Comparing with LUT entry %d: CSN='%s'\n", i, lutTable[i].cardCsn);
    if (strcmp(lutTable[i].cardCsn, cardCsn) == 0)
    {
      Serial.printf("Found Employee ID: %d, Name: %s\n", lutTable[i].empId, lutTable[i].empName);
      // Update global employee information
      currentEmpId = String(lutTable[i].empId);
      currentEmpName = String(lutTable[i].empName);
      Serial.println("verified in LUT successfully");
      writetotft(31, currentEmpId.c_str());
      writetotft(32, currentEmpName.c_str());
      writetotft(33, "Card");
      START_BEEP(1.0f);
      delay(1000);
      isValidEmployee = true;
      showMenuScreen("card");
      // face=0;
      return;
    }
  }
  currentEmpId = "";
  currentEmpName = "";
  isValidEmployee = false;
  Serial.println("Employee not found");
  writetotft(31, "CARD");
  writetotft(32, "NOT IN LIST");
  delay(1000);
  card_band();
}

// Function to handle new file received from web
void handleNewFile()
{
  newFileReceived = true;
  Serial.println("New MASTER.csv file received from web");
  Serial.println("Displaying new file contents:");
  displayFileContents("/MASTER.csv"); // Show the contents of new file
}

// Function to read RFID card and return CSN as global string
// Returns true if a card is read successfully, false otherwise
bool readRFIDCard()
{
  if (xSemaphoreTake(displayMutex, portMAX_DELAY))
  {
    if (mfrc522.PICC_IsNewCardPresent() && mfrc522.PICC_ReadCardSerial())
    {
      char cardCsn[9]; // 8 chars + null terminator
      char temp[3];
      int idx = 0;

      for (byte i = 0; i < mfrc522.uid.size; i++)
      {
        sprintf(temp, "%02X", mfrc522.uid.uidByte[i]);
        cardCsn[idx++] = temp[0];
        cardCsn[idx++] = temp[1];
      }
      cardCsn[idx] = '\0';

      // Update global card CSN string
      currentCardCSN = String(cardCsn);
      Serial.printf("Card read successfully: CSN = %s\n", cardCsn);

      mfrc522.PICC_HaltA();
      mfrc522.PCD_StopCrypto1();
      xSemaphoreGive(displayMutex);
      return true;
    }
    xSemaphoreGive(displayMutex);
  }
  else
  {
    Serial.println("Failed to take mutex for RFID read");
  }
  // Serial.println("No card detected or read failed");
  return false;
}

// Task Handler for Card Detection
void TaskHandleCardDetection(void *parameter)
{
  // Initial LUT creation
  if (!createLUTInPSRAM())
  {
    Serial.println("Failed to create initial LUT in PSRAM");
    vTaskDelete(NULL);
    return;
  }

  // Display initial file contents
  Serial.println("Initial MASTER.csv contents:");
  displayFileContents("/MASTER.csv");

  Serial.println("System ready! Waiting for cards...");

  // Main RFID reading loop
  for (;;)
  {
    // Check if new file was received
    if (newFileReceived)
    {
      Serial.println("Updating LUT with new file data...");
      if (!createLUTInPSRAM())
      {
        Serial.println("Failed to update LUT with new file");
      }
    }

    // Use the readRFIDCard function to check for a card
    if (readRFIDCard())
    {
      if (face == 0)
      {

        // Process card immediately
        findEmployeeByCard(currentCardCSN.c_str());
      }
      else if (face == 11)
      {
        // Card enrollment mode
        Serial.printf("IN CARD ENROLL - CSN: %s\n", currentCardCSN.c_str());
        writetotft(31, "SHOW CARD");
        vTaskDelay(pdMS_TO_TICKS(1000));
        // Store the card CSN for employee enrollment
        strncpy(CardCSN, currentCardCSN.c_str(), sizeof(CardCSN));
        // Set flag to indicate card was scanned
        cardScanned = true;
      }
    }
    vTaskDelay(pdMS_TO_TICKS(50)); // 50ms delay between reads
  }
}

/*
 * Developer Notes:
 * - Added a new global variable 'currentCardCSN' as a String to store the latest card CSN read from the RFID reader.
 * - Created a new function 'readRFIDCard()' that handles the card reading logic and updates 'currentCardCSN' when a card is successfully read.
 * - The 'readRFIDCard()' function returns a boolean (true if a card is read, false otherwise) to indicate success or failure, making it usable by other tasks.
 * - Modified the 'TaskHandleCardDetection' task to use 'readRFIDCard()' instead of directly reading the card, ensuring modularity.
 * - Other RTOS tasks can now call 'readRFIDCard()' to check for a card and access the result via the global 'currentCardCSN' string.
 * - Added debug statements in 'readRFIDCard()' to log card read success or failure on the Serial console.
 */
