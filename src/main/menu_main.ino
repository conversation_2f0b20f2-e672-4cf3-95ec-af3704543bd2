// menu_main.ino
#include "main.h"

// Menu 1 Data
const ButtonInfo menu1Buttons[] = {
    {"ENROLL", "/M1B1"}, // Triggers Enrollment process via handleCommand
    {"DELETE", "/M1B2"}, // Goes to Delete Menu (handleDeleteMenu)
    {"REPORTS", "/M1B4"} // Goes to Reports Menu (handleReportsMenu)
};
const char *menu1Title = "OASYS NG Panel";
const char *menu1BackUrl = "/logout"; // Back to Login

// --- Main Menu Handler ---
void handleMenu1()
{
  resetCommandState(); // Reset state when returning to main menu
  sendMenuPage(menu1Title, menu1Buttons, sizeof(menu1Buttons) / sizeof(menu1Buttons[0]), menu1BackUrl);
}

// --- Generic Menu Page Generator ---
// Moved to utilities.ino for better organization
/*
void sendMenuPage(const char* title, const ButtonInfo buttons[], size_t buttonCount, const char* backUrl) {
    // ... implementation ...
}
*/
