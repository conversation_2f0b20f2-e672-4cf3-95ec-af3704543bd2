#include <main.h>

// Declare these variables at file scope (global)
static volatile bool beepActive = false;
static volatile bool relay1Active = false;
static volatile bool relay2Active = false;
static volatile bool okLedActive = false;
static volatile bool errLedActive = false;

static TimerHandle_t beepTimer = NULL;
static TimerHandle_t relay1Timer = NULL;
static TimerHandle_t relay2Timer = NULL;
static TimerHandle_t okLedTimer = NULL;
static TimerHandle_t errLedTimer = NULL;

static TaskHandle_t gpioControlTaskHandle = NULL;

// Forward declarations
static void gpioTimerCallback(TimerHandle_t xTimer);
void GPIOControlTask(void *parameter);
void START_BEEP(int seconds);
void START_RELAY1(int seconds);
void START_RELAY2(int seconds);
void START_OK_LED(int seconds);
void START_ERR_LED(int seconds);

// Timer callback function with identification
static void gpioTimerCallback(TimerHandle_t xTimer)
{
  // Get timer ID to know which GPIO to handle
  uint32_t id = (uint32_t)pvTimerGetTimerID(xTimer);

  switch (id)
  {
  case 0: // BEEP
    beepActive = false;
    if (beepTimer != NULL)
    {
      xTimerDelete(beepTimer, 0);
      beepTimer = NULL;
    }
    break;
  case 1: // RELAY1
    relay1Active = false;
    if (relay1Timer != NULL)
    {
      xTimerDelete(relay1Timer, 0);
      relay1Timer = NULL;
    }
    break;
  case 2: // RELAY2
    relay2Active = false;
    if (relay2Timer != NULL)
    {
      xTimerDelete(relay2Timer, 0);
      relay2Timer = NULL;
    }
    break;
  case 3: // OK_LED
    okLedActive = false;
    if (okLedTimer != NULL)
    {
      xTimerDelete(okLedTimer, 0);
      okLedTimer = NULL;
    }
    break;
  case 4: // ERR_LED
    errLedActive = false;
    if (errLedTimer != NULL)
    {
      xTimerDelete(errLedTimer, 0);
      errLedTimer = NULL;
    }
    break;
  }
}

// Combined GPIO control task
void GPIOControlTask(void *parameter)
{
  while (1)
  {
    // Control BEEP
    pcf.digitalWrite(BEEP, beepActive ? LOW : HIGH);

    // Control RELAY1
    pcf.digitalWrite(RELAY, relay1Active ? HIGH : LOW);

    // Control RELAY2
    pcf.digitalWrite(RELAY2, relay2Active ? HIGH : LOW);

    // Control OK_LED
    pcf.digitalWrite(OK_LED, okLedActive ? LOW : HIGH);

    // Control ERR_LED
    pcf.digitalWrite(ERR_LED, errLedActive ? LOW : HIGH);

    // Monitor SWITCH
    if (pcf.digitalRead(SWITCH) == 0)
    {
      vTaskDelay(pdMS_TO_TICKS(250)); // Debounce delay

      if (pcf.digitalRead(SWITCH) == 0)
      {
        START_RELAY1(5); // Trigger relay for 5 seconds
      }
    }

    vTaskDelay(pdMS_TO_TICKS(50)); // Small delay for stability
  }
}

// Function to create and start a timer for GPIO control
static TimerHandle_t startGPIOTimer(float seconds, uint32_t id, volatile bool *activeFlag, TimerHandle_t *timerHandle)
{
  // If timer is already active, stop it
  if (*timerHandle != NULL)
  {
    xTimerDelete(*timerHandle, 0);
    *timerHandle = NULL;
  }

  // Create and start new timer
  *timerHandle = xTimerCreate(
      "GPIOTimer",
      pdMS_TO_TICKS(seconds * 1000),
      pdFALSE,
      (void *)id,
      gpioTimerCallback);

  if (*timerHandle != NULL)
  {
    *activeFlag = true;
    xTimerStart(*timerHandle, 0);
  }

  return *timerHandle;
}

// Control functions for each GPIO
void START_BEEP(float seconds)
{
  startGPIOTimer(seconds, 0, &beepActive, &beepTimer);
}

void ERROR_BEEP(float seconds, int times)
{
  for (int i = 0; i < times; i++)
  {
    START_BEEP(seconds);
    vTaskDelay(pdMS_TO_TICKS(seconds * 1000 + 200)); // Beep duration + 200ms gap
  }
}

void START_RELAY1(int seconds)
{
  startGPIOTimer(seconds, 1, &relay1Active, &relay1Timer);
}

void START_RELAY2(int seconds)
{
  startGPIOTimer(seconds, 2, &relay2Active, &relay2Timer);
}

void START_OK_LED(int seconds)
{
  startGPIOTimer(seconds, 3, &okLedActive, &okLedTimer);
}

void START_ERR_LED(int seconds)
{
  startGPIOTimer(seconds, 4, &errLedActive, &errLedTimer);
}

// Initialize GPIO task
void Pragati_Init_gpio(void)
{
  pcf.pinMode(OK_LED, OUTPUT);
  pcf.pinMode(ERR_LED, OUTPUT);
  pcf.pinMode(BEEP, OUTPUT);
  pcf.pinMode(RELAY, OUTPUT);
  pcf.pinMode(RELAY2, OUTPUT);
  pcf.pinMode(SWITCH, INPUT);
  pcf.pinMode(EXT_INPUT, INPUT);
  pcf.begin();

  // Initial states
  pcf.digitalWrite(RELAY, LOW);    // RELAY OFF
  pcf.digitalWrite(RELAY2, LOW);   // RELAY2 OFF
  pcf.digitalWrite(BEEP, HIGH);    // BEEP OFF
  pcf.digitalWrite(OK_LED, HIGH);  // OK LED OFF
  pcf.digitalWrite(ERR_LED, HIGH); // ERR LED OFF
}
