#include <main.h>
#include "html_templates.ino" // Include for HTML templates
#include "esp_efuse.h" // For MAC address functions

// Global flags
bool cardScanned = false; // Flag to indicate card was scanned during enrollment
volatile bool enableDateTimeUpdate = true;

// AP mode settings
String ap_ssid= "OASYS-NG";
const char *ap_password = "12345678";
unsigned long startMillis;
const unsigned long apTimeout = 90000;
const unsigned long keyPressTimeout = 5000;
unsigned long lastKeyPressMillis;


TAMC_GT911 ts = TAMC_GT911(TOUCH_SDA, TOUCH_SCL, TOUCH_INT, TOUCH_RST, TOUCH_WIDTH, TOUCH_HEIGHT);

//	FT6236 ts = FT6236();
// Variables for touch sensing
unsigned long touchStartTime = 0;
bool touchActive = false;

void setup()
{
  Serial.begin(115200);
  Wire.begin();
 


  
  Ethernet.init(14);
  
  // Display partition information
  printPartitionInfo();

  initperipherals(); // RTC,touch,display ,SD card, SPIFF etc



  Reboot = readEEPROM(0x108); // increment reboot count on Power ON
  delay(100);
  Reboot = Reboot + 1;
  delay(100);
  writeEEPROM(0x108, Reboot); // write new count so as to show

  // Draw initial logo
  draw_logo();
  delay(5000);
  // Start AP mode
  start_ap_mode();

  // Read EEPROM values
  delay(100);
  // Pending = readEEPROM(0x110);
  // Total = readEEPROM(0x112);
  Reboot = readEEPROM(0x108);

  // Generate dynamic TID from MAC address (uses AP MAC when in AP mode)
  uint8_t mac[6];
  if (ap_mode_done == 0)
  {
    // In AP mode - use softAP MAC
    String apMac = WiFi.softAPmacAddress();
    sscanf(apMac.c_str(), "%02hhx:%02hhx:%02hhx:%02hhx:%02hhx:%02hhx",
           &mac[0], &mac[1], &mac[2], &mac[3], &mac[4], &mac[5]);
  }
  else
  {
    // In STA mode - use regular MAC
    WiFi.macAddress(mac);
  }
  TID = (mac[4] << 8) | mac[5];

  // Serial.print("Initial Pending: ");
  // Serial.println(Pending);
  // Serial.print("Initial Total: ");
  // Serial.println(Total);
  Serial.print("Initial Reboot: ");
  Serial.println(Reboot);
  Serial.print("Initial WEBMIS: ");
  Serial.println(WEBMIS ? "true" : "false");

  // Create mutex for display access
  displayMutex = xSemaphoreCreateMutex();

  // ... rest of setup() ...

} // end of setup

void initperipherals()
{

  // Initialize peripherals

  EEPROM.begin(512);

  if (!rtc.begin())
  {
    Serial.println("Couldn't find RTC");
    while (1)
      ;
  }

  /*
  // Initialize SD Card
      if(!SD.begin()) {
          Serial.println("SD Card Mount Failed");
      } else {
          Serial.println("SD Card Mounted Successfully");
      } */

  // Initialize SPIFFS
  if (!SPIFFS.begin(true))
  {
    Serial.println("SPIFFS Mount Failed");
    return;
  }
  Serial.println("SPIFFS Mounted Successfully");
  showFlashSize();

#if CONFIG_SPIRAM_SUPPORT
  if (psramInit())
  {
    Serial.println("PSRAM initialized successfully");
  }
  else
  {
    Serial.println("PSRAM initialization failed");
  }
#endif

  // FOR EA100 Motion Sensor
  pinMode(MOTION_SENSOR_PIN, INPUT);

  Pragati_Init_gpio(); // Initialize GPIO

  // Turn the buzzer ON
  pcf.digitalWrite(BEEP, HIGH); // PUT OFF ...
  delay(2000);                  // Wait for 2 seconds


  ts.begin();
  faceScanner.begin();
 SPI.begin(13,12,11,-1);
  // Initialize the GT911 touch panel

  
  // Initialize TFT_eSPI display
  tft.begin();
  tft.setRotation(0); // TFT_eSPI uses different rotation values
  tft.fillScreen(TFT_BLACK);

  //SPI.begin();
  mfrc522.PCD_Init();
  // NBP
  mfrc522.PCD_DumpVersionToSerial();
}

/**
 * Function to write text to a specific line on the display TFT_e_SPI
 * @param line_number Line number (1-6)
 * @param text Text to display (can be either direct string or variable)
 // Examples of usage:
String dateStr = "06-APR-2025";
String timeStr = "21:13";
int rebootCount = 42;
String ipAddress = "*************";

// Using direct strings
writetotft(1, "06-APR-2025");

// Using variables
writetotft(1, dateStr);
writetotft(2, timeStr);

// Using number conversion
writetotft(41, String(rebootCount));

// Using concatenation
writetotft(5, "V: " + String(1.2));
writetotft(6, "IP: " + ipAddress);

 */
void writetotft(int line_number, const String &text)
{
  int16_t x, y;
  uint16_t textWidth, textHeight;

  // Take mutex to prevent concurrent display updates
  if (xSemaphoreTake(displayMutex, portMAX_DELAY))
  {
    tft.setTextSize(1);

    switch (line_number)
    {

    case 1:
    { // Date in Band 1
      text.toCharArray(array_line1, sizeof(array_line1));

      // Clear the band
      tft.fillRect(0, BAND1_Y, TFT_WIDTH, BAND1_HEIGHT, BAND1_COLOR);

      // Set text properties
      tft.setTextColor(TEXT_COLOR_LIGHT);
      tft.setFreeFont(&FreeSans12pt7b);
      tft.setTextSize(1);

      // Calculate position to center text
      textWidth = tft.textWidth(text);
      textHeight = tft.fontHeight();
      x = y = 0;
      x = (TFT_WIDTH - textWidth) / 2;
      y = (BAND1_Y + (BAND1_HEIGHT - textHeight) / 2) + 20;

      // Draw text
      tft.setCursor(5, y);
      tft.print(text);
    }
      // Release mutex
      xSemaphoreGive(displayMutex);
      break;

    case 2:
    { // Time in Band 2
      text.toCharArray(array_line2, sizeof(array_line2));

      // Clear the band
      tft.fillRect(0, BAND2_Y, TFT_WIDTH, BAND2_HEIGHT, BAND2_COLOR);

      // Set text properties
      tft.setTextColor(TEXT_COLOR_LIGHT);
      tft.setFreeFont(&FreeSans24pt7b);
      tft.setTextSize(1);

      // Calculate position to center text
      textWidth = tft.textWidth(text);
      textHeight = tft.fontHeight();
      x = y = 0;
      x = (TFT_WIDTH - textWidth) / 2;
      y = (BAND2_Y + (BAND2_HEIGHT - textHeight) / 2) + 45;

      // Draw text
      tft.setCursor(x, y);
      tft.print(text);
    }
      // Release mutex
      xSemaphoreGive(displayMutex);
      break;

    case 31:
    { // CARD/FINGER in Band 3 (40% of top)
      text.toCharArray(array_line3_1, sizeof(array_line3_1));

      // Calculate band section
      float band3_section1 = BAND3_HEIGHT * 0.4;

      // Clear the section
      tft.fillRect(0, BAND3_Y, TFT_WIDTH, band3_section1, BAND3_COLOR);

      // Set text properties
      tft.setTextColor(TEXT_COLOR_LIGHT);
      tft.setFreeFont(&FreeSans12pt7b);
      tft.setTextSize(1);

      // Calculate position to center text
      textWidth = tft.textWidth(text);
      textHeight = tft.fontHeight();
      x = y = 0;
      x = (TFT_WIDTH - textWidth) / 2;
      y = (BAND3_Y + (band3_section1 - textHeight) / 2) + 20;

      // Draw text
      tft.setCursor(x, y);
      tft.print(text);
    }
      // Release mutex
      xSemaphoreGive(displayMutex);
      break;

    case 32:
    { // FACE in Band 3 (40% of middle)
      text.toCharArray(array_line3_2, sizeof(array_line3_2));

      // Calculate band section
      float band3_section1 = BAND3_HEIGHT * 0.4;
      float band3_section2 = BAND3_HEIGHT * 0.4;

      // Clear the section
      tft.fillRect(0, BAND3_Y + band3_section1, TFT_WIDTH, band3_section2, BAND3_COLOR);

      // Set text properties
      tft.setTextColor(TEXT_COLOR_LIGHT);
      tft.setFreeFont(&FreeSans12pt7b);
      tft.setTextSize(1);

      // Calculate position to center text
      textWidth = tft.textWidth(text);
      textHeight = tft.fontHeight();
      x = y = 0;
      x = (TFT_WIDTH - textWidth) / 2;
      y = BAND3_Y + band3_section1 + (band3_section2 - textHeight) / 2;

      // Draw text
      tft.setCursor(x, y);
      tft.print(text);
    }
      // Release mutex
      xSemaphoreGive(displayMutex);
      break;

    case 33:
    { // ***** in Band 3 (20% of bottom)
      text.toCharArray(array_line3_3, sizeof(array_line3_3));

      // Calculate band section
      float band3_section1 = BAND3_HEIGHT * 0.4;
      float band3_section2 = BAND3_HEIGHT * 0.4;
      float band3_section3 = BAND3_HEIGHT * 0.2;

      // Clear the section
      tft.fillRect(0, BAND3_Y + band3_section1 + band3_section2, TFT_WIDTH, band3_section3, BAND3_COLOR);

      // Set text properties
      tft.setTextColor(TEXT_COLOR_LIGHT);
      tft.setFreeFont(&FreeSans9pt7b);
      tft.setTextSize(1);

      // Calculate position to center text
      textWidth = tft.textWidth(text);
      textHeight = tft.fontHeight();
      x = y = 0;
      x = ((TFT_WIDTH - textWidth) / 2) + 80;
      y = BAND3_Y + band3_section1 + band3_section2 + ((band3_section3 - textHeight) / 2 + 10);

      // Draw text
      tft.setCursor(x, y);
      tft.print(text);
    }
      // Release mutex
      xSemaphoreGive(displayMutex);
      break;

    case 41:
    { // Reboot - Left aligned
      // Clear only the left portion of band 4
      tft.fillRect(0, BAND4_Y, TFT_WIDTH / 3, BAND4_HEIGHT, BAND4_COLOR);

      // Set text properties
      tft.setTextColor(TEXT_COLOR_DARK);
      tft.setFreeFont(&FreeSans9pt7b);
      tft.setTextSize(1);

      // Position text on left with small margin
      tft.setCursor(5, (BAND4_Y + (BAND4_HEIGHT - 8) / 2) + 5);
      tft.print(text);
    }
      // Release mutex
      xSemaphoreGive(displayMutex);
      break;

    case 421:
    { // Total - Center aligned (first number)
      // Clear the middle portion of band 4
      tft.fillRect(TFT_WIDTH / 3, BAND4_Y, TFT_WIDTH / 3, BAND4_HEIGHT, BAND4_COLOR);

      // Set text properties
      tft.setTextColor(TEXT_COLOR_DARK);
      tft.setFreeFont(&FreeSans9pt7b);
      tft.setTextSize(1);

      // Calculate total width for centering
      textWidth = tft.textWidth(text);
      x = (TFT_WIDTH - textWidth) / 2;

      // Draw text
      tft.setCursor(x, (BAND4_Y + (BAND4_HEIGHT - 8) / 2) + 5);
      tft.print(text);
    }
      // Release mutex
      xSemaphoreGive(displayMutex);
      break;

    case 422:
    { // Pending - Center aligned (second number)
      // Set text properties
      tft.setTextColor(TEXT_COLOR_DARK);
      tft.setFreeFont(&FreeSans9pt7b);
      tft.setTextSize(1);

      // Calculate position (after the Total number)
      String prevText = "/";
      textWidth = tft.textWidth(prevText);
      x = ((TFT_WIDTH + textWidth) / 2) + 15; // move to show "/"

      // Draw text
      tft.setCursor(x, (BAND4_Y + (BAND4_HEIGHT - 8) / 2) + 5);
      tft.print(text);
    }
      // Release mutex
      xSemaphoreGive(displayMutex);
      break;

    case 43:
    { // TID - Right aligned
      // Clear only the right portion of band 4
      tft.fillRect(2 * TFT_WIDTH / 3, BAND4_Y, TFT_WIDTH / 3, BAND4_HEIGHT, BAND4_COLOR);

      // Set text properties
      tft.setTextColor(TEXT_COLOR_DARK);
      tft.setFreeFont(&FreeSans9pt7b);
      tft.setTextSize(1);

      // Calculate position for right alignment with small margin
      textWidth = tft.textWidth(text);
      x = TFT_WIDTH - textWidth - 5;

      // Draw text
      tft.setCursor(x, (BAND4_Y + (BAND4_HEIGHT - 8) / 2) + 5);
      tft.print(text);
    }
      // Release mutex
      xSemaphoreGive(displayMutex);
      break;

    case 5:
    { // V: value in Band 5
      text.toCharArray(array_line5, sizeof(array_line5));

      // Clear the band
      tft.fillRect(0, BAND5_Y, TFT_WIDTH, BAND5_HEIGHT, BAND5_COLOR);

      // Set text properties
      tft.setTextColor(TEXT_COLOR_DARK);
      tft.setFreeFont(&FreeSans9pt7b);
      tft.setTextSize(1);

      // Calculate position to center text
      textWidth = tft.textWidth(text);
      textHeight = tft.fontHeight();
      x = y = 0;
      x = 10;
      y = (BAND5_Y + (BAND5_HEIGHT - textHeight) / 2) + 15;

      // Draw text
      tft.setCursor(x, y);
      tft.print(text);
    }
      // Release mutex
      xSemaphoreGive(displayMutex);
      break;

    case 6:
    { // IP: value in Band 6
      text.toCharArray(array_line6, sizeof(array_line6));

      // Clear the band
      tft.fillRect(0, BAND6_Y, TFT_WIDTH, BAND6_HEIGHT, BAND6_COLOR);

      // Set text properties
      tft.setTextColor(TEXT_COLOR_DARK);
      tft.setFreeFont(&FreeSans9pt7b);
      tft.setTextSize(1);

      // Calculate position to center text
      textWidth = tft.textWidth(text);
      textHeight = tft.fontHeight();
      x = y = 0;
      x = 70;
      y = (BAND6_Y + (BAND6_HEIGHT - textHeight) / 2) + 15;

      // Draw text
      tft.setCursor(x, y);
      tft.print(text);
    }
      // Release mutex
      xSemaphoreGive(displayMutex);
      break;

    default:
      Serial.println("Invalid line number");
      // Release mutex
      xSemaphoreGive(displayMutex);
      break;
    }
  }
}

/**
 * Initialize the display
 */
void initDisplay()
{
  // Take mutex for direct TFT operations
  if (xSemaphoreTake(displayMutex, portMAX_DELAY))
  {
    // Initialize TFT (redundant if already done in initperipherals, but safe)
    // tft.init(); // Usually called once in setup/initperipherals
    tft.setRotation(0); // Portrait mode
    tft.fillScreen(TFT_BLACK);

    // Draw initial bands
    tft.fillRect(0, BAND1_Y, TFT_WIDTH, BAND1_HEIGHT, BAND1_COLOR);
    tft.fillRect(0, BAND2_Y, TFT_WIDTH, BAND2_HEIGHT, BAND2_COLOR);
    tft.fillRect(0, BAND3_Y, TFT_WIDTH, BAND3_HEIGHT, BAND3_COLOR);
    tft.fillRect(0, BAND4_Y, TFT_WIDTH, BAND4_HEIGHT, BAND4_COLOR);
    tft.fillRect(0, BAND5_Y, TFT_WIDTH, BAND5_HEIGHT, BAND5_COLOR);
    tft.fillRect(0, BAND6_Y, TFT_WIDTH, BAND6_HEIGHT, BAND6_COLOR);

    // Release mutex after direct TFT operations
    xSemaphoreGive(displayMutex);
  }
  else
  {
    Serial.println("Failed to get display mutex in initDisplay()");
    // If mutex fails, subsequent writetotft calls might also fail or block
  }

  // Initialize text arrays (These are not shared resources accessed directly by others)
  strcpy(array_line1, "");
  strcpy(array_line2, "");
  strcpy(array_line3_1, "CARD/FINGER");
  strcpy(array_line3_2, "FACE");
  strcpy(array_line3_3, "");
  strcpy(array_line4, ""); // Note: Line 4 uses sub-cases (41, 421, 422, 43)
  strcpy(array_line5, "V: 0.0");
  strcpy(array_line6, "IP: 0.0.0.0");

  // Write initial values to display using the protected writetotft function
  writetotft(1, array_line1);
  writetotft(2, array_line2);
  writetotft(31, array_line3_1);
  writetotft(32, array_line3_2);
  writetotft(33, array_line3_3);
  // writetotft(4, array_line4); // Initial call for line 4 might need adjustment based on sub-cases
  writetotft(41, ""); // Initial empty values for sub-cases
  writetotft(421, "");
  writetotft(422, "");
  writetotft(43, "");
  writetotft(5, array_line5);
  writetotft(6, array_line6);

  Serial.println("Display initialized");
}

void card_band()
{ // to re-instate card rectangle
  String inoutText = "";
  if (INOUT == 1)
  {
    inoutText = "IN";
  }
  else if (INOUT == 2)
  {
    inoutText = "OUT";
  }

  if (xSemaphoreTake(displayMutex, portMAX_DELAY))
  {
    tft.fillRect(0, BAND3_Y, TFT_WIDTH, BAND3_HEIGHT, BAND3_COLOR);
    xSemaphoreGive(displayMutex);
    writetotft(31, "CARD/FINGER");
    writetotft(32, "FACE");
    writetotft(33, inoutText);
  }
  else
  {
    Serial.println("Failed to get display mutex in card_band()");
  }
}

uint16_t readEEPROM(uint16_t location)
{
  uint16_t value = 0;
  Wire.beginTransmission(EEPROM_ADDRESS);
  Wire.write((location >> 8) & 0xFF);
  Wire.write(location & 0xFF);
  Wire.endTransmission();
  delay(5);
  Wire.requestFrom(EEPROM_ADDRESS, 2);
  if (Wire.available() >= 2)
  {
    value = (Wire.read() << 8) | Wire.read();
  }
  return value;
}

void writeEEPROM(uint16_t location, uint16_t value)
{
  Wire.beginTransmission(EEPROM_ADDRESS);
  Wire.write((location >> 8) & 0xFF);
  Wire.write(location & 0xFF);
  Wire.write((value >> 8) & 0xFF);
  Wire.write(value & 0xFF);
  Wire.endTransmission();
  delay(10);
}

void draw_logo()
{
  // Initialize serial communication for debugging
  Serial.begin(115200);
  Serial.println("TFT_eSPI Display Initialization");

  // Initialize the display
  tft.begin();
  tft.setRotation(0); // TFT_eSPI rotation (1 = 90°)
  tft.fillScreen(TFT_BLACK);

  tft.setCursor(1, 305);
  tft.setTextColor(TFT_WHITE, TFT_BLACK);
  tft.setTextFont(2); // Use built-in font 2
  tft.setTextSize(1);
  tft.print("LONGTOUCH TO CANCEL");

  // Draw the image
  drawMonochromeImage(20, 55, 200, 200, box_image);
}

void drawMonochromeImage(int16_t x, int16_t y, int16_t w, int16_t h, const uint16_t *bitmap)
{
  int16_t byteWidth = (w + 7) / 8; // Bitmap scanline pad = whole byte
  uint8_t byte = 0;

  for (int16_t j = 0; j < h; j++, y++)
  {
    for (int16_t i = 0; i < w; i++)
    {
      if (i & 7)
      {
        byte <<= 1;
      }
      else
      {
        byte = pgm_read_byte(bitmap + j * byteWidth + i / 8);
      }
      if (byte & 0x80)
      {
        tft.drawPixel(x + i, y, TFT_WHITE);
      }
      else
      {
        tft.drawPixel(x + i, y, TFT_BLACK);
      }
    }
  }
}

void drawIcon(int x, int y)
{
  // Take mutex before drawing pixels
  if (xSemaphoreTake(displayMutex, portMAX_DELAY))
  {
    for (int i = 0; i < 37; i++)
    {
      for (int j = 0; j < 45; j++)
      {
        // pgm_read_word is safe for concurrent access
        // Accessing tft.drawPixel needs protection
        tft.drawPixel(x + j, y + i, pgm_read_word(&(icon[i][j])));
      }
    }
    // Release mutex after drawing pixels
    xSemaphoreGive(displayMutex);
  }
  else
  {
    Serial.println("Failed to get display mutex in drawIcon()");
  }
}

// Function to send an NTP request to the time server
void sendNTPpacket(IPAddress &address)
{
  memset(packetBuffer, 0, NTP_PACKET_SIZE);
  packetBuffer[0] = 0b11100011; // LI, Version, Mode
  packetBuffer[1] = 0;          // Stratum, or type of clock
  packetBuffer[2] = 6;          // Polling Interval
  packetBuffer[3] = 0xEC;       // Peer Clock Precision
  packetBuffer[12] = 49;
  packetBuffer[13] = 0x4E;
  packetBuffer[14] = 49;
  packetBuffer[15] = 52;

  Udp.beginPacket(address, 123); // NTP requests are to port 123
  Udp.write(packetBuffer, NTP_PACKET_SIZE);
  Udp.endPacket();
}

void getTimeFromNTP()
{
  IPAddress ntpServerIP;

  // Resolve the NTP server IP address
  if (WiFi.hostByName(ntpServer.c_str(), ntpServerIP))
  {
    Serial.print("NTP Server: ");
    Serial.print(ntpServer.c_str());
    Serial.print(" IP: ");
    Serial.println(ntpServerIP);

    sendNTPpacket(ntpServerIP);

    delay(1000); // Wait for response

    int cb = Udp.parsePacket();
    if (!cb)
    {
      Serial.println("No packet received");
    }
    else
    {
      Udp.read(packetBuffer, NTP_PACKET_SIZE);

      // Extract the time from the NTP packet
      unsigned long highWord = word(packetBuffer[40], packetBuffer[41]);
      unsigned long lowWord = word(packetBuffer[42], packetBuffer[43]);
      unsigned long secsSince1900 = highWord << 16 | lowWord;

      const unsigned long seventyYears = 2208988800UL;
      unsigned long epoch = secsSince1900 - seventyYears;

      // Adjust for Indian Standard Time (IST) +5:30
      epoch += 19800;

      DateTime ntpTime = DateTime(epoch);
      Serial.print("NTP Time: ");
      Serial.print(ntpTime.day());
      Serial.print("-");
      Serial.print(monthShortStr(ntpTime.month()));
      Serial.print("-");
      Serial.print(ntpTime.year());
      Serial.print(" ");
      Serial.print(ntpTime.hour());
      Serial.print(":");
      Serial.println(ntpTime.minute());

      // Update RTC
      rtc.adjust(ntpTime);
      Serial.println("RTC updated from NTP server");
      ntpSuccess = true;
    }
  }

  if (!ntpSuccess)
  {
    Serial.println("NTP sync failed, using RTC time");
    // Get time from RTC
    DateTime now = rtc.now();
    Serial.print("RTC Time: ");
    Serial.print(now.day());
    Serial.print("-");
    Serial.print(monthShortStr(now.month()));
    Serial.print("-");
    Serial.print(now.year());
    Serial.print(" ");
    Serial.print(now.hour());
    Serial.print(":");
    Serial.println(now.minute());
  }
}

// Define CardCSN storage
char CardCSN[9] = {0};

// Basic implementation of handleMenu3
void handleMenu3()
{
  server.send(200, "text/html", "Menu 3 Placeholder");
}

void loop()
{

  // Main program loop
  if (ap_mode_done == 0)
  {

    serverAP.handleClient();

    unsigned long currentMillis = millis();
    unsigned long remainingTime = (apTimeout - (currentMillis - startMillis)) / 1000;

    tft.fillRect(20, 280, 200, 20, TFT_BLACK);
    tft.setCursor(1, 280);
    tft.print("SSID: ");
    tft.print(ap_ssid);
    tft.print(" Time Left: ");
    tft.print(remainingTime);
    //tft.print(" sec.");

    delay(800);
ts.read();
    // Check for touch input
    if (isTouchDetected())
    {
      if (!touchActive)
      {
        touchStartTime = millis(); // Start timing when touch is first detected
        touchActive = true;
      }
      else if (millis() - touchStartTime >= 1500)
      {
        // NBP If touch is held for 2 seconds, terminate AP Mode
        Serial.println("Touch held for 2 seconds. Terminating AP Mode...");
        terminateAPMode();
      }
    }
    else
    {
      touchActive = false; // Reset if no touch is detected
    }

    if (remainingTime == 0)
    {
      terminateAPMode();
    }
  }
}

// Function to print partition information
void printPartitionInfo()
{
  Serial.println("\n--- ESP32 Partition Information ---");

  // Display Flash size
  uint32_t flashSize = ESP.getFlashChipSize();
  Serial.printf("Flash Size: %d bytes (%.2f MB)\n", flashSize, flashSize / 1048576.0);

  // Display SPIFFS information
  Serial.printf("SPIFFS Total: %d bytes (%.2f KB)\n", SPIFFS.totalBytes(), SPIFFS.totalBytes() / 1024.0);
  Serial.printf("SPIFFS Used: %d bytes (%.2f KB)\n", SPIFFS.usedBytes(), SPIFFS.usedBytes() / 1024.0);
  Serial.printf("SPIFFS Free: %d bytes (%.2f KB)\n", SPIFFS.totalBytes() - SPIFFS.usedBytes(),
                (SPIFFS.totalBytes() - SPIFFS.usedBytes()) / 1024.0);

  // Display RAM information
  Serial.printf("Free Heap: %d bytes (%.2f KB)\n", ESP.getFreeHeap(), ESP.getFreeHeap() / 1024.0);
  Serial.printf("Total Heap: %d bytes (%.2f KB)\n", ESP.getHeapSize(), ESP.getHeapSize() / 1024.0);

// Display PSRAM information if available
#if CONFIG_SPIRAM_SUPPORT
  Serial.printf("PSRAM Size: %d bytes (%.2f MB)\n", ESP.getPsramSize(), ESP.getPsramSize() / 1048576.0);
  Serial.printf("Free PSRAM: %d bytes (%.2f MB)\n", ESP.getFreePsram(), ESP.getFreePsram() / 1048576.0);
#endif

  // Display sketch information
  Serial.printf("Sketch Size: %d bytes (%.2f KB)\n", ESP.getSketchSize(), ESP.getSketchSize() / 1024.0);
  Serial.printf("Free Sketch Space: %d bytes (%.2f KB)\n", ESP.getFreeSketchSpace(), ESP.getFreeSketchSpace() / 1024.0);

  // Display partition table
  esp_partition_iterator_t it = esp_partition_find(ESP_PARTITION_TYPE_ANY, ESP_PARTITION_SUBTYPE_ANY, NULL);
  Serial.println("\nPartition Table:");
  Serial.println("----------------------------------------------------------");
  Serial.println("| Type | Subtype | Address    | Size       | Label      |");
  Serial.println("----------------------------------------------------------");

  while (it != NULL)
  {
    const esp_partition_t *part = esp_partition_get(it);
    Serial.printf("| 0x%02x | 0x%02x     | 0x%08x | %10d | %-10s |\n",
                  part->type, part->subtype, part->address, part->size, part->label);
    it = esp_partition_next(it);
  }

  esp_partition_iterator_release(it);
  Serial.println("----------------------------------------------------------");
  Serial.println("");
}
