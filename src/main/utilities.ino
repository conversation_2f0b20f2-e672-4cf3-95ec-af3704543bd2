// utilities.ino
#include "main.h"

uint16_t printMacAddress(uint8_t mac[6])
{
  for (int i = 0; i < 6; i++)
  {
    if (i > 0)
      Serial.print(":");
    if (mac[i] < 0x10)
      Serial.print("0");
    Serial.print(mac[i], HEX);
  }
  return 0; // Return some transaction ID if needed
}

void resetCommandState()
{
  // Reset flags related to ongoing web operations
  // Be careful NOT to reset flags needed by background tasks unless the web operation
  // is truly finished or cancelled.
  // Example: Only reset flag_enroll AFTER enrollment process completes or is cancelled.
  // flag_enroll = false; // Reset when appropriate, maybe not here?
  // face = 0; // Only reset when NO scanner operation is expected.
  // cardScanned = false; // Reset if needed
  // memset(CardCSN, 0, sizeof(CardCSN)); // Clear CSN buffer if needed

  Serial.println("Web command state reset."); // Or specific flags reset
}

// --- File System Utilities ---
// Implement initSPIFFS, writeToCSV here if needed by multiple web server files

// --- HTML Generation Helpers ---
void sendMenuPage(const char *title, const ButtonInfo buttons[], size_t buttonCount, const char *backUrl)
{
  if (!isLoggedIn)
  {
    server.sendHeader("Location", "/");
    server.send(302, "text/plain", "");
    return;
  }
  lastActivityTime = millis();

  String html = "<!DOCTYPE HTML><html><head><title>";
  html += title;
  html += "</title><meta name='viewport' content='width=device-width, initial-scale=1'>";
  html += FPSTR(htmlStyle);   // Use PROGMEM strings
  html += FPSTR(timerScript); // Use PROGMEM strings
  html += "</head><body><div class='container'>";
  html += "<h1>";
  html += title;
  html += "</h1>";

  for (size_t i = 0; i < buttonCount; ++i)
  {
    html += "<a href='";
    html += buttons[i].url;
    html += "' class='button'>";
    html += buttons[i].text;
    html += "</a>";
  }

  html += "<a href='";
  html += backUrl;
  html += "' class='button back-button'>";
  html += (strcmp(backUrl, "/logout") == 0) ? "Back to Login" : "Back";
  html += "</a>";

  html += "<div id='timer' class='timer'></div>";
  html += "</div></body></html>";

  server.send(200, "text/html", html);
}

String generateHtmlHeader(const char *title)
{
  String header = "<!DOCTYPE HTML><html><head><title>";
  header += title;
  header += "</title><meta name='viewport' content='width=device-width, initial-scale=1'>";
  header += FPSTR(htmlStyle);
  header += FPSTR(timerScript);
  header += "</head><body>"; // Body tag opened here
  return header;
}

String generateHtmlFooter(const char *backUrl)
{
  // Assume the main content div is already closed before calling this
  String footer = "<div id='timer' class='timer'></div>"; // Timer usually inside container
  footer += "<a href='";
  footer += backUrl;
  footer += "' class='button back-button'>Back</a>"; // Back button usually inside container
  // footer += "</div>"; // Closing the container div should happen before this if used stand-alone
  footer += "</body></html>"; // Close body and html
  return footer;
}

// Add other utility functions as needed
