

// https://javl.github.io/image2cpp/

// Byte array for a 100x100 monochrome image of a box with two diagonal lines
const uint16_t box_image[5000] PROGMEM = {
	// ... (image data)
	0xff, 0xff, 0xff, 0xfd, 0xde, 0xf7, 0xbb, 0xbb,
	0xbb, 0xef, 0xbd, 0xef, 0x7b, 0xbb, 0xbb, 0xbb,
	0xbb, 0xde, 0xf7, 0xbd, 0xef, 0x7f, 0xff, 0xff,
	0xff, 0xff, 0xff, 0xff, 0xea, 0x52, 0x94, 0xaa,
	0xaa, 0xaa, 0x28, 0xa5, 0x29, 0x4a, 0xaa, 0xaa,
	0xaa, 0xaa, 0x52, 0x94, 0xa5, 0x29, 0x4b, 0xff,
	0xff, 0xff, 0xff, 0xff, 0xff, 0xc0, 0x00, 0x00,
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x0b,
	0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xa2, 0x92,
	0x49, 0x10, 0x84, 0x44, 0x92, 0x49, 0x24, 0x92,
	0x22, 0x22, 0x22, 0x22, 0x49, 0x24, 0x92, 0x49,
	0x23, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x08,
	0x20, 0x82, 0x44, 0x21, 0x10, 0x41, 0x00, 0x00,
	0x00, 0x88, 0x88, 0x88, 0x88, 0x02, 0x01, 0x00,
	0x00, 0x02, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfd,
	0x20, 0x84, 0x10, 0x01, 0x08, 0x02, 0x08, 0x25,
	0x52, 0xa8, 0x00, 0x00, 0x00, 0x01, 0x20, 0x48,
	0x24, 0x94, 0x90, 0xbf, 0xff, 0xff, 0xff, 0xff,
	0xfd, 0x09, 0x01, 0x24, 0x94, 0x42, 0x48, 0xa2,
	0x88, 0x04, 0x02, 0xaa, 0x55, 0x2a, 0x54, 0x49,
	0x02, 0x02, 0x20, 0x44, 0x7f, 0xff, 0xff, 0xff,
	0xff, 0xf8, 0x40, 0xba, 0x92, 0xaa, 0xaa, 0xaa,
	0x94, 0xa5, 0x55, 0x5a, 0x49, 0x49, 0x52, 0xa5,
	0x2a, 0xba, 0xd9, 0x56, 0x01, 0x1e, 0xff, 0xff,
	0xff, 0xff, 0xf4, 0x12, 0xd7, 0xff, 0x77, 0x7f,
	0xf7, 0x7f, 0xff, 0xfb, 0xef, 0xff, 0xff, 0xdf,
	0xdf, 0xff, 0x6f, 0x7f, 0xed, 0x94, 0x57, 0xff,
	0xff, 0xff, 0xff, 0xd1, 0x05, 0xff, 0xff, 0xff,
	0xfe, 0xff, 0xff, 0xbf, 0xff, 0xff, 0xf7, 0xf7,
	0x7e, 0xff, 0xf7, 0xff, 0xff, 0xff, 0x00, 0x0f,
	0xfb, 0xff, 0xff, 0xff, 0x60, 0x4b, 0xff, 0x77,
	0xff, 0xfb, 0xff, 0xfe, 0xfe, 0xff, 0xff, 0x7f,
	0xbf, 0xff, 0xfe, 0xff, 0xff, 0xff, 0x7f, 0xd1,
	0x23, 0xff, 0xff, 0xff, 0xff, 0xc5, 0x17, 0xff,
	0xff, 0xff, 0xff, 0xff, 0xfb, 0xff, 0xff, 0xff,
	0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfb, 0xff,
	0xc4, 0x0b, 0xff, 0xff, 0xff, 0xff, 0x10, 0x2f,
	0xff, 0xff, 0x77, 0x7f, 0xdd, 0xef, 0xfb, 0xdb,
	0x6d, 0xfe, 0xff, 0xfb, 0xf7, 0xdf, 0x7b, 0x6f,
	0xff, 0xf0, 0x80, 0xff, 0xff, 0xff, 0xfa, 0x82,
	0x5f, 0xe8, 0x41, 0x49, 0x42, 0x52, 0x2a, 0x8a,
	0x55, 0x55, 0x02, 0x90, 0x0a, 0x14, 0x51, 0x4a,
	0xaa, 0xff, 0xe9, 0x29, 0x7f, 0xff, 0xff, 0xfe,
	0x10, 0x7f, 0xe1, 0x08, 0x10, 0x10, 0x01, 0x00,
	0x20, 0x00, 0x00, 0x50, 0x05, 0x40, 0x81, 0x04,
	0x00, 0x00, 0x7f, 0xbc, 0x00, 0xbf, 0xff, 0xff,
	0xd8, 0x45, 0xff, 0x90, 0x22, 0x42, 0x44, 0x94,
	0x52, 0x09, 0x49, 0x24, 0x04, 0xa0, 0x12, 0x24,
	0x20, 0x92, 0x49, 0x1d, 0xfd, 0x24, 0x2f, 0xff,
	0xff, 0xf5, 0x10, 0xff, 0x45, 0x00, 0x08, 0x00,
	0x00, 0x00, 0xa0, 0x00, 0x01, 0x20, 0x04, 0x40,
	0x00, 0x84, 0x00, 0x00, 0x5f, 0xfe, 0x01, 0x3f,
	0x7f, 0xff, 0xe8, 0x07, 0xfe, 0x80, 0x49, 0x21,
	0x25, 0x49, 0x24, 0x04, 0x92, 0x90, 0x09, 0x21,
	0x09, 0x48, 0x10, 0x94, 0xa4, 0x0f, 0xff, 0xa4,
	0x07, 0xff, 0xff, 0xd0, 0x93, 0xfd, 0x12, 0x00,
	0x04, 0x00, 0x00, 0x01, 0x20, 0x00, 0x05, 0x40,
	0x08, 0x20, 0x02, 0x42, 0x00, 0x01, 0x25, 0xff,
	0x88, 0x8b, 0xff, 0xff, 0x42, 0x0f, 0xf4, 0x44,
	0x4a, 0x90, 0x92, 0x4a, 0x94, 0x0a, 0x54, 0x90,
	0x12, 0x81, 0x04, 0x90, 0x08, 0x52, 0x48, 0x07,
	0xfd, 0xe0, 0x23, 0xff, 0xff, 0x40, 0xbf, 0xba,
	0x00, 0x00, 0x02, 0x08, 0x00, 0x00, 0x80, 0x00,
	0x02, 0x40, 0x24, 0x20, 0x05, 0x21, 0x00, 0x02,
	0x91, 0x7f, 0xd2, 0x01, 0x7f, 0xfe, 0x92, 0x2f,
	0xe9, 0x29, 0x29, 0x50, 0x41, 0x28, 0xa4, 0x24,
	0x92, 0x50, 0x09, 0x00, 0x8a, 0x50, 0x04, 0x12,
	0xa8, 0x41, 0xff, 0xe8, 0xa4, 0xff, 0xfd, 0x00,
	0xbf, 0xd0, 0x15, 0x40, 0x04, 0x10, 0x02, 0x01,
	0x00, 0x00, 0x04, 0x82, 0x2a, 0x00, 0x01, 0x40,
	0x80, 0x01, 0x08, 0x5f, 0xfc, 0x01, 0x7f, 0xfa,
	0x09, 0x7e, 0xc2, 0xbf, 0x52, 0x41, 0x45, 0x48,
	0x24, 0x55, 0x55, 0x40, 0x28, 0x00, 0x52, 0x94,
	0x12, 0x2a, 0x48, 0x20, 0x7f, 0xfa, 0x94, 0x7f,
	0xf8, 0x41, 0xff, 0x90, 0xed, 0xe8, 0x10, 0x00,
	0x01, 0x00, 0x00, 0x00, 0x15, 0x01, 0x49, 0x00,
	0x00, 0x40, 0x00, 0x02, 0x05, 0x1d, 0xfe, 0x00,
	0xff, 0xf8, 0x15, 0xfe, 0x85, 0xbf, 0xfa, 0x45,
	0x28, 0x90, 0x52, 0x44, 0x22, 0x40, 0x48, 0x00,
	0x24, 0x92, 0x09, 0x49, 0x20, 0x90, 0x0f, 0xff,
	0x48, 0xbf, 0xf9, 0x47, 0xfe, 0x12, 0xff, 0xf8,
	0x00, 0x02, 0x05, 0x00, 0x11, 0x08, 0x04, 0x02,
	0x54, 0x80, 0x00, 0x84, 0x00, 0x4a, 0x04, 0xa7,
	0xff, 0xa2, 0x7f, 0xf8, 0x17, 0xf4, 0x83, 0xff,
	0xfe, 0xa4, 0x90, 0x48, 0x15, 0x00, 0x41, 0x21,
	0x50, 0x00, 0x15, 0x54, 0x20, 0x95, 0x00, 0x50,
	0x03, 0xdf, 0xa0, 0xff, 0xf9, 0x1f, 0xd8, 0x4b,
	0xff, 0xfa, 0x02, 0x04, 0x81, 0x40, 0x55, 0x14,
	0x08, 0x04, 0x92, 0x40, 0x01, 0x08, 0x00, 0x11,
	0x02, 0x22, 0xfe, 0xc8, 0xbf, 0xf8, 0x5f, 0xf5,
	0x03, 0xf5, 0xdc, 0xa8, 0xa9, 0x2a, 0x15, 0x00,
	0x40, 0xa2, 0x40, 0x00, 0x08, 0x88, 0x42, 0xa4,
	0x84, 0x10, 0x89, 0x7f, 0xe2, 0x7f, 0xf9, 0x1f,
	0x50, 0x2b, 0xe8, 0xff, 0x77, 0xf6, 0xed, 0xfb,
	0xff, 0xb2, 0x00, 0x15, 0x4a, 0xa2, 0x21, 0x10,
	0x00, 0x20, 0x84, 0x00, 0xff, 0xc0, 0xff, 0xf8,
	0x2f, 0xe2, 0x03, 0xe0, 0xbf, 0xff, 0x5f, 0xff,
	0xef, 0xff, 0xe8, 0x4a, 0x80, 0x00, 0x00, 0x04,
	0x04, 0x92, 0x08, 0x21, 0x24, 0x5f, 0x54, 0x7f,
	0xfa, 0x3f, 0x88, 0x93, 0xe8, 0xff, 0xff, 0xff,
	0xff, 0xff, 0xbf, 0xfd, 0x00, 0x24, 0xa9, 0x12,
	0x90, 0x90, 0x00, 0xa2, 0x88, 0x41, 0x1d, 0xe0,
	0xff, 0xf8, 0x8e, 0xe0, 0x07, 0xe2, 0xbf, 0xdf,
	0xff, 0x6f, 0x7f, 0xfd, 0xfc, 0x28, 0x80, 0x00,
	0x40, 0x02, 0x02, 0x4a, 0x00, 0x02, 0x14, 0x1f,
	0xc4, 0x7f, 0xf8, 0x3f, 0xc5, 0x43, 0xe8, 0xfe,
	0xfb, 0x7b, 0xff, 0xff, 0xff, 0xf6, 0x02, 0x15,
	0x12, 0x09, 0x28, 0x48, 0x00, 0x24, 0xa0, 0x80,
	0x9f, 0xc0, 0xbf, 0xf9, 0x1f, 0xc0, 0x17, 0xf4,
	0xfa, 0x09, 0x04, 0x11, 0x20, 0x42, 0x5f, 0x48,
	0x40, 0x40, 0x84, 0x01, 0x01, 0x52, 0x80, 0x0a,
	0x12, 0x1f, 0xd4, 0xff, 0xf8, 0x5f, 0x54, 0x83,
	0xf5, 0xfc, 0xa0, 0x41, 0x44, 0x95, 0x29, 0x7e,
	0x81, 0x09, 0x08, 0x20, 0x90, 0x28, 0x00, 0x15,
	0x20, 0x40, 0x5d, 0xc0, 0xbf, 0xfa, 0x2f, 0xc0,
	0x22, 0xff, 0xfa, 0x04, 0x14, 0x00, 0x00, 0x80,
	0x0b, 0xe4, 0x20, 0x22, 0x88, 0x04, 0x82, 0x49,
	0x00, 0x02, 0x09, 0x1f, 0xd2, 0xff, 0xf8, 0x2f,
	0xc9, 0x0b, 0xff, 0xf8, 0x41, 0x40, 0x2a, 0x48,
	0x12, 0x2f, 0xd0, 0x84, 0x80, 0x02, 0xa0, 0x20,
	0x00, 0x48, 0x90, 0x82, 0x37, 0xc0, 0x7f, 0xf9,
	0x3f, 0x40, 0x40, 0xdf, 0xea, 0x10, 0x09, 0x00,
	0x02, 0x40, 0x83, 0xe0, 0x10, 0x14, 0xa0, 0x0a,
	0x89, 0x2a, 0x02, 0x04, 0x28, 0x1f, 0xd4, 0xbf,
	0xf8, 0x1f, 0xd4, 0x12, 0xff, 0xf0, 0x84, 0x80,
	0x44, 0xa8, 0x08, 0x0b, 0xfa, 0x82, 0x40, 0x0a,
	0x40, 0x00, 0x40, 0xa8, 0x51, 0x01, 0x5f, 0xc0,
	0xff, 0xf9, 0x2f, 0xc1, 0x40, 0x7f, 0xd2, 0x20,
	0x2a, 0x10, 0x01, 0x42, 0x42, 0xf4, 0x20, 0x04,
	0x80, 0x11, 0x25, 0x02, 0x01, 0x00, 0x48, 0x1f,
	0xa4, 0x7f, 0xf8, 0x3f, 0x44, 0x09, 0x1e, 0x40,
	0x0a, 0x80, 0x82, 0x44, 0x10, 0x21, 0x7c, 0x0a,
	0x90, 0x29, 0x04, 0x00, 0x28, 0x10, 0x24, 0x02,
	0x5f, 0xd0, 0xff, 0xf9, 0x1d, 0xd0, 0x80, 0x37,
	0x89, 0x40, 0x12, 0x24, 0x10, 0x85, 0x09, 0x7d,
	0x40, 0x04, 0x80, 0x40, 0x94, 0x81, 0x44, 0x81,
	0x50, 0x1f, 0xc2, 0x7f, 0xf8, 0x57, 0xc2, 0x29,
	0x1f, 0x42, 0x12, 0x40, 0x01, 0x02, 0x10, 0x40,
	0xb7, 0x12, 0x50, 0x12, 0x14, 0x00, 0x08, 0x00,
	0x14, 0x05, 0x2e, 0xd0, 0xbf, 0xf9, 0x1f, 0xc8,
	0x00, 0x5f, 0x88, 0x00, 0x09, 0x50, 0x50, 0x41,
	0x0a, 0x1f, 0x80, 0x02, 0x80, 0x81, 0x52, 0x42,
	0x2a, 0x40, 0x90, 0x1f, 0xc4, 0xff, 0xf8, 0x2f,
	0xc1, 0x25, 0x1f, 0x42, 0x54, 0x80, 0x05, 0x04,
	0x08, 0x20, 0x2f, 0xa5, 0x20, 0x2a, 0x24, 0x00,
	0x10, 0x80, 0x08, 0x01, 0x5f, 0xd0, 0xbf, 0xf9,
	0x3f, 0xa4, 0x00, 0x5f, 0x10, 0x00, 0x2a, 0x40,
	0x21, 0x22, 0x04, 0x8b, 0xd0, 0x09, 0x00, 0x00,
	0x4a, 0x84, 0x11, 0x42, 0x48, 0x1f, 0xc2, 0x7f,
	0xf8, 0x1f, 0xc0, 0x92, 0x1f, 0x82, 0x92, 0x80,
	0x12, 0x08, 0x00, 0xa0, 0x27, 0xe9, 0x40, 0x44,
	0xa9, 0x00, 0x20, 0x84, 0x10, 0x02, 0x5f, 0xa4,
	0xff, 0xf9, 0x2f, 0xca, 0x00, 0x9f, 0x48, 0x00,
	0x12, 0x40, 0xa2, 0x94, 0x09, 0x0a, 0xf0, 0x15,
	0x10, 0x00, 0x24, 0x84, 0x20, 0x85, 0x50, 0x1b,
	0xd0, 0x7f, 0xf8, 0x3f, 0x40, 0x54, 0x3f, 0x40,
	0xaa, 0x40, 0x0a, 0x00, 0x01, 0x20, 0x43, 0xfa,
	0x40, 0x02, 0x4a, 0x00, 0x11, 0x08, 0x20, 0x04,
	0x9f, 0xc0, 0xbf, 0xfa, 0x9f, 0xd2, 0x01, 0x1f,
	0x14, 0x00, 0x09, 0x20, 0x49, 0x24, 0x04, 0x10,
	0xec, 0x04, 0x90, 0x00, 0xa9, 0x00, 0x42, 0x84,
	0x40, 0x3f, 0xd4, 0xff, 0xf8, 0x2f, 0xc0, 0x90,
	0x5d, 0xc1, 0x12, 0x40, 0x84, 0x00, 0x00, 0x91,
	0x02, 0xf9, 0x20, 0x05, 0x52, 0x00, 0x4a, 0x10,
	0x11, 0x12, 0x97, 0xa0, 0xbf, 0xf8, 0x2f, 0x48,
	0x04, 0x1f, 0x04, 0x40, 0x14, 0x11, 0x2a, 0xaa,
	0x04, 0x50, 0xfc, 0x09, 0x50, 0x00, 0x49, 0x00,
	0x84, 0x80, 0x00, 0x1f, 0xc2, 0x7f, 0xf9, 0x3f,
	0xc5, 0x41, 0x5f, 0xa0, 0x09, 0x41, 0x00, 0x40,
	0x00, 0x50, 0x05, 0xf4, 0xa0, 0x01, 0x12, 0x00,
	0x52, 0x20, 0x24, 0xa9, 0x3f, 0xd0, 0xff, 0xf8,
	0x1f, 0xd0, 0x14, 0x1f, 0x44, 0x80, 0x04, 0x4a,
	0x04, 0x44, 0x02, 0x40, 0xfa, 0x05, 0x10, 0x40,
	0x94, 0x00, 0x09, 0x00, 0x00, 0x1f, 0xa4, 0x7f,
	0xfa, 0xaf, 0x41, 0x00, 0x9f, 0x10, 0x2a, 0x50,
	0x00, 0x91, 0x11, 0x48, 0x14, 0xf8, 0x20, 0x45,
	0x0a, 0x01, 0x25, 0x40, 0x4a, 0x92, 0x9f, 0xc0,
	0xff, 0xf8, 0x1f, 0xc8, 0x48, 0x3f, 0xc2, 0x80,
	0x02, 0x52, 0x00, 0x00, 0x01, 0x41, 0xec, 0x89,
	0x00, 0x20, 0x48, 0x00, 0x15, 0x00, 0x00, 0x2f,
	0xd2, 0x7f, 0xf8, 0x5f, 0xc2, 0x02, 0x9f, 0x00,
	0x12, 0x88, 0x00, 0x49, 0x4a, 0x48, 0x08, 0xfa,
	0x00, 0x29, 0x04, 0x02, 0xa9, 0x00, 0x29, 0x29,
	0x1e, 0xc0, 0xbf, 0xf9, 0x2f, 0x48, 0xa8, 0x1f,
	0x4a, 0x40, 0x21, 0x29, 0x00, 0x00, 0x02, 0x41,
	0xb8, 0x54, 0x80, 0x41, 0x50, 0x00, 0x48, 0x80,
	0x00, 0x5f, 0xd4, 0xff, 0xf8, 0x2f, 0xc0, 0x00,
	0xbb, 0x40, 0x09, 0x04, 0x00, 0x2a, 0x52, 0xa8,
	0x14, 0xfd, 0x00, 0x09, 0x14, 0x04, 0x4a, 0x02,
	0x12, 0xa5, 0x1f, 0xc0, 0x7f, 0xf9, 0x3f, 0xd5,
	0x12, 0x1f, 0x89, 0x20, 0x20, 0x92, 0x80, 0x00,
	0x01, 0x00, 0xf8, 0x22, 0x40, 0x00, 0x41, 0x00,
	0xa0, 0x40, 0x00, 0x2f, 0xd2, 0xbf, 0xf8, 0x1f,
	0x40, 0x40, 0x5f, 0x40, 0x04, 0x88, 0x00, 0x11,
	0x24, 0x88, 0x49, 0xfa, 0x08, 0x14, 0xa5, 0x10,
	0x24, 0x0a, 0x09, 0x29, 0x1f, 0xa0, 0xff, 0xfa,
	0xaf, 0xc4, 0x09, 0x1f, 0x4a, 0x48, 0x02, 0xa9,
	0x04, 0x00, 0x22, 0x00, 0xf8, 0xa1, 0x40, 0x00,
	0x04, 0x81, 0x20, 0x90, 0x00, 0x5e, 0xc4, 0x7f,
	0xf8, 0x1f, 0xd1, 0x20, 0x5f, 0x00, 0x01, 0x50,
	0x00, 0x41, 0x55, 0x00, 0xa9, 0xec, 0x04, 0x05,
	0x28, 0xa1, 0x10, 0x04, 0x02, 0x94, 0x1f, 0xe0,
	0xff, 0xf8, 0x5f, 0x40, 0x04, 0x1f, 0xa4, 0xa4,
	0x04, 0x8a, 0x14, 0x00, 0x4a, 0x00, 0xfa, 0x40,
	0x90, 0x02, 0x08, 0x05, 0x40, 0x90, 0x01, 0x5f,
	0xc4, 0xbf, 0xf9, 0x2f, 0xc9, 0x51, 0x5f, 0x40,
	0x00, 0x80, 0x20, 0x80, 0x22, 0x00, 0x25, 0xb8,
	0x12, 0x02, 0x48, 0x42, 0x50, 0x12, 0x04, 0xa4,
	0x1f, 0xa0, 0xff, 0xf8, 0x2f, 0xc4, 0x00, 0x1f,
	0x0a, 0x54, 0x2a, 0x04, 0x24, 0x88, 0x52, 0x00,
	0xfd, 0x40, 0x48, 0x01, 0x08, 0x02, 0x04, 0x40,
	0x00, 0x5b, 0xd4, 0x7f, 0xf9, 0x3f, 0x50, 0x4a,
	0x5f, 0xa0, 0x01, 0x00, 0x90, 0x80, 0x01, 0x00,
	0xa4, 0xf8, 0x09, 0x01, 0x54, 0x21, 0x48, 0xa0,
	0x15, 0x29, 0x1f, 0xc0, 0xff, 0xf8, 0x1f, 0xc1,
	0x00, 0x1f, 0x42, 0x90, 0x48, 0x02, 0x12, 0xa4,
	0x12, 0x01, 0xfa, 0x40, 0x24, 0x00, 0x84, 0x00,
	0x09, 0x40, 0x00, 0x3f, 0xd2, 0x7f, 0xf9, 0x2f,
	0xa4, 0x24, 0xbf, 0x08, 0x04, 0x02, 0x50, 0x40,
	0x00, 0x80, 0x48, 0xf8, 0x15, 0x08, 0x88, 0x10,
	0x95, 0x20, 0x12, 0xa5, 0x17, 0xa0, 0xbf, 0xf8,
	0x3f, 0xc0, 0x80, 0x1b, 0x81, 0x21, 0x48, 0x04,
	0x0a, 0x24, 0x29, 0x05, 0xfd, 0x40, 0x40, 0x22,
	0x42, 0x00, 0x05, 0x00, 0x00, 0x5f, 0xc4, 0xff,
	0xf9, 0x1d, 0xa4, 0x25, 0x2f, 0x54, 0x08, 0x01,
	0x41, 0x20, 0x81, 0x00, 0x20, 0xd8, 0x08, 0x12,
	0x00, 0x08, 0x52, 0x40, 0x52, 0x29, 0x1f, 0xd0,
	0xbf, 0xf8, 0x5f, 0xc1, 0x00, 0x1f, 0x80, 0xa2,
	0x50, 0x14, 0x04, 0x14, 0x4a, 0x44, 0xfa, 0x22,
	0x80, 0xaa, 0xa1, 0x00, 0x12, 0x00, 0x80, 0x1f,
	0xc4, 0xff, 0xfa, 0x2f, 0xc8, 0x49, 0x5f, 0x44,
	0x00, 0x05, 0x00, 0xa0, 0x80, 0x00, 0x11, 0xf8,
	0x80, 0x24, 0x00, 0x04, 0x12, 0xed, 0x4a, 0x12,
	0x5f, 0x60, 0x7f, 0xf8, 0x9f, 0xa2, 0x00, 0x1f,
	0x91, 0x15, 0x20, 0x48, 0x08, 0x22, 0x92, 0x40,
	0xfc, 0x12, 0x02, 0x44, 0x40, 0x81, 0x5b, 0x40,
	0x40, 0x1f, 0xc4, 0xff, 0xf8, 0x2f, 0xc0, 0xaa,
	0x5f, 0x40, 0x40, 0x09, 0x02, 0x82, 0x88, 0x00,
	0x09, 0xb4, 0x80, 0x90, 0x11, 0x12, 0x16, 0xff,
	0xd2, 0x09, 0x5f, 0xd0, 0xbf, 0xf9, 0x2f, 0xa4,
	0x00, 0x3f, 0x09, 0x09, 0x20, 0x20, 0x20, 0x01,
	0x2a, 0x80, 0xfa, 0x2a, 0x05, 0x40, 0x00, 0x45,
	0xff, 0xd0, 0x90, 0x1d, 0xc4, 0xff, 0xf8, 0x3f,
	0xc1, 0x11, 0x1f, 0xa0, 0x20, 0x04, 0x8a, 0x0a,
	0x50, 0x00, 0x25, 0xf8, 0x80, 0x50, 0x0a, 0x52,
	0x0f, 0xff, 0xe4, 0x02, 0x5f, 0xd0, 0x7f, 0xfa,
	0x1f, 0x48, 0x44, 0x5f, 0x44, 0x84, 0xa0, 0x00,
	0xa0, 0x05, 0x48, 0x80, 0xfc, 0x09, 0x00, 0x40,
	0x00, 0x97, 0xff, 0xf0, 0xa0, 0x1f, 0xc2, 0xbf,
	0xf8, 0xaf, 0xc2, 0x00, 0x1f, 0x00, 0x10, 0x0a,
	0x52, 0x04, 0x90, 0x02, 0x12, 0xf5, 0x40, 0x25,
	0x12, 0xaa, 0x17, 0xed, 0xfa, 0x0a, 0x9f, 0xa0,
	0xff, 0xf8, 0x2f, 0xd0, 0x92, 0xbf, 0xa5, 0x42,
	0x80, 0x00, 0x20, 0x02, 0x50, 0x41, 0xfd, 0x2d,
	0x94, 0xa9, 0x11, 0x6f, 0xa7, 0xe8, 0x20, 0x1f,
	0xd4, 0x7f, 0xf9, 0x1f, 0xa4, 0x08, 0x1b, 0x40,
	0x08, 0x24, 0x94, 0x8a, 0xa0, 0x05, 0x08, 0xbe,
	0xf6, 0xfb, 0xef, 0xff, 0xbf, 0xc9, 0xf4, 0x84,
	0x5f, 0xc0, 0xff, 0xf8, 0x5f, 0xc1, 0x41, 0x1f,
	0x09, 0x21, 0x00, 0x20, 0x20, 0x09, 0x20, 0x22,
	0xff, 0xff, 0xef, 0xbf, 0xef, 0xff, 0x03, 0x78,
	0x11, 0x1d, 0xa4, 0xbf, 0xfa, 0x2f, 0x44, 0x10,
	0x5f, 0xc0, 0x04, 0x2a, 0x82, 0x81, 0x20, 0x09,
	0x0b, 0xfb, 0xff, 0xff, 0xfe, 0xff, 0xff, 0xa1,
	0xf4, 0x80, 0x37, 0xd0, 0xff, 0xf8, 0x3f, 0xd0,
	0x44, 0x1f, 0x14, 0x90, 0x80, 0x10, 0x14, 0x05,
	0x40, 0x42, 0xff, 0xdf, 0xbf, 0xff, 0xfd, 0xff,
	0x4b, 0xf0, 0x24, 0x9f, 0xc2, 0x7f, 0xf9, 0x1f,
	0xc2, 0x01, 0x5f, 0x80, 0x02, 0x11, 0x04, 0x80,
	0x90, 0x14, 0x17, 0xfd, 0x75, 0xf5, 0x6b, 0x57,
	0x57, 0x82, 0xfd, 0x02, 0x1f, 0xd0, 0xbf, 0xf8,
	0x5d, 0xa0, 0x94, 0x1f, 0x4a, 0xa8, 0x44, 0x50,
	0x22, 0x02, 0x41, 0x2b, 0xd2, 0x8a, 0x0a, 0x94,
	0xa8, 0x9f, 0xf7, 0xf0, 0x48, 0x5f, 0xc4, 0xff,
	0xf9, 0x1f, 0xc9, 0x00, 0xbf, 0x40, 0x01, 0x00,
	0x02, 0x08, 0x48, 0x08, 0x0f, 0xe8, 0x00, 0x00,
	0x00, 0x00, 0x17, 0xdf, 0xf4, 0x01, 0x1f, 0xd0,
	0x7f, 0xf8, 0x2f, 0xc0, 0x24, 0x1f, 0x08, 0x88,
	0x29, 0x48, 0xa1, 0x01, 0x21, 0x5f, 0x00, 0x48,
	0xa4, 0x42, 0x0a, 0x4f, 0xff, 0xd1, 0x54, 0x1f,
	0xa2, 0xbf, 0xf9, 0x3f, 0xa4, 0x81, 0x1f, 0xa2,
	0x22, 0x00, 0x00, 0x04, 0x28, 0x04, 0x3f, 0x89,
	0x02, 0x01, 0x10, 0xa0, 0x17, 0x7f, 0xf0, 0x00,
	0x9f, 0xc0, 0xff, 0xf8, 0x1f, 0xc1, 0x14, 0x5d,
	0x40, 0x00, 0xa5, 0x25, 0x20, 0x82, 0xa1, 0x7e,
	0x40, 0x50, 0x54, 0x04, 0x02, 0x45, 0xfe, 0xc5,
	0x22, 0x3e, 0xd4, 0x7f, 0xfa, 0x5d, 0xa4, 0x00,
	0x1f, 0x89, 0x4a, 0x00, 0x00, 0x08, 0x10, 0x08,
	0xfd, 0x12, 0x04, 0x00, 0xa1, 0x28, 0x05, 0xff,
	0xc0, 0x08, 0x17, 0xc0, 0xff, 0xf8, 0x2f, 0xc0,
	0xa4, 0xbf, 0x40, 0x00, 0x49, 0x54, 0xa2, 0x84,
	0x42, 0xfd, 0x40, 0x81, 0x22, 0x08, 0x01, 0x52,
	0xfb, 0x49, 0x21, 0x5f, 0xe4, 0xbf, 0xf9, 0x2f,
	0xc9, 0x00, 0x1f, 0x0a, 0x52, 0x00, 0x00, 0x00,
	0x21, 0x09, 0xf2, 0x0a, 0x28, 0x08, 0x21, 0x24,
	0x00, 0x85, 0x00, 0x04, 0x1f, 0xa0, 0xff, 0xf8,
	0x3f, 0xa0, 0x12, 0x5f, 0xa0, 0x00, 0x94, 0x89,
	0x29, 0x08, 0x47, 0xf4, 0x20, 0x02, 0xa1, 0x04,
	0x00, 0x44, 0x54, 0x29, 0x40, 0x5f, 0xc4, 0xbf,
	0xf9, 0x1f, 0xc4, 0x80, 0x1d, 0x82, 0x48, 0x00,
	0x20, 0x00, 0x42, 0x0b, 0xa0, 0x82, 0x90, 0x04,
	0x41, 0x52, 0x11, 0x00, 0x80, 0x15, 0x1f, 0xd0,
	0xff, 0xf8, 0x5f, 0x40, 0x2a, 0xaf, 0x48, 0x02,
	0x49, 0x04, 0x92, 0x08, 0x2f, 0xd0, 0x10, 0x04,
	0x90, 0x14, 0x01, 0x00, 0x24, 0x25, 0x00, 0x1f,
	0xc4, 0x7f, 0xf9, 0x1d, 0xd5, 0x00, 0x1f, 0x01,
	0x20, 0x00, 0x40, 0x00, 0x81, 0x2e, 0xa5, 0x24,
	0xa0, 0x02, 0x40, 0x14, 0x4a, 0x11, 0x00, 0x52,
	0x5f, 0x60, 0xff, 0xf8, 0x2f, 0xc0, 0x44, 0x5f,
	0xd4, 0x95, 0x55, 0x2a, 0xaa, 0x54, 0xbf, 0x00,
	0x00, 0x09, 0x48, 0x09, 0x40, 0x00, 0x80, 0x29,
	0x00, 0x1d, 0xc4, 0xbf, 0xfa, 0x3f, 0xc9, 0x11,
	0x1f, 0x52, 0x49, 0x54, 0x95, 0x55, 0x2a, 0x5d,
	0x11, 0x49, 0x20, 0x01, 0x40, 0x09, 0x52, 0x15,
	0x00, 0x25, 0x5f, 0xd0, 0xff, 0xf8, 0x9f, 0xa0,
	0x00, 0x5f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
	0xfd, 0x24, 0x00, 0x04, 0x94, 0x12, 0x40, 0x00,
	0x80, 0x4a, 0x00, 0x1f, 0xc4, 0x7f, 0xf8, 0x2f,
	0xc4, 0xa4, 0x1f, 0xff, 0xff, 0xff, 0xff, 0xff,
	0xff, 0xfa, 0x00, 0x95, 0x40, 0x00, 0x80, 0x12,
	0x4a, 0x24, 0x80, 0xa9, 0x1f, 0xd0, 0xbf, 0xf9,
	0x2f, 0xa0, 0x01, 0x5f, 0xff, 0xff, 0xf7, 0xff,
	0xef, 0xff, 0xfc, 0x4a, 0x00, 0x15, 0x52, 0x15,
	0x04, 0x00, 0x00, 0x24, 0x00, 0x5f, 0xc2, 0xff,
	0xf8, 0x3f, 0xc5, 0x24, 0x1f, 0x42, 0x10, 0x14,
	0x90, 0x28, 0x01, 0x74, 0x00, 0x52, 0x40, 0x00,
	0x40, 0x50, 0xa4, 0x95, 0x01, 0x24, 0x1f, 0xa8,
	0x7f, 0xf9, 0x1f, 0x40, 0x00, 0xbf, 0x94, 0xa5,
	0xa2, 0x4a, 0x92, 0xad, 0xf9, 0x24, 0x00, 0x08,
	0x92, 0x04, 0x02, 0x00, 0x20, 0x54, 0x01, 0x57,
	0xc0, 0xff, 0xf8, 0x5d, 0xd4, 0x92, 0x1f, 0x40,
	0x00, 0x08, 0x00, 0x00, 0x00, 0xbc, 0x01, 0x4a,
	0x82, 0x00, 0x91, 0x20, 0x4a, 0x81, 0x00, 0x94,
	0x1f, 0xd4, 0xbf, 0xfa, 0x2f, 0xc0, 0x00, 0x5f,
	0x04, 0x92, 0x21, 0x24, 0x92, 0x48, 0xf8, 0x94,
	0x00, 0x20, 0x4a, 0x00, 0x09, 0x00, 0x08, 0x12,
	0x00, 0x9f, 0x40, 0xff, 0xf8, 0x2f, 0xc9, 0x54,
	0x1b, 0xa0, 0x00, 0x84, 0x00, 0x40, 0x02, 0xfc,
	0x00, 0x92, 0x0a, 0x00, 0x54, 0xa0, 0x24, 0x42,
	0x40, 0x52, 0x1f, 0xe4, 0x7f, 0xf9, 0x2f, 0xc0,
	0x01, 0x5f, 0x44, 0xa8, 0x10, 0x92, 0x0a, 0xa8,
	0xb4, 0xa4, 0x08, 0xa0, 0xa9, 0x00, 0x05, 0x01,
	0x10, 0x09, 0x00, 0x17, 0xa0, 0xff, 0xf8, 0x3f,
	0xa4, 0x88, 0x1f, 0x02, 0x02, 0x42, 0x21, 0x20,
	0x01, 0x78, 0x01, 0x40, 0x04, 0x00, 0x22, 0x90,
	0x50, 0x05, 0x40, 0x24, 0x9f, 0xc4, 0xbf, 0xf9,
	0x1f, 0xc0, 0x21, 0x3f, 0xd0, 0x20, 0x08, 0x08,
	0x04, 0x44, 0xfd, 0x28, 0x12, 0x91, 0x24, 0x88,
	0x02, 0x04, 0xa0, 0x14, 0x80, 0x5f, 0xd0, 0xff,
	0xf8, 0x5f, 0xaa, 0x84, 0x1f, 0x04, 0x89, 0x41,
	0x21, 0x21, 0x11, 0x74, 0x02, 0x40, 0x00, 0x00,
	0x01, 0x28, 0x42, 0x09, 0x00, 0x12, 0x1f, 0xa4,
	0x7f, 0xf9, 0x1e, 0xc0, 0x10, 0x9f, 0x40, 0x00,
	0x10, 0x04, 0x08, 0x00, 0xf8, 0x90, 0x09, 0x24,
	0x95, 0x50, 0x01, 0x10, 0x40, 0x49, 0x41, 0x17,
	0xc0, 0xff, 0xf8, 0x2f, 0xc4, 0x42, 0x3f, 0x4a,
	0xa5, 0x05, 0x40, 0xa2, 0x4a, 0xbc, 0x04, 0x80,
	0x02, 0x00, 0x04, 0x90, 0x04, 0x12, 0x00, 0x08,
	0x5f, 0xd2, 0x7f, 0xf9, 0x3f, 0xc8, 0x88, 0x1f,
	0x40, 0x00, 0x50, 0x12, 0x00, 0x10, 0xf4, 0x90,
	0x2a, 0xa8, 0x52, 0x10, 0x44, 0xa1, 0x40, 0xa5,
	0x22, 0x1f, 0xa0, 0xbf, 0xf8, 0x1f, 0xa0, 0x01,
	0x5d, 0x88, 0x94, 0x01, 0x00, 0x95, 0x21, 0x78,
	0x02, 0x80, 0x01, 0x00, 0x82, 0x01, 0x08, 0x0a,
	0x00, 0x00, 0x9e, 0xc8, 0xff, 0xfa, 0xaf, 0xc5,
	0x24, 0x1f, 0x42, 0x01, 0x48, 0x4a, 0x00, 0x04,
	0xfd, 0x48, 0x11, 0x10, 0x24, 0x20, 0x94, 0x02,
	0x20, 0x29, 0x4a, 0x17, 0xe2, 0x7f, 0xf8, 0x1f,
	0x40, 0x00, 0x5f, 0x48, 0x48, 0x02, 0x00, 0x48,
	0x91, 0x74, 0x01, 0x04, 0x44, 0x81, 0x0a, 0x00,
	0x50, 0x84, 0x80, 0x00, 0x1f, 0xc0, 0xbf, 0xf8,
	0x5f, 0xd2, 0x52, 0x1f, 0x41, 0x02, 0x48, 0x92,
	0x02, 0x00, 0xf8, 0x54, 0x40, 0x00, 0x14, 0x40,
	0x49, 0x04, 0x10, 0x12, 0x52, 0x9f, 0xd4, 0xff,
	0xf9, 0x2f, 0xc0, 0x00, 0xbf, 0x04, 0x20, 0x00,
	0x00, 0xa0, 0xa5, 0x7a, 0x00, 0x12, 0x49, 0x00,
	0x11, 0x00, 0x21, 0x02, 0x80, 0x00, 0x1f, 0xc0,
	0x7f, 0xf8, 0x2f, 0x4a, 0x94, 0x1f, 0xa0, 0x8a,
	0x55, 0x2a, 0x0a, 0x00, 0xec, 0x92, 0x88, 0x84,
	0xa9, 0x44, 0x25, 0x04, 0x50, 0x2a, 0x92, 0x5f,
	0xa4, 0xff, 0xfa, 0x3f, 0xc0, 0x01, 0x2f, 0x48,
	0x00, 0x00, 0x00, 0x20, 0x29, 0x7a, 0x54, 0xa5,
	0x55, 0x25, 0x2a, 0x00, 0x48, 0x04, 0x80, 0x00,
	0x17, 0xd0, 0xbf, 0xf8, 0x9f, 0xd2, 0x48, 0x1f,
	0x02, 0x52, 0x91, 0x49, 0x04, 0x80, 0xff, 0xff,
	0xff, 0xff, 0xff, 0xfd, 0x54, 0x01, 0x20, 0x08,
	0xa9, 0x1f, 0xc2, 0x7f, 0xf8, 0x2f, 0x40, 0x01,
	0x5f, 0xa0, 0x80, 0x04, 0x00, 0x40, 0x0a, 0xbf,
	0xef, 0xbf, 0x7b, 0xfd, 0xde, 0x01, 0x50, 0x0a,
	0x42, 0x00, 0x9f, 0xa4, 0xff, 0xfa, 0x2f, 0xc9,
	0x50, 0x1f, 0x4a, 0x14, 0xa0, 0x4a, 0x15, 0x20,
	0xff, 0xfe, 0xff, 0xff, 0xbf, 0xfa, 0x24, 0x05,
	0x40, 0x10, 0x44, 0x1f, 0xd0, 0x7f, 0xf8, 0x9f,
	0xc2, 0x04, 0xbf, 0x00, 0x40, 0x09, 0x00, 0x80,
	0x09, 0x7f, 0x7f, 0xfd, 0xff, 0xff, 0xff, 0x00,
	0x40, 0x12, 0x84, 0x11, 0x2e, 0xc0, 0xbf, 0xf8,
	0x3f, 0xa8, 0x20, 0x1f, 0xa2, 0x04, 0x80, 0x24,
	0x24, 0xa0, 0xfb, 0xd5, 0xd7, 0x55, 0x55, 0x54,
	0x52, 0x12, 0x40, 0x21, 0x00, 0x1f, 0xd4, 0xff,
	0xfa, 0x1f, 0xc1, 0x05, 0x3f, 0x44, 0x40, 0x25,
	0x01, 0x00, 0x05, 0x78, 0x02, 0x00, 0x00, 0x20,
	0x02, 0x00, 0x80, 0x09, 0x08, 0x55, 0x37, 0xc0,
	0xbf, 0xf8, 0xae, 0xc4, 0x20, 0x1b, 0x00, 0x15,
	0x08, 0x54, 0x29, 0x20, 0xfc, 0x28, 0x29, 0x55,
	0x05, 0x28, 0x4a, 0x2a, 0x80, 0x42, 0x00, 0x1f,
	0xd2, 0x7f, 0xf8, 0x2f, 0xc8, 0x89, 0x5f, 0xa9,
	0x40, 0x40, 0x00, 0x80, 0x09, 0x74, 0x80, 0x80,
	0x00, 0x40, 0x01, 0x00, 0x00, 0x2a, 0x10, 0x91,
	0x1f, 0xc0, 0xff, 0xfa, 0x3f, 0xc0, 0x00, 0x1f,
	0x40, 0x09, 0x12, 0x92, 0x0a, 0x80, 0xf8, 0x08,
	0x09, 0x11, 0x12, 0x48, 0x24, 0x91, 0x00, 0x84,
	0x04, 0x5f, 0xa4, 0xbf, 0xf8, 0x9f, 0xa5, 0x24,
	0x9f, 0x04, 0x80, 0x00, 0x00, 0x40, 0x2a, 0xdd,
	0x22, 0x40, 0x44, 0x00, 0x02, 0x00, 0x04, 0x24,
	0x21, 0x50, 0x17, 0xd0, 0xff, 0xf8, 0x2f, 0xc0,
	0x00, 0x3f, 0xc8, 0x29, 0x49, 0x24, 0x11, 0x00,
	0xf8, 0x08, 0x25, 0x00, 0x92, 0xa0, 0xaa, 0x40,
	0x80, 0x84, 0x02, 0x9f, 0xc4, 0x7f, 0xfa, 0x2f,
	0x49, 0x55, 0x1f, 0x02, 0x40, 0x00, 0x01, 0x44,
	0x22, 0x7c, 0xa1, 0x00, 0x28, 0x00, 0x08, 0x00,
	0x28, 0x12, 0x10, 0x48, 0x1f, 0xa0, 0xff, 0xf8,
	0x9f, 0xc0, 0x00, 0x5f, 0x50, 0x0a, 0x55, 0x54,
	0x00, 0x89, 0x74, 0x04, 0x52, 0x02, 0xa9, 0x22,
	0x89, 0x02, 0x80, 0x42, 0x01, 0x1f, 0xd2, 0x7f,
	0xf8, 0x2f, 0xd4, 0x89, 0x1f, 0x42, 0x80, 0x00,
	0x00, 0x52, 0x00, 0xf9, 0x10, 0x00, 0x90, 0x00,
	0x00, 0x20, 0x48, 0x2a, 0x08, 0x90, 0x57, 0xc0,
	0xbf, 0xf9, 0x3f, 0xa0, 0x20, 0x3f, 0x48, 0x24,
	0x88, 0x89, 0x00, 0x52, 0xfc, 0x42, 0x94, 0x04,
	0x45, 0x29, 0x04, 0x01, 0x00, 0x80, 0x04, 0x1f,
	0xa4, 0xff, 0xf8, 0x1f, 0xc2, 0x84, 0x9b, 0x80,
	0x80, 0x22, 0x20, 0x12, 0x00, 0xb4, 0x08, 0x01,
	0x51, 0x10, 0x00, 0x41, 0x48, 0x44, 0x25, 0x51,
	0x1f, 0xd0, 0xbf, 0xfa, 0x5f, 0x48, 0x10, 0x1f,
	0x44, 0x15, 0x00, 0x04, 0x80, 0x95, 0x79, 0x20,
	0x90, 0x00, 0x02, 0x4a, 0x14, 0x02, 0x10, 0x80,
	0x00, 0x9f, 0xc4, 0xff, 0xf8, 0x2f, 0xc1, 0x02,
	0x5f, 0x11, 0x20, 0x49, 0x20, 0x2a, 0x00, 0xfc,
	0x02, 0x04, 0x89, 0x48, 0x00, 0x80, 0x90, 0x82,
	0x14, 0x8a, 0x2e, 0xd0, 0x7f, 0xf9, 0x2f, 0xc8,
	0x48, 0x1f, 0xc0, 0x02, 0x00, 0x8a, 0x80, 0x49,
	0x78, 0x90, 0x40, 0x20, 0x01, 0x52, 0x12, 0x04,
	0x20, 0x40, 0x20, 0x1f, 0xc2, 0xbf, 0xf8, 0x3f,
	0x42, 0x01, 0x5f, 0x0a, 0x90, 0x54, 0x00, 0x09,
	0x00, 0xfa, 0x05, 0x15, 0x04, 0x94, 0x00, 0x40,
	0x50, 0x8a, 0x09, 0x04, 0x9f, 0xd0, 0xff, 0xf9,
	0x1f, 0xd0, 0xa4, 0x1f, 0x40, 0x04, 0x81, 0x24,
	0x40, 0x2a, 0xec, 0x50, 0x00, 0x42, 0x00, 0x49,
	0x09, 0x02, 0x00, 0xa0, 0x40, 0x5f, 0xc4, 0x7f,
	0xf8, 0x5f, 0xc4, 0x00, 0x9f, 0x92, 0x50, 0x08,
	0x01, 0x12, 0x00, 0xb8, 0x02, 0x52, 0x10, 0x49,
	0x00, 0x20, 0x10, 0x52, 0x05, 0x12, 0x1f, 0xa0,
	0xbf, 0xfa, 0x2f, 0x40, 0x94, 0x3f, 0x40, 0x02,
	0x41, 0x50, 0x00, 0xa2, 0xfd, 0x48, 0x00, 0x84,
	0x00, 0x24, 0x84, 0x85, 0x00, 0x20, 0x04, 0x5d,
	0xd2, 0xff, 0xf8, 0x9f, 0xd4, 0x01, 0x1f, 0x09,
	0x20, 0x10, 0x04, 0xaa, 0x08, 0xf8, 0x01, 0x48,
	0x21, 0x54, 0x81, 0x10, 0x20, 0x14, 0x89, 0x50,
	0x1f, 0xc0, 0x7f, 0xf8, 0x2f, 0xc1, 0x24, 0x5d,
	0xa0, 0x0a, 0x85, 0x10, 0x00, 0x41, 0x7c, 0x52,
	0x02, 0x88, 0x00, 0x14, 0x02, 0x88, 0x80, 0x20,
	0x02, 0x9f, 0xd4, 0xbf, 0xf9, 0x3f, 0x44, 0x00,
	0x1f, 0x45, 0x40, 0x20, 0x42, 0x91, 0x10, 0xbb,
	0x00, 0x50, 0x02, 0x45, 0x20, 0x90, 0x02, 0x22,
	0x84, 0x88, 0x37, 0xc0, 0xff, 0xf8, 0x1f, 0xd0,
	0xa9, 0x5f, 0x80, 0x12, 0x09, 0x08, 0x04, 0x04,
	0x7e, 0x14, 0x04, 0x90, 0x10, 0x02, 0x04, 0xa0,
	0x88, 0x10, 0x20, 0x9f, 0xa4, 0xbf, 0xf9, 0x2f,
	0xa0, 0x00, 0x1f, 0x49, 0x00, 0x80, 0x21, 0x20,
	0xa1, 0x3f, 0x81, 0x40, 0x04, 0x81, 0x20, 0x50,
	0x0a, 0x01, 0x02, 0x04, 0x1f, 0xd0, 0xff, 0xf8,
	0x3e, 0xc5, 0x14, 0xbf, 0x00, 0x48, 0x29, 0x04,
	0x08, 0x08, 0x2e, 0xa4, 0x15, 0x50, 0x24, 0x0a,
	0x05, 0x20, 0x24, 0x48, 0xa1, 0x5f, 0xc2, 0x7f,
	0xfa, 0x9f, 0xc0, 0x40, 0x1b, 0xa4, 0x12, 0x80,
	0x40, 0x82, 0x82, 0xbf, 0xa0, 0x80, 0x02, 0x00,
	0x80, 0x90, 0x02, 0x08, 0x02, 0x08, 0x1f, 0xa4,
	0xbf, 0xf8, 0x2f, 0xc9, 0x0a, 0x5f, 0x42, 0x80,
	0x12, 0x12, 0x20, 0x20, 0x07, 0xe4, 0x24, 0x48,
	0xaa, 0x28, 0x02, 0x48, 0xa1, 0x50, 0x42, 0x2e,
	0xd0, 0xff, 0xf8, 0x9f, 0xa0, 0x20, 0x1f, 0x08,
	0x24, 0x80, 0x80, 0x09, 0x09, 0x0b, 0xd1, 0x01,
	0x00, 0x00, 0x02, 0x48, 0x02, 0x04, 0x02, 0x10,
	0x9f, 0xc0, 0x7f, 0xfa, 0x2f, 0xc4, 0x81, 0x5f,
	0xa0, 0x80, 0x24, 0x29, 0x40, 0x40, 0x42, 0xf4,
	0x24, 0x25, 0x21, 0x48, 0x21, 0x50, 0x20, 0x10,
	0x84, 0x1f, 0xd4, 0xbf, 0xf8, 0x3f, 0x41, 0x12,
	0xbf, 0xea, 0x14, 0x81, 0x00, 0x15, 0x15, 0x11,
	0xf9, 0x08, 0x80, 0x08, 0x01, 0x04, 0x05, 0xdf,
	0xa2, 0x10, 0xbf, 0xc0, 0xff, 0xf9, 0x1f, 0xd4,
	0x08, 0xff, 0x70, 0x40, 0x14, 0x4a, 0x40, 0x00,
	0x04, 0xfc, 0x40, 0x12, 0x42, 0x48, 0x40, 0x93,
	0xfe, 0x80, 0x42, 0x17, 0xd2, 0x7f, 0xf8, 0x2f,
	0xc0, 0x83, 0xff, 0xfd, 0x09, 0x40, 0x00, 0x08,
	0xa4, 0x91, 0x2f, 0x09, 0x00, 0x10, 0x02, 0x14,
	0x0f, 0xff, 0xf5, 0x08, 0x1f, 0xa0, 0xbf, 0xf9,
	0x2f, 0x44, 0x2b, 0xff, 0xfa, 0x20, 0x05, 0x24,
	0xa2, 0x00, 0x20, 0x7f, 0x40, 0x54, 0x84, 0xa8,
	0x81, 0x26, 0xff, 0xd0, 0x21, 0x5f, 0xc4, 0xff,
	0xf8, 0x3f, 0xd0, 0x87, 0xff, 0xfc, 0x05, 0x20,
	0x00, 0x00, 0x4a, 0x04, 0x1f, 0x12, 0x00, 0x20,
	0x00, 0x10, 0x1f, 0xff, 0xf0, 0x84, 0x1f, 0xd0,
	0xbf, 0xfa, 0x1f, 0xc2, 0x0b, 0xe9, 0xfa, 0x50,
	0x09, 0x55, 0x24, 0x00, 0x91, 0x2f, 0xc0, 0x92,
	0x85, 0x24, 0x84, 0xaf, 0xcb, 0xe8, 0x10, 0x5f,
	0xa2, 0x7f, 0xf8, 0xaf, 0x48, 0x4b, 0xea, 0xec,
	0x02, 0x40, 0x00, 0x01, 0x52, 0x00, 0x8f, 0x44,
	0x00, 0x10, 0x00, 0x20, 0x3f, 0xd7, 0xb2, 0x42,
	0x1e, 0xc0, 0xff, 0xf8, 0x2f, 0xc1, 0x0b, 0xa0,
	0xfe, 0x90, 0x14, 0x92, 0x54, 0x00, 0xaa, 0x13,
	0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x42, 0xf4,
	0x08, 0x9f, 0xe8, 0xbf, 0xf9, 0x2f, 0xd0, 0x27,
	0xe2, 0xfa, 0x05, 0x40, 0x00, 0x00, 0x88, 0x00,
	0x45, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x0b,
	0xf0, 0xa0, 0x2f, 0xc2, 0x7f, 0xf8, 0x3f, 0xc5,
	0x0b, 0xc8, 0xbc, 0x40, 0x09, 0x49, 0x48, 0x22,
	0x44, 0x01, 0x7f, 0xff, 0xff, 0xff, 0xff, 0xf7,
	0x41, 0xf4, 0x05, 0x1f, 0xa0, 0xff, 0xfa, 0x9f,
	0xa0, 0x47, 0xa0, 0xfa, 0x12, 0x20, 0x00, 0x02,
	0x08, 0x11, 0x24, 0xff, 0xff, 0xf7, 0xff, 0xff,
	0xff, 0x49, 0xf1, 0x20, 0x5f, 0xd4, 0xbf, 0xf8,
	0x2f, 0xd2, 0x17, 0xe2, 0xfc, 0x80, 0x84, 0xaa,
	0x48, 0xa1, 0x40, 0x08, 0xef, 0xff, 0xff, 0xff,
	0xef, 0xbf, 0x85, 0xf4, 0x09, 0x1e, 0xc0, 0xff,
	0xf8, 0x2f, 0xf0, 0x8b, 0xfd, 0xfe, 0x28, 0x10,
	0x00, 0x10, 0x04, 0x0a, 0xa1, 0x10, 0x00, 0x12,
	0x00, 0x90, 0xbf, 0xd7, 0xd0, 0xa0, 0x7f, 0xe2,
	0x7f, 0xf9, 0x2f, 0xfa, 0x27, 0xf7, 0xf8, 0x02,
	0x82, 0x44, 0x82, 0x90, 0x80, 0x04, 0x55, 0x6d,
	0xa9, 0x5a, 0x56, 0x5f, 0x57, 0xf4, 0x05, 0xff,
	0xa0, 0xbf, 0xf8, 0x3f, 0xdc, 0x03, 0xff, 0xfa,
	0x90, 0x24, 0x10, 0x20, 0x02, 0x28, 0x90, 0x00,
	0x00, 0x00, 0x01, 0x00, 0x17, 0xff, 0xf1, 0x10,
	0xff, 0xc8, 0xff, 0xfa, 0x1f, 0x7d, 0x4a, 0xff,
	0xe8, 0x04, 0x81, 0x02, 0x8a, 0x48, 0x02, 0x02,
	0x44, 0x80, 0x04, 0x88, 0x11, 0x17, 0xff, 0xd4,
	0x43, 0xfb, 0xc2, 0x7f, 0xf8, 0xaf, 0xff, 0x02,
	0xff, 0xfa, 0xa0, 0x10, 0x50, 0x00, 0x01, 0x48,
	0x48, 0x10, 0x2a, 0xa0, 0x21, 0x44, 0x4f, 0xff,
	0xf0, 0x0b, 0xff, 0xa8, 0xff, 0xf8, 0x0b, 0xff,
	0xa1, 0xb7, 0xa0, 0x0a, 0x44, 0x04, 0xa4, 0xa8,
	0x01, 0x01, 0x42, 0x00, 0x0a, 0x04, 0x00, 0x05,
	0xfe, 0xc2, 0xaf, 0xff, 0x40, 0xbf, 0xfa, 0x87,
	0xff, 0xa4, 0xdd, 0xd1, 0x20, 0x11, 0x40, 0x00,
	0x02, 0x50, 0x24, 0x08, 0xa4, 0x80, 0xa0, 0x29,
	0x2b, 0xb7, 0xa8, 0x0f, 0xef, 0x04, 0xff, 0xf8,
	0x21, 0xff, 0xe0, 0x02, 0x14, 0x02, 0x80, 0x15,
	0x2a, 0x90, 0x04, 0x80, 0x82, 0x00, 0x24, 0x09,
	0x00, 0x80, 0xda, 0x00, 0x7f, 0xfc, 0x20, 0xbf,
	0xf8, 0x8a, 0xff, 0xf4, 0xa8, 0x40, 0x90, 0x25,
	0x00, 0x00, 0x05, 0x20, 0x2a, 0x28, 0x25, 0x01,
	0x20, 0x48, 0x0a, 0x21, 0x45, 0x5f, 0xfa, 0x0a,
	0x7f, 0xf9, 0x01, 0x7d, 0xf4, 0x00, 0x02, 0x04,
	0x80, 0x49, 0x48, 0x90, 0x0a, 0x00, 0x01, 0x00,
	0x54, 0x05, 0x02, 0x40, 0x88, 0x10, 0x7f, 0xf4,
	0x80, 0xff, 0xf8, 0x24, 0x5f, 0xfc, 0x45, 0x50,
	0xa0, 0x12, 0x00, 0x02, 0x01, 0x20, 0x91, 0x48,
	0x52, 0x00, 0x90, 0x28, 0x24, 0x02, 0x43, 0xff,
	0xa8, 0x29, 0x7f, 0xf8, 0x80, 0x5f, 0xfe, 0x10,
	0x04, 0x09, 0x00, 0xaa, 0x48, 0x48, 0x02, 0x04,
	0x02, 0x00, 0x88, 0x01, 0x01, 0x00, 0xa0, 0x09,
	0xff, 0xe1, 0x03, 0xff, 0xfa, 0x12, 0x2f, 0xff,
	0x41, 0x21, 0x00, 0x54, 0x00, 0x01, 0x02, 0x90,
	0x40, 0x90, 0x94, 0x22, 0xa4, 0x48, 0x4a, 0x0a,
	0x47, 0xfd, 0xa4, 0x45, 0xff, 0xfc, 0x40, 0x8b,
	0xef, 0x08, 0x04, 0x4a, 0x01, 0x11, 0x24, 0x28,
	0x04, 0x12, 0x04, 0x01, 0x00, 0x00, 0x01, 0x00,
	0x20, 0x1b, 0xff, 0x80, 0x17, 0xff, 0xfd, 0x08,
	0x0f, 0xff, 0xc2, 0x90, 0x00, 0x88, 0x40, 0x00,
	0x80, 0x91, 0x40, 0x40, 0x90, 0x24, 0x25, 0x24,
	0x22, 0x02, 0x8f, 0xfe, 0x49, 0x1f, 0xff, 0xff,
	0x22, 0x41, 0xbf, 0xf0, 0x45, 0x54, 0x52, 0x15,
	0x54, 0x25, 0x24, 0x15, 0x2a, 0x45, 0x49, 0x48,
	0x91, 0x11, 0x54, 0x2f, 0xf5, 0x00, 0x5f, 0xff,
	0xff, 0x41, 0x12, 0xff, 0xd6, 0xa8, 0x92, 0x89,
	0x52, 0xaa, 0x94, 0x92, 0xa4, 0x92, 0x94, 0x92,
	0x92, 0x4a, 0xaa, 0x49, 0x5f, 0xfc, 0x4a, 0x5f,
	0xff, 0xff, 0xc8, 0x41, 0x7f, 0x7f, 0xff, 0xff,
	0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
	0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xd4, 0x00,
	0xff, 0xff, 0xff, 0xc1, 0x08, 0x7f, 0xff, 0xff,
	0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
	0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf1,
	0x2a, 0xbf, 0xff, 0xff, 0xf4, 0x22, 0x5d, 0xff,
	0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
	0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
	0xd4, 0x01, 0xff, 0xff, 0xff, 0xf0, 0x80, 0x17,
	0xff, 0xdf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
	0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
	0xff, 0x40, 0xa7, 0xff, 0xff, 0xff, 0xfc, 0x14,
	0x9f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xdf, 0xff,
	0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
	0xff, 0xff, 0xa4, 0x0b, 0xfe, 0xff, 0xff, 0xbe,
	0x80, 0x43, 0xff, 0xff, 0xff, 0xff, 0xff, 0x7d,
	0xff, 0xbd, 0xff, 0xf7, 0x7b, 0xdf, 0x7f, 0xfe,
	0xef, 0x7f, 0xfb, 0x81, 0x17, 0xfb, 0xff, 0xff,
	0xfe, 0x12, 0x0b, 0x55, 0x55, 0x55, 0x55, 0x55,
	0xee, 0xaa, 0xd7, 0x52, 0xad, 0xad, 0x6a, 0xd5,
	0x55, 0xb5, 0xaa, 0xad, 0x08, 0x5f, 0xff, 0xff,
	0xff, 0xff, 0x80, 0x80, 0x40, 0x08, 0x00, 0x08,
	0x00, 0x00, 0x00, 0x20, 0x08, 0x10, 0x42, 0x11,
	0x00, 0x02, 0x08, 0x40, 0x00, 0xa2, 0x3f, 0xff,
	0xff, 0xff, 0xff, 0xd4, 0x24, 0x8a, 0xa2, 0xaa,
	0xa2, 0xaa, 0x52, 0xaa, 0x89, 0x42, 0x82, 0x10,
	0x84, 0x2a, 0xa8, 0x42, 0x15, 0x52, 0x01, 0x7f,
	0xff, 0xff, 0xff, 0xdf, 0xa1, 0x00, 0x00, 0x00,
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x08, 0x10,
	0x82, 0x10, 0x00, 0x01, 0x08, 0x00, 0x00, 0x48,
	0xff, 0xff, 0xff, 0xff, 0xfe, 0xf0, 0x54, 0x92,
	0x24, 0x49, 0x24, 0x89, 0x12, 0x20, 0x92, 0x41,
	0x24, 0x20, 0x41, 0x48, 0x84, 0x21, 0x48, 0x12,
	0x03, 0xff, 0xbf, 0xff, 0xff, 0xff, 0xf5, 0x00,
	0x00, 0x81, 0x00, 0x00, 0x20, 0x40, 0x8a, 0x00,
	0x10, 0x01, 0x09, 0x04, 0x02, 0x10, 0x84, 0x02,
	0x80, 0x95, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfc,
	0x02, 0x48, 0x10, 0x24, 0x92, 0x02, 0x08, 0x00,
	0x24, 0x84, 0x88, 0x00, 0x10, 0x48, 0x02, 0x00,
	0x20, 0x24, 0x0b, 0xf7, 0xff, 0xff, 0xff, 0xff,
	0xfb, 0xfd, 0xb7, 0xef, 0xdb, 0x6d, 0xfd, 0xef,
	0xff, 0xdb, 0x76, 0xf7, 0xff, 0xef, 0xb7, 0xfd,
	0xff, 0xdf, 0xb7, 0xef, 0xff, 0xff, 0xff, 0xff,
	0xff, 0xfe, 0xb7, 0xfe, 0xfd, 0xff, 0xff, 0xb7,
	0xbb, 0x6b, 0x7f, 0xdf, 0xbd, 0xbb, 0x7b, 0xfd,
	0xb7, 0x6d, 0xfb, 0xfd, 0xbf, 0xff, 0xff, 0xff};

// orig 37 x 45
const uint16_t icon[37][45] PROGMEM = {
	0xe7fe, 0x3cad, 0x2c2a, 0x2c09, 0x2429, 0x242a, 0x2c09, 0x2c09, 0x2c09, 0x2c09, 0x2c09, 0x2c2a, 0x2c09, 0x242a, 0x2c09, 0x242a, 0x2c4b, 0x8e54, 0xf7be, 0xffff, 0xffff, 0xffff, 0xffff, 0xffff, 0xffff, 0xffff, 0xffff, 0xffbf, 0xffff, 0xffff, 0xffff, 0xffff, 0xffff, 0xffff, 0xffff, 0xffff, 0xcffb, 0x2449, 0x0c47, 0xd7fc, 0xefff, 0xffff, 0xffff, 0xf7ff, 0xffff,
	0x2449, 0x0467, 0x1448, 0x1447, 0x0c87, 0x0488, 0x0467, 0x0c47, 0x0468, 0x0468, 0x0467, 0x0c87, 0x0c47, 0x0468, 0x0c47, 0x0488, 0x0c88, 0x0c47, 0xbf59, 0xf7ff, 0xffff, 0xffff, 0xffff, 0xffff, 0xffff, 0xffff, 0xffff, 0xffff, 0xffff, 0xffbf, 0xf7ff, 0xf7ff, 0xffff, 0xfffe, 0xffff, 0xefff, 0x0c28, 0x0c89, 0x242a, 0xefff, 0xffff, 0xffbf, 0xffff, 0xffff, 0xffff,
	0x1429, 0x146a, 0xdffe, 0xefff, 0xf7ff, 0xf7ff, 0xf7ff, 0xf7ff, 0xf7ff, 0xf7ff, 0xf7ff, 0xf7ff, 0xf7ff, 0xf7ff, 0xefff, 0xf7ff, 0xdffe, 0x146a, 0xa738, 0xffff, 0xffff, 0xffff, 0xffff, 0xffff, 0xffff, 0xffff, 0xffff, 0xffff, 0xffff, 0xffff, 0xffff, 0xffff, 0xf7bf, 0xffff, 0xd7fd, 0x1449, 0x0488, 0x8eb6, 0xffff, 0xffff, 0xffbf, 0xffbe, 0xffff, 0xfffe, 0xffff,
	0x1448, 0x0c29, 0xf7ff, 0xffff, 0xffff, 0xffff, 0xffff, 0xffff, 0xffff, 0xffff, 0xffff, 0xffff, 0xffff, 0xffff, 0xffff, 0xffff, 0xf7fe, 0x0c68, 0xa738, 0xffff, 0xffff, 0xffff, 0xffff, 0xffff, 0xffff, 0xffff, 0xffbf, 0xffff, 0xffff, 0xffff, 0xffff, 0xffff, 0xffff, 0xffff, 0x1c48, 0x0c47, 0x5d8f, 0xefff, 0xffff, 0xffff, 0xffff, 0xffff, 0xffff, 0xffff, 0xffff,
	0x1c48, 0x1449, 0xffff, 0xffff, 0xffff, 0xffff, 0xffff, 0xffff, 0xffff, 0xffff, 0xffff, 0xffff, 0xffff, 0xffff, 0xffff, 0xf7ff, 0xe7fe, 0x0c68, 0xa758, 0xffff, 0xffff, 0xffff, 0xffff, 0xffff, 0xffff, 0xffff, 0xf7ff, 0xdffe, 0x1c29, 0x1c28, 0xe7fe, 0xfffe, 0xefff, 0x1c4a, 0x04c8, 0x1c48, 0xf7ff, 0xffff, 0xffff, 0xffff, 0xf7ff, 0xfffe, 0xffff, 0xffff, 0xffff,
	0x1448, 0x0c69, 0xf7ff, 0xf7ff, 0xffff, 0xffff, 0xffff, 0xffff, 0xffff, 0xffff, 0xffff, 0xffff, 0xffff, 0xffff, 0xffff, 0xf7ff, 0xefff, 0x0468, 0x9f58, 0xffff, 0xffff, 0xffff, 0xffff, 0xffff, 0xffff, 0xffff, 0xf7ff, 0x3c4c, 0x04c8, 0x0488, 0x6e52, 0xf7ff, 0x450d, 0x0427, 0x0428, 0xcffb, 0xffbf, 0xffff, 0xffff, 0xffff, 0xffff, 0xffff, 0xffff, 0xffff, 0xffff,
	0x1429, 0x1448, 0xffff, 0xffff, 0xffff, 0xffff, 0xffff, 0xffff, 0xffff, 0xffff, 0xffff, 0xffff, 0xffff, 0xffff, 0xffff, 0xffff, 0xefff, 0x1448, 0xaf37, 0xffff, 0xffff, 0xffff, 0xffff, 0xffff, 0xffff, 0xffff, 0xffff, 0xe7fe, 0x0467, 0x0487, 0x0c68, 0x9f97, 0x0488, 0x0488, 0xdffd, 0xffff, 0xffff, 0xffff, 0xf7ff, 0xffff, 0xffff, 0xffff, 0xffff, 0xffff, 0xffff,
	0x1449, 0x1448, 0xf7ff, 0xffff, 0xfffe, 0xffff, 0xffff, 0xffff, 0xffff, 0xffff, 0xffff, 0xffff, 0xffff, 0xffff, 0xffff, 0xffff, 0xe7fe, 0x0469, 0xa757, 0xffff, 0xffff, 0xffff, 0xffff, 0xffff, 0xffff, 0xffff, 0xffff, 0xefff, 0x0c88, 0x0c87, 0x0c88, 0x0c47, 0x0488, 0x1c28, 0xf7fe, 0xffbf, 0xffff, 0xffff, 0xffff, 0xffff, 0xffff, 0xffff, 0xffff, 0xffff, 0xffff,
	0x1448, 0x1448, 0xf7ff, 0xffff, 0xfffe, 0xffff, 0xffff, 0xffff, 0xffff, 0xffff, 0xffff, 0xffff, 0xffff, 0xfffe, 0xffff, 0xffff, 0xf7fe, 0x0c69, 0xa758, 0xffff, 0xffff, 0xffff, 0xffff, 0xffff, 0xffff, 0xffff, 0xffff, 0xf7ff, 0x0c69, 0x0487, 0x04a7, 0x0c87, 0x1429, 0xe7fe, 0xffff, 0xffff, 0xffff, 0xffff, 0xffff, 0xffff, 0xffff, 0xffff, 0xffff, 0xffff, 0xffff,
	0x1449, 0x1448, 0xf7ff, 0xffff, 0xf7ff, 0xffff, 0xffff, 0xffff, 0xffff, 0xffff, 0xffff, 0xffff, 0xffff, 0xffff, 0xf7ff, 0xffff, 0xefff, 0x0489, 0x9f57, 0xffff, 0xffff, 0xffff, 0xffff, 0xffff, 0xffff, 0xffff, 0xffff, 0xfffe, 0x1c2a, 0x0c47, 0x04a7, 0x0466, 0xb779, 0xffff, 0xffff, 0xf7ff, 0xffff, 0xffff, 0xffff, 0xffff, 0xffff, 0xffff, 0xffff, 0xffff, 0xffff,
	0x1c48, 0x1447, 0xefff, 0xffff, 0xffff, 0xffff, 0xffff, 0xffff, 0xffff, 0xffff, 0xffff, 0xffff, 0xffff, 0xffff, 0xffff, 0xf7ff, 0xe7fe, 0x0c68, 0xaf18, 0xffff, 0xffff, 0xffff, 0xffff, 0xffff, 0xffff, 0xffff, 0xffff, 0xffff, 0x86b5, 0x1449, 0x0488, 0x34ab, 0xffff, 0xffff, 0xffff, 0xffff, 0xffff, 0xffff, 0xffff, 0xffff, 0xffff, 0xffff, 0xffff, 0xffff, 0xffff,
	0x1c49, 0x0466, 0xafda, 0xbfb9, 0xbfb9, 0xc7ba, 0xb7da, 0xb7da, 0xb7da, 0xb7da, 0xb7da, 0xb7da, 0xb7da, 0xbfba, 0xbfba, 0xb7da, 0x9f57, 0x0c27, 0x9ed6, 0xffff, 0xffff, 0xffff, 0xffff, 0xffff, 0xffff, 0xffff, 0xffff, 0xffbf, 0xefff, 0x9655, 0xdffc, 0xefff, 0xffff, 0xffff, 0xffff, 0xffff, 0xffff, 0xffff, 0xffff, 0xffff, 0xffff, 0xffff, 0xffff, 0xffff, 0xffff,
	0x75b1, 0x0c68, 0x1449, 0x1448, 0x1488, 0x1449, 0x1448, 0x1448, 0x1c48, 0x1c48, 0x1448, 0x1c48, 0x1448, 0x0c68, 0x1c48, 0x1449, 0x1c29, 0x2429, 0xe7fe, 0xffff, 0xffff, 0xffff, 0xffff, 0xffff, 0xffff, 0xffff, 0xffff, 0xffff, 0xffff, 0xffff, 0xfffe, 0xf7bf, 0xffff, 0xffff, 0xffff, 0xffff, 0xffff, 0xffff, 0xffff, 0xffff, 0xffff, 0xffff, 0xffff, 0xffff, 0xffff,
	0xfffe, 0xefff, 0xefff, 0xe7fe, 0xefff, 0xeffe, 0xe7fe, 0xe7fe, 0xeffe, 0xefff, 0xe7fe, 0xe7fe, 0xe7fe, 0xeffe, 0xefff, 0xe7ff, 0xe7fe, 0xf7fe, 0xffff, 0xffff, 0xffff, 0xffff, 0xffff, 0xffbf, 0xffff, 0xffff, 0xffff, 0xffff, 0xffff, 0xffff, 0xffff, 0xffff, 0xffff, 0xffff, 0xffff, 0xffff, 0xffff, 0xffff, 0xffff, 0xffff, 0xffff, 0xffff, 0xffff, 0xffff, 0xffff,
	0xcfdc, 0xc7bb, 0xbfda, 0xbffb, 0xbfda, 0xbffb, 0xbffc, 0xbffb, 0xbfda, 0xbffb, 0xbffb, 0xbfbb, 0xbffb, 0xbffb, 0xbffb, 0xbffb, 0xbfba, 0xbfbb, 0xe7ff, 0xffff, 0xffff, 0xffff, 0xffff, 0xffff, 0xfffe, 0xffff, 0xffff, 0xffff, 0xffff, 0xffff, 0xffff, 0xffff, 0xffff, 0xf7ff, 0xffff, 0xffff, 0xffff, 0xffff, 0xffff, 0xffff, 0xffff, 0xffff, 0xffff, 0xffff, 0xffff,
	0x1407, 0x1448, 0x1488, 0x1447, 0x0467, 0x0c47, 0x0c27, 0x0c47, 0x0c27, 0x0c47, 0x0c47, 0x0c47, 0x0c47, 0x0c47, 0x0468, 0x0c68, 0x0c68, 0x0c69, 0x2c2a, 0xf7bf, 0xffff, 0xffff, 0xffff, 0xfffe, 0xffff, 0xf7fe, 0xf7ff, 0xf7ff, 0xf7ff, 0xf7ff, 0xf7ff, 0xf7ff, 0xf7ff, 0xf7ff, 0xf7ff, 0xf7ff, 0xf7ff, 0xfffe, 0xf7ff, 0xf7ff, 0xf7ff, 0xf7ff, 0xf7ff, 0xf7ff, 0xf7ff,
	0x1c48, 0x0c69, 0xe7fd, 0xdffe, 0x0cca, 0x0487, 0x0487, 0x0468, 0x0488, 0x0487, 0x0467, 0x0468, 0x0468, 0x0c87, 0x0487, 0x0c87, 0x0c87, 0x0487, 0x1c49, 0xffff, 0xffff, 0xffff, 0xffff, 0xffff, 0xe7ff, 0x1c2a, 0x0487, 0x0487, 0x0487, 0x0487, 0x0467, 0x0487, 0x0467, 0x0c87, 0x0467, 0x04a7, 0x0468, 0x76d3, 0x0c47, 0x0487, 0x0487, 0x0488, 0x0487, 0x0488, 0x0487,
	0x0c47, 0x1cca, 0xf7fe, 0xdfff, 0x0469, 0x0488, 0x0487, 0x0488, 0x0488, 0x0488, 0x0488, 0x0488, 0x0488, 0x0488, 0x0488, 0x0488, 0x0487, 0x0488, 0x1c49, 0xffff, 0xffff, 0xfffe, 0xf7ff, 0xffbf, 0xdfff, 0x0428, 0x0c69, 0x0c68, 0x0c69, 0x0c68, 0x0c68, 0x0c68, 0x0468, 0x0c68, 0x0c69, 0x0469, 0x0468, 0x6ed2, 0x0488, 0x0487, 0x0487, 0x0c87, 0x0487, 0x0487, 0x0487,
	0x1429, 0x0468, 0x7631, 0x3d4c, 0x0487, 0x04a7, 0x0487, 0x0467, 0x0487, 0x0487, 0x0487, 0x0487, 0x0487, 0x0488, 0x0488, 0x0487, 0x0488, 0x0488, 0x1c2a, 0xf7ff, 0xffff, 0xfffe, 0xffff, 0xffff, 0xdffd, 0x1c48, 0xcfdc, 0xcfdc, 0xcffc, 0xcfdc, 0xcfdc, 0xc7fc, 0xcffc, 0xcfdc, 0xc7fc, 0xcffc, 0x4df0, 0x7693, 0x1488, 0x0488, 0x0488, 0x0488, 0x0488, 0x0488, 0x0488,
	0x4d0e, 0x2c2a, 0x2c4b, 0x242a, 0x242a, 0x1c2a, 0x2429, 0x2429, 0x2429, 0x2449, 0x1c2a, 0x2429, 0x2428, 0x2449, 0x2429, 0x2429, 0x1c29, 0x2bea, 0x6d51, 0xf7ff, 0xffff, 0xffff, 0xffff, 0xffff, 0xdffe, 0x1c29, 0xffff, 0xf7ff, 0xffff, 0xf7ff, 0xffff, 0xffff, 0xffff, 0xf7ff, 0xffff, 0xf7ff, 0x5db0, 0x76d4, 0x0468, 0x0487, 0x0467, 0x0487, 0x0487, 0x0487, 0x0487,
	0xffff, 0xf7fe, 0xe7fe, 0xefff, 0xeffe, 0xf7fe, 0xf7fe, 0xefff, 0xefff, 0xf7ff, 0xf7fe, 0xefff, 0xf7ff, 0xeffe, 0xeffe, 0xf7fe, 0xefff, 0xeffe, 0xffff, 0xffff, 0xffff, 0xffff, 0xffff, 0xffff, 0xdffe, 0x1c29, 0xffff, 0xffff, 0xffff, 0xffff, 0xffff, 0xffbf, 0xfffe, 0xffbf, 0xf7bf, 0xffff, 0x5db0, 0x76d4, 0x0c68, 0x04a7, 0x0487, 0x0489, 0x0c47, 0x0466, 0x0487,
	0xffff, 0xffff, 0xf7ff, 0xffff, 0xffff, 0xffff, 0xffff, 0xffff, 0xffff, 0xffff, 0xeffe, 0xd7fd, 0xf7ff, 0xffff, 0xffff, 0xffff, 0xffff, 0xffff, 0xffff, 0xffff, 0xffff, 0xffff, 0xffff, 0xffff, 0xdffe, 0x1c29, 0xf7ff, 0xcf5a, 0xf7fe, 0x544e, 0xc7bb, 0xefff, 0x8eb6, 0xe7fe, 0x33cb, 0xffff, 0x55b0, 0x7eb4, 0x1448, 0x1447, 0x0c68, 0x1c4a, 0x0cca, 0x0487, 0x0466,
	0xffff, 0xffff, 0xffff, 0xffff, 0xffff, 0xffff, 0xffff, 0xffff, 0xffff, 0xf7fe, 0x1447, 0x0469, 0xa6f8, 0xffff, 0xffff, 0xffff, 0xffff, 0xffff, 0xffff, 0xffff, 0xffff, 0xffff, 0xffff, 0xffff, 0xdffe, 0x1c29, 0xffff, 0xe7ff, 0xeffd, 0xcffc, 0xd7fd, 0xf7fe, 0x86b5, 0xbfba, 0x342b, 0xffff, 0x55b0, 0x7ed4, 0x0c68, 0x1ca9, 0xc7ba, 0xf7fe, 0xd7fd, 0x1447, 0x0c87,
	0xffff, 0xffff, 0xffff, 0xffff, 0xffff, 0xffff, 0xffff, 0xffff, 0xffff, 0xe7fe, 0x0468, 0x0c68, 0x8e54, 0xffff, 0xffff, 0xffff, 0xffff, 0xffff, 0xffff, 0xffff, 0xffff, 0xfffe, 0xffff, 0xffff, 0xdffe, 0x2429, 0xffbe, 0xf7bf, 0xfffe, 0xeffe, 0xf7fe, 0xf7bf, 0xe7fe, 0xefff, 0xeffd, 0xffff, 0x5db0, 0x76d4, 0x0468, 0xd7fc, 0xffff, 0xffff, 0xffff, 0x65f1, 0x04c8,
	0xffff, 0xffff, 0xffff, 0xffff, 0xffff, 0xffff, 0xffff, 0xffff, 0xffff, 0xf7ff, 0xb77a, 0x7551, 0xf7ff, 0xfffe, 0xffff, 0xffff, 0xffff, 0xffff, 0xffff, 0xffff, 0xffff, 0xffff, 0xffff, 0xffff, 0xdffe, 0x2429, 0xffff, 0xffff, 0xffff, 0xf7bf, 0xffff, 0xffff, 0xffff, 0xffff, 0xfffe, 0xffff, 0x5d8f, 0x76d4, 0x0c68, 0xd7fc, 0xffff, 0xffbf, 0xffff, 0x75b1, 0x0c88,
	0xffff, 0xffff, 0xffff, 0xffff, 0xffff, 0xffff, 0xfffe, 0xfffe, 0xfffe, 0xffff, 0xffbf, 0xffff, 0xffff, 0xfffe, 0xffff, 0xf7ff, 0xf7ff, 0xffff, 0xf7ff, 0xffff, 0xffff, 0xffff, 0xffff, 0xffff, 0xdffe, 0x1c29, 0xf7ff, 0xffff, 0xffff, 0xffbf, 0xffff, 0xffbf, 0xffff, 0xffbf, 0xf7ff, 0xffff, 0x5db0, 0x76d4, 0x0c68, 0xc7bb, 0xf7ff, 0xffff, 0xffff, 0x6db1, 0x0488,
	0xffff, 0xffff, 0xffff, 0xffff, 0xffff, 0xffff, 0xffff, 0xfffe, 0xffff, 0xffff, 0xffff, 0xf7ff, 0xffff, 0xffff, 0xffff, 0xffff, 0xffff, 0xffff, 0xffff, 0xffff, 0xffff, 0xffff, 0xffff, 0xffff, 0xdffe, 0x2429, 0xffff, 0xffff, 0xffff, 0xffff, 0xffff, 0xffff, 0xffff, 0xffff, 0xffff, 0xffff, 0x55b0, 0x7eb4, 0x0468, 0xcffc, 0xffff, 0xf7ff, 0xffff, 0x6db1, 0x0488,
	0xffff, 0xffff, 0xffff, 0xffff, 0xffff, 0xffff, 0xffff, 0xffff, 0xf7ff, 0xf7fe, 0x2c4a, 0x1c49, 0xc7ba, 0xffff, 0xffff, 0xffff, 0xffff, 0xffff, 0xffff, 0xffff, 0xffff, 0xffff, 0xffff, 0xffff, 0xdffe, 0x1c29, 0xf7ff, 0xffff, 0xffff, 0xffff, 0xffff, 0xffff, 0xffff, 0xffff, 0xffff, 0xffff, 0x5dd0, 0x7eb4, 0x0c68, 0x9f97, 0xb799, 0xbfba, 0xbfba, 0x55ef, 0x0488,
	0xffff, 0xffff, 0xffff, 0xffff, 0xffff, 0xffff, 0xfffe, 0xffff, 0xffff, 0xe7fd, 0x1488, 0x0c87, 0x75b1, 0xffff, 0xffff, 0xffff, 0xffff, 0xffff, 0xffff, 0xffff, 0xffff, 0xffff, 0xffff, 0xffff, 0xdffe, 0x2429, 0xf7ff, 0xffff, 0xffff, 0xffff, 0xffff, 0xffff, 0xffff, 0xffff, 0xffff, 0xffff, 0x55af, 0x7ed4, 0x0c68, 0x0468, 0x0c68, 0x0c68, 0x0c68, 0x0489, 0x0488,
	0xffff, 0xffff, 0xffff, 0xffff, 0xffff, 0xffff, 0xfffe, 0xffff, 0xffff, 0xf7ff, 0x3ccc, 0x1c49, 0xd7fd, 0xffff, 0xffff, 0xffff, 0xffff, 0xffff, 0xffff, 0xffff, 0xffff, 0xfffe, 0xffff, 0xffff, 0xdffe, 0x1c29, 0xffff, 0xffff, 0xffff, 0xffff, 0xffff, 0xffff, 0xffff, 0xffff, 0xffff, 0xfffe, 0x55b0, 0x7eb4, 0x0468, 0x0488, 0x0487, 0x0487, 0x04a7, 0x0487, 0x0c47,
	0xffff, 0xffff, 0xffff, 0xffff, 0xffff, 0xffff, 0xfffe, 0xffff, 0xffff, 0xffff, 0xf7ff, 0xffff, 0xffff, 0xffff, 0xffff, 0xffff, 0xffff, 0xffff, 0xffff, 0xffff, 0xffff, 0xffff, 0xffff, 0xffff, 0xdffe, 0x2429, 0xf7ff, 0xffff, 0xffff, 0xffff, 0xffff, 0xffff, 0xffff, 0xffff, 0xffff, 0xffff, 0x55b0, 0x7eb4, 0x0c68, 0x0487, 0x0487, 0x0487, 0x0487, 0x0487, 0x0c87,
	0xffff, 0xffff, 0xffff, 0xffff, 0xffff, 0xffff, 0xffff, 0xffff, 0xffff, 0xffff, 0xffbe, 0xffff, 0xf7ff, 0xffff, 0xffff, 0xffff, 0xffff, 0xffff, 0xffff, 0xffff, 0xffff, 0xffff, 0xffff, 0xffff, 0xdffe, 0x2429, 0xf7ff, 0xffff, 0xffff, 0xffff, 0xffff, 0xffff, 0xffff, 0xffff, 0xffff, 0xffff, 0x55b0, 0x7eb4, 0x0c68, 0x0488, 0x0468, 0x0468, 0x0488, 0x0488, 0x0487,
	0xffff, 0xffff, 0xffff, 0xffff, 0xffff, 0xffff, 0xffff, 0xffff, 0xffff, 0xfffe, 0xd7fc, 0xaf58, 0xefff, 0xffff, 0xffff, 0xdffe, 0x9eb6, 0xe7fe, 0xffff, 0xffff, 0xfffe, 0x8735, 0xc7fb, 0xf7be, 0xdfff, 0x1449, 0xcffb, 0xd7fc, 0xcffc, 0xd7fc, 0xcffc, 0xd7fc, 0xcffc, 0xd7fc, 0xc7fc, 0xc7fb, 0x4df0, 0x7ed4, 0x0c89, 0x0487, 0x0467, 0x0488, 0x0488, 0x0487, 0x0487,
	0xffff, 0xffff, 0xffff, 0xffff, 0xffff, 0xffff, 0xffff, 0xffff, 0xffff, 0xe7fd, 0x0c89, 0x0469, 0x75b1, 0xffff, 0xf7ff, 0x1c48, 0x0c88, 0x2429, 0xffbe, 0xffbf, 0x7e13, 0x0488, 0x0c29, 0xbfba, 0xe7fd, 0x1448, 0x2d4c, 0x2d4c, 0x354d, 0x2d4c, 0x354d, 0x2d2c, 0x2d4c, 0x2d4c, 0x354d, 0x354d, 0x0c68, 0x76d3, 0x0468, 0x0488, 0x0487, 0x0487, 0x0488, 0x0487, 0x0488,
	0xffff, 0xffff, 0xffff, 0xffff, 0xffff, 0xffff, 0xfffe, 0xffbf, 0xffff, 0xe7fd, 0x0c68, 0x0c68, 0x8e54, 0xffff, 0xf7ff, 0x242a, 0x1448, 0x3ccc, 0xf7ff, 0xffff, 0xa738, 0x0489, 0x146a, 0xd7fd, 0xdffd, 0x1488, 0x6e52, 0x1c2a, 0x65f1, 0x23e9, 0x6db1, 0x23e9, 0x5d4f, 0x2c2a, 0x550d, 0x2429, 0x1449, 0x8735, 0x0c68, 0x0487, 0x0487, 0x0c87, 0x0467, 0x0487, 0x0487,
	0xffff, 0xffff, 0xffff, 0xffff, 0xffff, 0xffff, 0xffff, 0xffff, 0xf7ff, 0xf7ff, 0xf7fe, 0xf7fe, 0xfffe, 0xffff, 0xffff, 0xf7ff, 0xeffe, 0xfffe, 0xf7bf, 0xffff, 0xfffe, 0xe7fe, 0xf7fe, 0xffff, 0xd7fd, 0x0c69, 0x4daf, 0x4e0f, 0x5630, 0x4def, 0x55ef, 0x55ef, 0x4def, 0x4e0f, 0x55ef, 0x462f, 0x0468, 0x6ed2, 0x0c88, 0x0487, 0x0487, 0x0488, 0x0488, 0x0488, 0x0487,
	0xffff, 0xffff, 0xffff, 0xffff, 0xffff, 0xffff, 0xffff, 0xffff, 0xffff, 0xffff, 0xffff, 0xffbf, 0xffff, 0xffff, 0xffff, 0xffff, 0xffff, 0xffff, 0xffff, 0xffff, 0xffff, 0xffff, 0xffff, 0xffff, 0xe7fe, 0x2c4a, 0x1448, 0x1428, 0x1c48, 0x1448, 0x1c48, 0x1428, 0x1c49, 0x2429, 0x2428, 0x1c29, 0x1429, 0x7693, 0x1c49, 0x1449, 0x1448, 0x1448, 0x1448, 0x1448, 0x1449};

// 'OASYS-NG_QR-code_extra', 246x246px
const uint16_t OASYS_QR[5024] PROGMEM = {
	// 'OASYS-NG_QR-code_extra', 200x200px
	// const uint16_t OASYS_QR[200 [200] PROGMEM = {
	0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
	0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
	0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
	0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
	0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
	0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
	0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
	0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
	0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
	0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
	0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
	0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
	0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
	0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
	0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
	0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xc0, 0x00, 0x00, 0x00, 0x0f,
	0x80, 0x00, 0x0f, 0x80, 0x1f, 0x07, 0xc3, 0xff, 0xff, 0xc1, 0xff, 0x87, 0xff, 0xf0, 0x00, 0x00,
	0x00, 0x03, 0xff, 0xff, 0xc0, 0x00, 0x00, 0x00, 0x0f, 0x80, 0x00, 0x0f, 0x80, 0x1f, 0x07, 0xc3,
	0xff, 0xff, 0xc1, 0xff, 0x87, 0xff, 0xf0, 0x00, 0x00, 0x00, 0x03, 0xff, 0xff, 0xc0, 0x00, 0x00,
	0x00, 0x0f, 0x80, 0x00, 0x0f, 0x80, 0x1f, 0x07, 0xc3, 0xff, 0xff, 0xc1, 0xff, 0x87, 0xff, 0xf0,
	0x00, 0x00, 0x00, 0x03, 0xff, 0xff, 0xc0, 0x00, 0x00, 0x00, 0x0f, 0x80, 0x00, 0x0f, 0x80, 0x1f,
	0x07, 0xc3, 0xff, 0xff, 0xc1, 0xff, 0x87, 0xff, 0xf0, 0x00, 0x00, 0x00, 0x03, 0xff, 0xff, 0xc0,
	0x00, 0x00, 0x00, 0x0f, 0x80, 0x00, 0x0f, 0x80, 0x1f, 0x0f, 0xc3, 0xff, 0xff, 0xc1, 0xff, 0x87,
	0xff, 0xf0, 0x00, 0x00, 0x00, 0x03, 0xff, 0xff, 0xc1, 0xff, 0xff, 0xfe, 0x0f, 0x83, 0xff, 0xff,
	0xfc, 0x1f, 0xff, 0xc3, 0xe0, 0xf8, 0x3e, 0x0f, 0xf8, 0x01, 0xf0, 0x7f, 0xff, 0xff, 0x83, 0xff,
	0xff, 0xc1, 0xff, 0xff, 0xfe, 0x0f, 0x83, 0xff, 0xff, 0xfc, 0x1f, 0xff, 0xc3, 0xe0, 0xf8, 0x3e,
	0x0f, 0xf8, 0x01, 0xf0, 0x7f, 0xff, 0xff, 0x83, 0xff, 0xff, 0xc1, 0xff, 0xff, 0xfe, 0x0f, 0x83,
	0xff, 0xff, 0xfc, 0x1f, 0xff, 0xc3, 0xe0, 0xf8, 0x3e, 0x0f, 0xf8, 0x01, 0xf0, 0x7f, 0xff, 0xff,
	0x83, 0xff, 0xff, 0xc1, 0xff, 0xff, 0xfe, 0x0f, 0x83, 0xff, 0xff, 0xfc, 0x1f, 0xff, 0xc3, 0xe0,
	0xf8, 0x3e, 0x0f, 0xf8, 0x01, 0xf0, 0x7f, 0xff, 0xff, 0x83, 0xff, 0xff, 0xc1, 0xff, 0xff, 0xfe,
	0x0f, 0x83, 0xff, 0xff, 0xfc, 0x1f, 0xff, 0xc3, 0xf0, 0xf8, 0x3c, 0x0f, 0xfc, 0x01, 0xf0, 0x7f,
	0xff, 0xff, 0x83, 0xff, 0xff, 0xc1, 0xf0, 0x00, 0x3e, 0x0f, 0x80, 0x01, 0xff, 0x83, 0xe0, 0xff,
	0xc3, 0xff, 0xff, 0xc0, 0x0f, 0xff, 0xc1, 0xf0, 0x7c, 0x00, 0x0f, 0x83, 0xff, 0xff, 0xc1, 0xf0,
	0x00, 0x3e, 0x0f, 0x80, 0x01, 0xff, 0x83, 0xe0, 0xff, 0xc3, 0xff, 0xff, 0xc0, 0x0f, 0xff, 0xc1,
	0xf0, 0x7c, 0x00, 0x0f, 0x83, 0xff, 0xff, 0xc1, 0xf0, 0x00, 0x3e, 0x0f, 0x80, 0x01, 0xff, 0x83,
	0xe0, 0xff, 0xc3, 0xff, 0xff, 0xc0, 0x0f, 0xff, 0xc1, 0xf0, 0x7c, 0x00, 0x0f, 0x83, 0xff, 0xff,
	0xc1, 0xf0, 0x00, 0x3e, 0x0f, 0x80, 0x01, 0xff, 0x83, 0xe0, 0xff, 0xc3, 0xff, 0xff, 0xc0, 0x0f,
	0xff, 0xc1, 0xf0, 0x7c, 0x00, 0x0f, 0x83, 0xff, 0xff, 0xc1, 0xf0, 0x00, 0x3e, 0x0f, 0xf8, 0x00,
	0x0f, 0x80, 0x1f, 0x0f, 0xc0, 0x00, 0xff, 0xc0, 0x00, 0xfc, 0x01, 0xf0, 0x7c, 0x00, 0x0f, 0x83,
	0xff, 0xff, 0xc1, 0xf0, 0x00, 0x3e, 0x0f, 0xfc, 0x00, 0x0f, 0x80, 0x1f, 0x07, 0xc0, 0x00, 0xff,
	0xc0, 0x00, 0x78, 0x01, 0xf0, 0x7c, 0x00, 0x0f, 0x83, 0xff, 0xff, 0xc1, 0xf0, 0x00, 0x3e, 0x0f,
	0xfc, 0x00, 0x0f, 0x80, 0x1f, 0x07, 0xc0, 0x00, 0xff, 0xc0, 0x00, 0x78, 0x01, 0xf0, 0x7c, 0x00,
	0x0f, 0x83, 0xff, 0xff, 0xc1, 0xf0, 0x00, 0x3e, 0x0f, 0xfc, 0x00, 0x0f, 0x80, 0x1f, 0x07, 0xc0,
	0x00, 0xff, 0xc0, 0x00, 0x78, 0x01, 0xf0, 0x7c, 0x00, 0x0f, 0x83, 0xff, 0xff, 0xc1, 0xf0, 0x00,
	0x3e, 0x0f, 0xfc, 0x00, 0x0f, 0x80, 0x1f, 0x07, 0xc0, 0x00, 0xff, 0xc0, 0x00, 0x78, 0x01, 0xf0,
	0x7c, 0x00, 0x0f, 0x83, 0xff, 0xff, 0xc1, 0xf0, 0x00, 0x3e, 0x0f, 0xff, 0xfe, 0x00, 0x7f, 0xe0,
	0x00, 0x3c, 0x00, 0xff, 0xff, 0xff, 0x80, 0x01, 0xf0, 0x7c, 0x00, 0x0f, 0x83, 0xff, 0xff, 0xc1,
	0xf0, 0x00, 0x3e, 0x0f, 0xff, 0xfe, 0x00, 0x7f, 0xe0, 0x00, 0x3c, 0x00, 0xff, 0xff, 0xff, 0x80,
	0x01, 0xf0, 0x7c, 0x00, 0x0f, 0x83, 0xff, 0xff, 0xc1, 0xf0, 0x00, 0x3e, 0x0f, 0xff, 0xfe, 0x00,
	0x7f, 0xe0, 0x00, 0x3c, 0x00, 0xff, 0xff, 0xff, 0x80, 0x01, 0xf0, 0x7c, 0x00, 0x0f, 0x83, 0xff,
	0xff, 0xc1, 0xf0, 0x00, 0x3e, 0x0f, 0xff, 0xfe, 0x00, 0x7f, 0xe0, 0x00, 0x3c, 0x00, 0xff, 0xff,
	0xff, 0x80, 0x01, 0xf0, 0x7c, 0x00, 0x0f, 0x83, 0xff, 0xff, 0xc1, 0xf0, 0x00, 0x3e, 0x0f, 0xff,
	0xfe, 0x00, 0x7f, 0xe0, 0x00, 0x3c, 0x00, 0xff, 0xff, 0xff, 0x80, 0x01, 0xf0, 0x7c, 0x00, 0x0f,
	0x83, 0xff, 0xff, 0xc1, 0xff, 0xff, 0xfe, 0x0f, 0x80, 0x1e, 0x0f, 0x83, 0xe0, 0xf8, 0x03, 0xff,
	0xf8, 0x00, 0x0f, 0xf8, 0x3f, 0xf0, 0x7f, 0xff, 0xff, 0x83, 0xff, 0xff, 0xc1, 0xff, 0xff, 0xfe,
	0x0f, 0x80, 0x1e, 0x0f, 0x83, 0xe0, 0xf8, 0x03, 0xff, 0xf8, 0x00, 0x0f, 0xf8, 0x3f, 0xf0, 0x7f,
	0xff, 0xff, 0x83, 0xff, 0xff, 0xc1, 0xff, 0xff, 0xfe, 0x0f, 0x80, 0x1e, 0x0f, 0x83, 0xe0, 0xf8,
	0x03, 0xff, 0xf8, 0x00, 0x0f, 0xf8, 0x3f, 0xf0, 0x7f, 0xff, 0xff, 0x83, 0xff, 0xff, 0xc1, 0xff,
	0xff, 0xfe, 0x0f, 0x80, 0x1e, 0x0f, 0x83, 0xe0, 0xf8, 0x03, 0xff, 0xf8, 0x00, 0x0f, 0xf8, 0x3f,
	0xf0, 0x7f, 0xff, 0xff, 0x83, 0xff, 0xff, 0xc1, 0xff, 0xff, 0xfe, 0x0f, 0x80, 0x1e, 0x0f, 0x83,
	0xe0, 0xf8, 0x03, 0xff, 0xf8, 0x00, 0x0f, 0xf8, 0x3f, 0xf0, 0x7f, 0xff, 0xff, 0x83, 0xff, 0xff,
	0xc0, 0x00, 0x00, 0x00, 0x0f, 0x83, 0xe1, 0xf0, 0x7c, 0x1f, 0x07, 0xc3, 0xe0, 0xf8, 0x3e, 0x0f,
	0x87, 0xc1, 0xf0, 0x00, 0x00, 0x00, 0x03, 0xff, 0xff, 0xc0, 0x00, 0x00, 0x00, 0x0f, 0x83, 0xe1,
	0xf0, 0x7c, 0x1f, 0x07, 0xc3, 0xe0, 0xf8, 0x3e, 0x0f, 0x87, 0xc1, 0xf0, 0x00, 0x00, 0x00, 0x03,
	0xff, 0xff, 0xc0, 0x00, 0x00, 0x00, 0x0f, 0x83, 0xe1, 0xf0, 0x7c, 0x1f, 0x07, 0xc3, 0xe0, 0xf8,
	0x3e, 0x0f, 0x87, 0xc1, 0xf0, 0x00, 0x00, 0x00, 0x03, 0xff, 0xff, 0xc0, 0x00, 0x00, 0x00, 0x0f,
	0x83, 0xe1, 0xf0, 0x7c, 0x1f, 0x07, 0xc3, 0xe0, 0xf8, 0x3e, 0x0f, 0x87, 0xc1, 0xf0, 0x00, 0x00,
	0x00, 0x03, 0xff, 0xff, 0xc0, 0x00, 0x00, 0x00, 0x0f, 0x83, 0xe1, 0xf0, 0x7c, 0x1f, 0x07, 0xc3,
	0xe0, 0xf8, 0x3e, 0x0f, 0x87, 0xc1, 0xf0, 0x00, 0x00, 0x00, 0x03, 0xff, 0xff, 0xff, 0xff, 0xff,
	0xff, 0xff, 0x80, 0x01, 0xf0, 0x00, 0x00, 0x07, 0xc3, 0xe0, 0xf8, 0x3e, 0x0f, 0x87, 0xff, 0xff,
	0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x80, 0x01, 0xf0, 0x00, 0x00,
	0x07, 0xc3, 0xe0, 0xf8, 0x3e, 0x0f, 0x87, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
	0xff, 0xff, 0xff, 0xff, 0x80, 0x01, 0xf0, 0x00, 0x00, 0x07, 0xc3, 0xe0, 0xf8, 0x3e, 0x0f, 0x87,
	0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x80, 0x01, 0xf0,
	0x00, 0x00, 0x07, 0xc3, 0xe0, 0xf8, 0x3e, 0x0f, 0x87, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
	0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x80, 0x01, 0xf0, 0x00, 0x00, 0x07, 0x81, 0xe0, 0xf8, 0x3e,
	0x0f, 0x87, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf0, 0x00, 0x3e, 0x0f, 0x83,
	0xe1, 0xf0, 0x7f, 0xff, 0x00, 0x00, 0x1f, 0xff, 0xc0, 0x0f, 0xff, 0xfe, 0x00, 0x03, 0xff, 0x00,
	0x03, 0xff, 0xff, 0xff, 0xf0, 0x00, 0x3e, 0x0f, 0x83, 0xe1, 0xf0, 0x7f, 0xff, 0x00, 0x00, 0x1f,
	0xff, 0xc0, 0x0f, 0xff, 0xfe, 0x00, 0x03, 0xff, 0x00, 0x03, 0xff, 0xff, 0xff, 0xf0, 0x00, 0x3e,
	0x0f, 0x83, 0xe1, 0xf0, 0x7f, 0xff, 0x00, 0x00, 0x1f, 0xff, 0xc0, 0x0f, 0xff, 0xfe, 0x00, 0x03,
	0xff, 0x00, 0x03, 0xff, 0xff, 0xff, 0xf0, 0x00, 0x3e, 0x0f, 0x83, 0xe1, 0xf0, 0x7f, 0xff, 0x00,
	0x00, 0x1f, 0xff, 0xc0, 0x0f, 0xff, 0xfe, 0x00, 0x03, 0xff, 0x00, 0x03, 0xff, 0xff, 0xff, 0xf0,
	0x00, 0x3e, 0x0f, 0x83, 0xe1, 0xf0, 0x7f, 0xff, 0x00, 0x00, 0x1f, 0xff, 0xc0, 0x0f, 0xff, 0xfe,
	0x00, 0x03, 0xff, 0x00, 0x03, 0xff, 0xff, 0xc0, 0x0f, 0x07, 0xff, 0xff, 0x80, 0x1f, 0xf0, 0x03,
	0xe0, 0xf8, 0x00, 0x1f, 0xff, 0xc0, 0x0f, 0x80, 0x00, 0x0f, 0xff, 0xe0, 0xf0, 0x03, 0xff, 0xff,
	0xc0, 0x0f, 0x07, 0xff, 0xff, 0x80, 0x1f, 0xf0, 0x03, 0xe0, 0xf8, 0x00, 0x1f, 0xff, 0xc0, 0x0f,
	0x80, 0x00, 0x0f, 0xff, 0xe0, 0xf0, 0x03, 0xff, 0xff, 0xc0, 0x0f, 0x07, 0xff, 0xff, 0x80, 0x1f,
	0xf0, 0x03, 0xe0, 0xf8, 0x00, 0x1f, 0xff, 0xc0, 0x0f, 0x80, 0x00, 0x0f, 0xff, 0xe0, 0xf0, 0x03,
	0xff, 0xff, 0xc0, 0x0f, 0x07, 0xff, 0xff, 0x80, 0x1f, 0xf0, 0x03, 0xe0, 0xf8, 0x00, 0x1f, 0xff,
	0xc0, 0x0f, 0x80, 0x00, 0x0f, 0xff, 0xe0, 0xf0, 0x03, 0xff, 0xff, 0xc0, 0x0f, 0x87, 0xff, 0xff,
	0x00, 0x3f, 0xf0, 0x03, 0xe0, 0xf8, 0x00, 0x0f, 0xff, 0xc0, 0x0f, 0x80, 0x00, 0x0f, 0xff, 0xe0,
	0xf0, 0x03, 0xff, 0xff, 0xc0, 0x0f, 0xff, 0xfe, 0x00, 0x03, 0xfe, 0x00, 0x7c, 0x00, 0x07, 0xfc,
	0x00, 0x00, 0x3e, 0x0f, 0xff, 0xff, 0xff, 0x80, 0x1f, 0x00, 0x03, 0xff, 0xff, 0xc0, 0x0f, 0xff,
	0xfe, 0x00, 0x03, 0xfe, 0x00, 0x7c, 0x00, 0x07, 0xfc, 0x00, 0x00, 0x3e, 0x0f, 0xff, 0xff, 0xff,
	0x80, 0x1f, 0x00, 0x03, 0xff, 0xff, 0xc0, 0x0f, 0xff, 0xfe, 0x00, 0x03, 0xfe, 0x00, 0x7c, 0x00,
	0x07, 0xfc, 0x00, 0x00, 0x3e, 0x0f, 0xff, 0xff, 0xff, 0x80, 0x1f, 0x00, 0x03, 0xff, 0xff, 0xc0,
	0x0f, 0xff, 0xfe, 0x00, 0x03, 0xfe, 0x00, 0x7c, 0x00, 0x07, 0xfc, 0x00, 0x00, 0x3e, 0x0f, 0xff,
	0xff, 0xff, 0x80, 0x1f, 0x00, 0x03, 0xff, 0xff, 0xff, 0xe0, 0xfc, 0x3f, 0xf0, 0x03, 0xe0, 0x07,
	0x80, 0x1f, 0xf8, 0x7f, 0xff, 0xf8, 0x00, 0x0f, 0xff, 0xe0, 0x00, 0x7c, 0x1f, 0xf0, 0x3f, 0xff,
	0xff, 0xff, 0xf0, 0xf8, 0x3f, 0xf0, 0x03, 0xe0, 0x0f, 0x80, 0x1f, 0xf8, 0x3f, 0xff, 0xf8, 0x00,
	0x0f, 0xff, 0xc0, 0x00, 0x7c, 0x1f, 0xf0, 0x7f, 0xff, 0xff, 0xff, 0xf0, 0xf8, 0x3f, 0xf0, 0x03,
	0xe0, 0x0f, 0x80, 0x1f, 0xf8, 0x3f, 0xff, 0xf8, 0x00, 0x0f, 0xff, 0xc0, 0x00, 0x7c, 0x1f, 0xf0,
	0x7f, 0xff, 0xff, 0xff, 0xf0, 0xf8, 0x3f, 0xf0, 0x03, 0xe0, 0x0f, 0x80, 0x1f, 0xf8, 0x3f, 0xff,
	0xf8, 0x00, 0x0f, 0xff, 0xc0, 0x00, 0x7c, 0x1f, 0xf0, 0x7f, 0xff, 0xff, 0xff, 0xf0, 0xf8, 0x3f,
	0xf0, 0x03, 0xe0, 0x0f, 0x80, 0x1f, 0xf8, 0x3f, 0xff, 0xf8, 0x00, 0x0f, 0xff, 0xc0, 0x00, 0x7c,
	0x1f, 0xf0, 0x7f, 0xff, 0xff, 0xfe, 0x0f, 0xf8, 0x00, 0x00, 0x00, 0x1e, 0x0f, 0xfc, 0x00, 0x07,
	0xff, 0xe0, 0x07, 0xc1, 0xff, 0x87, 0xff, 0xf0, 0x00, 0x01, 0xf0, 0x03, 0xff, 0xff, 0xfe, 0x0f,
	0xf8, 0x00, 0x00, 0x00, 0x1e, 0x0f, 0xfc, 0x00, 0x07, 0xff, 0xe0, 0x07, 0xc1, 0xff, 0x87, 0xff,
	0xf0, 0x00, 0x00, 0xf0, 0x03, 0xff, 0xff, 0xfe, 0x0f, 0xf8, 0x00, 0x00, 0x00, 0x1e, 0x0f, 0xfc,
	0x00, 0x07, 0xff, 0xe0, 0x07, 0xc1, 0xff, 0x87, 0xff, 0xf0, 0x00, 0x00, 0xf0, 0x03, 0xff, 0xff,
	0xfe, 0x0f, 0xf8, 0x00, 0x00, 0x00, 0x1e, 0x0f, 0xfc, 0x00, 0x07, 0xff, 0xe0, 0x07, 0xc1, 0xff,
	0x87, 0xff, 0xf0, 0x00, 0x00, 0xf0, 0x03, 0xff, 0xff, 0xfe, 0x0f, 0xf8, 0x00, 0x00, 0x00, 0x1e,
	0x0f, 0xfc, 0x00, 0x07, 0xff, 0xe0, 0x07, 0xc1, 0xff, 0x87, 0xff, 0xf0, 0x00, 0x00, 0xf0, 0x03,
	0xff, 0xff, 0xc1, 0xf0, 0x07, 0xff, 0xf0, 0x00, 0x1e, 0x0f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
	0xfe, 0x00, 0x78, 0x00, 0x0f, 0xff, 0xff, 0xff, 0x83, 0xff, 0xff, 0xc1, 0xf0, 0x07, 0xff, 0xf0,
	0x00, 0x1e, 0x0f, 0xff, 0x80, 0x00, 0x00, 0x00, 0x00, 0xfe, 0x00, 0x78, 0x00, 0x0f, 0xff, 0xff,
	0xff, 0x83, 0xff, 0xff, 0xc1, 0xf0, 0x07, 0xff, 0xf0, 0x00, 0x1e, 0x0f, 0xff, 0x3f, 0xff, 0xff,
	0xff, 0xfc, 0x7e, 0x00, 0x78, 0x00, 0x0f, 0xff, 0xff, 0xff, 0x83, 0xff, 0xff, 0xc1, 0xf0, 0x07,
	0xff, 0xf0, 0x00, 0x1e, 0x0f, 0xfe, 0x7f, 0xff, 0xff, 0xff, 0xfe, 0x3e, 0x00, 0x78, 0x00, 0x0f,
	0xff, 0xff, 0xff, 0x83, 0xff, 0xff, 0xc1, 0xf0, 0x07, 0xff, 0xf0, 0x00, 0x1e, 0x0f, 0xfc, 0xe6,
	0x90, 0x00, 0x00, 0x07, 0x1e, 0x00, 0x78, 0x00, 0x0f, 0xff, 0xff, 0xff, 0x83, 0xff, 0xff, 0xc1,
	0xf0, 0x07, 0xc0, 0x00, 0x03, 0xe0, 0x00, 0x79, 0xc0, 0x00, 0x00, 0x00, 0x03, 0x8f, 0xf0, 0x78,
	0x3f, 0xf0, 0x7c, 0x1f, 0x00, 0x03, 0xff, 0xff, 0xc1, 0xf0, 0x07, 0xc0, 0x00, 0x03, 0xe0, 0x00,
	0x73, 0x80, 0x00, 0x00, 0x00, 0x01, 0xc7, 0xf0, 0x78, 0x3f, 0xf0, 0x7c, 0x1f, 0x00, 0x03, 0xff,
	0xff, 0xc1, 0xf0, 0x07, 0xc0, 0x00, 0x03, 0xe0, 0x00, 0x67, 0x3c, 0x00, 0x00, 0x00, 0x00, 0xe7,
	0xf0, 0x78, 0x3f, 0xf0, 0x7c, 0x1f, 0x00, 0x03, 0xff, 0xff, 0xc1, 0xf0, 0x07, 0xc0, 0x00, 0x03,
	0xe0, 0x00, 0x7e, 0x3e, 0x00, 0x00, 0x00, 0x00, 0x77, 0xf0, 0x78, 0x3f, 0xf0, 0x7c, 0x1f, 0x00,
	0x03, 0xff, 0xff, 0xc1, 0xf0, 0x07, 0xc0, 0x00, 0x03, 0xe0, 0x00, 0x6e, 0x2f, 0xff, 0xe0, 0x00,
	0x00, 0x77, 0xf0, 0x78, 0x3f, 0xf0, 0x7c, 0x1f, 0x00, 0x03, 0xff, 0xff, 0xfe, 0x0f, 0x00, 0x3f,
	0xf0, 0x7c, 0x1e, 0x0f, 0xfe, 0x3c, 0x00, 0x70, 0x00, 0x00, 0x76, 0x0f, 0x87, 0xfe, 0x0f, 0xff,
	0xff, 0xf0, 0x7f, 0xff, 0xff, 0xfe, 0x0f, 0x00, 0x3f, 0xf0, 0x7c, 0x1e, 0x0f, 0xfe, 0x38, 0x00,
	0x18, 0x00, 0x00, 0x76, 0x0f, 0x87, 0xfe, 0x0f, 0xff, 0xff, 0xf0, 0x7f, 0xff, 0xff, 0xfe, 0x0f,
	0x00, 0x3f, 0xf0, 0x7c, 0x1e, 0x0f, 0xfe, 0x18, 0x00, 0x08, 0x00, 0x00, 0x76, 0x0f, 0x87, 0xfe,
	0x0f, 0xff, 0xff, 0xf0, 0x7f, 0xff, 0xff, 0xfe, 0x0f, 0x00, 0x3f, 0xf0, 0x7c, 0x1e, 0x0f, 0xfe,
	0x18, 0x00, 0x04, 0x00, 0x00, 0x76, 0x0f, 0x87, 0xfe, 0x0f, 0xff, 0xff, 0xf0, 0x7f, 0xff, 0xff,
	0xfe, 0x0f, 0x00, 0x3f, 0xf0, 0x7c, 0x3e, 0x0f, 0xfe, 0x18, 0x00, 0x06, 0x00, 0x00, 0x76, 0x0f,
	0x87, 0xfe, 0x0f, 0xff, 0xff, 0xf0, 0x7f, 0xff, 0xff, 0xc0, 0x00, 0xff, 0xc0, 0x00, 0x7f, 0xfe,
	0x00, 0x7e, 0x18, 0x00, 0x06, 0x00, 0x00, 0x76, 0x0f, 0x87, 0xff, 0xf0, 0x03, 0xe0, 0xf0, 0x7f,
	0xff, 0xff, 0xc0, 0x00, 0xff, 0xc0, 0x00, 0x7f, 0xfe, 0x00, 0x7e, 0x18, 0x00, 0x06, 0x00, 0x00,
	0x76, 0x0f, 0x87, 0xff, 0xf0, 0x03, 0xe0, 0xf0, 0x7f, 0xff, 0xff, 0xc0, 0x00, 0xff, 0xc0, 0x00,
	0x7f, 0xfe, 0x00, 0x7e, 0x18, 0x00, 0x06, 0x00, 0x00, 0x76, 0x0f, 0x87, 0xff, 0xf0, 0x03, 0xe0,
	0xf0, 0x7f, 0xff, 0xff, 0xc0, 0x00, 0xff, 0xc0, 0x00, 0x7f, 0xfe, 0x00, 0x7e, 0x18, 0x00, 0x06,
	0x00, 0x00, 0x76, 0x0f, 0x87, 0xff, 0xf0, 0x03, 0xe0, 0xf0, 0x7f, 0xff, 0xff, 0xc0, 0x00, 0xff,
	0xc0, 0x00, 0x7f, 0xff, 0x00, 0x7e, 0x18, 0x00, 0x06, 0x00, 0x00, 0x76, 0x0f, 0x83, 0xff, 0xf0,
	0x03, 0xe0, 0xf0, 0x3f, 0xff, 0xff, 0xc0, 0x0f, 0xf8, 0x3f, 0xff, 0xfc, 0x1f, 0xff, 0xfe, 0x18,
	0x00, 0x06, 0x00, 0x10, 0x77, 0xff, 0x80, 0x01, 0xff, 0x83, 0xe0, 0xf0, 0x03, 0xff, 0xff, 0xc0,
	0x0f, 0xf8, 0x3f, 0xff, 0xfc, 0x1f, 0xff, 0xfe, 0x18, 0x00, 0x06, 0x00, 0x38, 0x77, 0xff, 0x80,
	0x01, 0xff, 0x83, 0xe0, 0xf0, 0x03, 0xff, 0xff, 0xc0, 0x0f, 0xf8, 0x3f, 0xff, 0xfc, 0x1f, 0xff,
	0xfe, 0x18, 0x00, 0x07, 0x00, 0x7c, 0x77, 0xff, 0x80, 0x01, 0xff, 0x83, 0xe0, 0xf0, 0x03, 0xff,
	0xff, 0xc0, 0x0f, 0xf8, 0x3f, 0xff, 0xfc, 0x1f, 0xff, 0xfe, 0x18, 0x00, 0x07, 0xff, 0xec, 0x77,
	0xff, 0x80, 0x01, 0xff, 0x83, 0xe0, 0xf0, 0x03, 0xff, 0xff, 0xc0, 0x0f, 0xf8, 0x3f, 0xff, 0xfc,
	0x3f, 0xff, 0xfe, 0x18, 0x00, 0x0c, 0x00, 0x78, 0x77, 0xff, 0x80, 0x01, 0xff, 0x83, 0xe1, 0xf0,
	0x03, 0xff, 0xff, 0xff, 0xf0, 0xf8, 0x3e, 0x00, 0x03, 0xfe, 0x0f, 0xfe, 0x18, 0x00, 0x18, 0x00,
	0x38, 0x76, 0x0f, 0xf8, 0x3f, 0xff, 0x83, 0xff, 0xf0, 0x03, 0xff, 0xff, 0xff, 0xf0, 0xf8, 0x3e,
	0x00, 0x03, 0xfe, 0x0f, 0xfe, 0x18, 0x00, 0x10, 0x00, 0x00, 0x76, 0x0f, 0xf8, 0x3f, 0xff, 0x83,
	0xff, 0xf0, 0x03, 0xff, 0xff, 0xff, 0xf0, 0xf8, 0x3e, 0x00, 0x03, 0xfe, 0x0f, 0xfe, 0x18, 0x00,
	0x60, 0x00, 0x00, 0x76, 0x0f, 0xf8, 0x3f, 0xff, 0x83, 0xff, 0xf0, 0x03, 0xff, 0xff, 0xff, 0xf0,
	0xf8, 0x3e, 0x00, 0x03, 0xfe, 0x0f, 0xfe, 0x18, 0x00, 0x60, 0x00, 0x00, 0x76, 0x0f, 0xf8, 0x3f,
	0xff, 0x83, 0xff, 0xf0, 0x03, 0xff, 0xff, 0xff, 0xf0, 0x00, 0x01, 0xf0, 0x03, 0xe1, 0xf0, 0x7e,
	0x18, 0x00, 0x60, 0x00, 0x00, 0x76, 0x0f, 0x87, 0xc0, 0x0f, 0xfc, 0x3f, 0xf0, 0x3f, 0xff, 0xff,
	0xff, 0xf0, 0x00, 0x01, 0xf0, 0x03, 0xe1, 0xf0, 0x7e, 0x18, 0x00, 0x60, 0x00, 0x00, 0x76, 0x0f,
	0x87, 0xc0, 0x0f, 0xfc, 0x1f, 0xf0, 0x7f, 0xff, 0xff, 0xff, 0xf0, 0x00, 0x01, 0xf0, 0x03, 0xe1,
	0xf0, 0x7e, 0x18, 0x00, 0x60, 0x00, 0x00, 0x76, 0x0f, 0x87, 0xc0, 0x0f, 0xfc, 0x1f, 0xf0, 0x7f,
	0xff, 0xff, 0xff, 0xf0, 0x00, 0x01, 0xf0, 0x03, 0xe1, 0xf0, 0x7e, 0x18, 0x00, 0x60, 0x00, 0x00,
	0x76, 0x0f, 0x87, 0xc0, 0x0f, 0xfc, 0x1f, 0xf0, 0x7f, 0xff, 0xff, 0xff, 0xf0, 0x00, 0x01, 0xf0,
	0x03, 0xe1, 0xf0, 0x7e, 0x18, 0x00, 0x7f, 0xf8, 0x00, 0x76, 0x0f, 0x87, 0xc0, 0x0f, 0xfc, 0x1f,
	0xf0, 0x7f, 0xff, 0xff, 0xff, 0xf0, 0x7f, 0xc0, 0x00, 0x03, 0xfe, 0x00, 0x7e, 0x18, 0x00, 0x7f,
	0xf8, 0x00, 0x77, 0xff, 0x87, 0xc0, 0x00, 0x7f, 0xff, 0xf0, 0x03, 0xff, 0xff, 0xff, 0xf0, 0xff,
	0xc0, 0x00, 0x03, 0xfe, 0x00, 0x7e, 0x18, 0x00, 0x60, 0x00, 0x00, 0x77, 0xff, 0x87, 0xc0, 0x00,
	0x7f, 0xff, 0xf0, 0x03, 0xff, 0xff, 0xff, 0xf0, 0xff, 0xc0, 0x00, 0x03, 0xfe, 0x00, 0x7e, 0x18,
	0x00, 0x60, 0x00, 0x00, 0x77, 0xff, 0x87, 0xc0, 0x00, 0x7f, 0xff, 0xf0, 0x03, 0xff, 0xff, 0xff,
	0xf0, 0xff, 0xc0, 0x00, 0x03, 0xfe, 0x00, 0x7e, 0x18, 0x00, 0x60, 0x00, 0x00, 0x77, 0xff, 0x87,
	0xc0, 0x00, 0x7f, 0xff, 0xf0, 0x03, 0xff, 0xff, 0xff, 0xf0, 0xff, 0xc0, 0x00, 0x03, 0xfe, 0x00,
	0x7e, 0x18, 0x00, 0x60, 0x00, 0x00, 0x77, 0xff, 0x87, 0xc0, 0x00, 0x7f, 0xff, 0xf0, 0x03, 0xff,
	0xff, 0xfe, 0x0f, 0xf8, 0x3f, 0xf0, 0x03, 0xe1, 0xff, 0xfe, 0x18, 0x00, 0x60, 0x00, 0x00, 0x76,
	0x00, 0x7f, 0xfe, 0x00, 0x03, 0xe0, 0xf0, 0x7f, 0xff, 0xff, 0xfe, 0x0f, 0xf8, 0x3f, 0xf0, 0x03,
	0xe1, 0xff, 0xfe, 0x18, 0x00, 0x60, 0x00, 0x00, 0x76, 0x00, 0x7f, 0xfe, 0x00, 0x03, 0xe0, 0xf0,
	0x7f, 0xff, 0xff, 0xfe, 0x0f, 0xf8, 0x3f, 0xf0, 0x03, 0xe1, 0xff, 0xfe, 0x18, 0x00, 0x30, 0x00,
	0x00, 0x76, 0x00, 0x7f, 0xfe, 0x00, 0x03, 0xe0, 0xf0, 0x7f, 0xff, 0xff, 0xfe, 0x0f, 0xf8, 0x3f,
	0xf0, 0x03, 0xe1, 0xff, 0xfe, 0x38, 0x00, 0x18, 0x00, 0x30, 0x76, 0x00, 0x7f, 0xfe, 0x00, 0x03,
	0xe0, 0xf0, 0x7f, 0xff, 0xff, 0xfe, 0x0f, 0xf8, 0x3f, 0xf0, 0x03, 0xe1, 0xff, 0xfe, 0x3c, 0x00,
	0x0c, 0x00, 0x78, 0x76, 0x00, 0x7f, 0xfe, 0x00, 0x03, 0xe0, 0xf0, 0x7f, 0xff, 0xff, 0xfe, 0x0f,
	0x07, 0xc0, 0x0f, 0xff, 0xe0, 0x0f, 0xee, 0x6c, 0x00, 0x07, 0xff, 0xe8, 0x77, 0xf0, 0x00, 0x01,
	0xff, 0xfc, 0x00, 0xff, 0x83, 0xff, 0xff, 0xfe, 0x0f, 0x07, 0xc0, 0x0f, 0xff, 0xe0, 0x0f, 0xfe,
	0x7c, 0x00, 0x03, 0xff, 0xec, 0x77, 0xf0, 0x00, 0x01, 0xff, 0xfc, 0x00, 0xff, 0x83, 0xff, 0xff,
	0xfe, 0x0f, 0x07, 0xc0, 0x0f, 0xff, 0xe0, 0x0f, 0xff, 0x7c, 0x00, 0x00, 0x00, 0x78, 0xf7, 0xf0,
	0x00, 0x01, 0xff, 0xfc, 0x00, 0xff, 0x83, 0xff, 0xff, 0xfe, 0x0f, 0x07, 0xc0, 0x0f, 0xff, 0xe0,
	0x0f, 0xf7, 0x90, 0x00, 0x00, 0x00, 0x38, 0xe7, 0xf0, 0x00, 0x01, 0xff, 0xfc, 0x00, 0xff, 0x83,
	0xff, 0xff, 0xfe, 0x0f, 0x07, 0xc0, 0x0f, 0xff, 0xe0, 0x0f, 0xe3, 0xc0, 0x00, 0x00, 0x00, 0x03,
	0xc7, 0xf0, 0x00, 0x01, 0xff, 0xfc, 0x00, 0xff, 0x83, 0xff, 0xff, 0xff, 0xf0, 0x00, 0x3f, 0xff,
	0x83, 0xe1, 0xff, 0xf1, 0xc0, 0x00, 0x00, 0x00, 0x03, 0x8f, 0xf0, 0x7f, 0xfe, 0x0f, 0x83, 0xff,
	0xff, 0x83, 0xff, 0xff, 0xff, 0xf0, 0x00, 0x3f, 0xff, 0x83, 0xe1, 0xff, 0xf8, 0xff, 0xff, 0xff,
	0xff, 0xff, 0x1f, 0xf0, 0x7f, 0xfe, 0x0f, 0x83, 0xff, 0xff, 0x83, 0xff, 0xff, 0xff, 0xf0, 0x00,
	0x3f, 0xff, 0x83, 0xe1, 0xff, 0xfc, 0x7f, 0xff, 0xff, 0xff, 0xfe, 0x3f, 0xf0, 0x7f, 0xfe, 0x0f,
	0x83, 0xff, 0xff, 0x83, 0xff, 0xff, 0xff, 0xf0, 0x00, 0x3f, 0xff, 0x83, 0xe1, 0xff, 0xfe, 0x00,
	0x00, 0x00, 0x00, 0x00, 0x7f, 0xf0, 0x7f, 0xfe, 0x0f, 0x83, 0xff, 0xff, 0x83, 0xff, 0xff, 0xff,
	0xf0, 0x00, 0x3f, 0xff, 0x83, 0xe1, 0xff, 0xfe, 0x00, 0x00, 0x00, 0x00, 0x00, 0xff, 0xf0, 0x7f,
	0xfe, 0x0f, 0x83, 0xff, 0xff, 0x83, 0xff, 0xff, 0xc1, 0xf0, 0xff, 0xfe, 0x00, 0x03, 0xe1, 0xff,
	0xff, 0xff, 0xff, 0xc3, 0xe0, 0xf8, 0x00, 0x0f, 0x80, 0x3f, 0xf0, 0x00, 0x00, 0xff, 0x83, 0xff,
	0xff, 0xc1, 0xf0, 0xff, 0xfe, 0x00, 0x03, 0xe1, 0xff, 0xff, 0xff, 0xff, 0xc3, 0xe0, 0xf8, 0x00,
	0x0f, 0x80, 0x3f, 0xf0, 0x00, 0x00, 0xff, 0x83, 0xff, 0xff, 0xc1, 0xf0, 0xff, 0xfe, 0x00, 0x03,
	0xe1, 0xff, 0xff, 0xff, 0xff, 0xc3, 0xe0, 0xf8, 0x00, 0x0f, 0x80, 0x3f, 0xf0, 0x00, 0x00, 0xff,
	0x83, 0xff, 0xff, 0xc1, 0xf0, 0xff, 0xfe, 0x00, 0x03, 0xe1, 0xff, 0xff, 0xff, 0xff, 0xc3, 0xe0,
	0xf8, 0x00, 0x0f, 0x80, 0x3f, 0xf0, 0x00, 0x00, 0xff, 0x83, 0xff, 0xff, 0xc1, 0xf0, 0x7f, 0xfe,
	0x00, 0x03, 0xe1, 0xff, 0xff, 0xff, 0xff, 0xc3, 0xe0, 0xf8, 0x00, 0x0f, 0x80, 0x3f, 0xf0, 0x00,
	0x00, 0xff, 0x83, 0xff, 0xff, 0xc1, 0xf0, 0x07, 0xc1, 0xff, 0x83, 0xe1, 0xf0, 0x00, 0x00, 0x07,
	0xc3, 0xff, 0x00, 0x3f, 0xff, 0xf8, 0x3e, 0x0f, 0xff, 0xe0, 0xf0, 0x03, 0xff, 0xff, 0xc1, 0xf0,
	0x07, 0xc1, 0xff, 0x83, 0xe1, 0xf0, 0x00, 0x00, 0x07, 0xc3, 0xff, 0x00, 0x3f, 0xff, 0xf8, 0x3e,
	0x0f, 0xff, 0xe0, 0xf0, 0x03, 0xff, 0xff, 0xc1, 0xf0, 0x07, 0xc1, 0xff, 0x83, 0xe1, 0xf0, 0x00,
	0x00, 0x07, 0xc3, 0xff, 0x00, 0x3f, 0xff, 0xf8, 0x3e, 0x0f, 0xff, 0xe0, 0xf0, 0x03, 0xff, 0xff,
	0xc1, 0xf0, 0x07, 0xc1, 0xff, 0x83, 0xe1, 0xf0, 0x00, 0x00, 0x07, 0xc3, 0xff, 0x00, 0x3f, 0xff,
	0xf8, 0x3e, 0x0f, 0xff, 0xe0, 0xf0, 0x03, 0xff, 0xff, 0xc1, 0xf0, 0x07, 0xc1, 0xff, 0x83, 0xe1,
	0xf0, 0x00, 0x00, 0x0f, 0xc1, 0xff, 0x00, 0x3f, 0xff, 0xf8, 0x1e, 0x0f, 0xff, 0xe0, 0xf0, 0x03,
	0xff, 0xff, 0xc1, 0xff, 0x07, 0xc0, 0x0f, 0x80, 0x1f, 0xf0, 0x7f, 0xff, 0xff, 0xc0, 0x1f, 0xf8,
	0x00, 0x0f, 0x80, 0x01, 0xff, 0xff, 0xe0, 0xf0, 0x03, 0xff, 0xff, 0xc1, 0xff, 0x07, 0xc0, 0x0f,
	0x80, 0x1f, 0xf0, 0x7f, 0xff, 0xff, 0xc0, 0x1f, 0xf8, 0x00, 0x0f, 0x80, 0x01, 0xff, 0xff, 0xe0,
	0xf0, 0x03, 0xff, 0xff, 0xc1, 0xff, 0x07, 0xc0, 0x0f, 0x80, 0x1f, 0xf0, 0x7f, 0xff, 0xff, 0xc0,
	0x1f, 0xf8, 0x00, 0x0f, 0x80, 0x01, 0xff, 0xff, 0xe0, 0xf0, 0x03, 0xff, 0xff, 0xc1, 0xff, 0x07,
	0xc0, 0x0f, 0x80, 0x1f, 0xf0, 0x7f, 0xff, 0xff, 0xc0, 0x1f, 0xf8, 0x00, 0x0f, 0x80, 0x01, 0xff,
	0xff, 0xe0, 0xf0, 0x03, 0xff, 0xff, 0xc1, 0xf0, 0xff, 0xc1, 0xf0, 0x03, 0xff, 0x00, 0x03, 0xe1,
	0xf8, 0x3f, 0xff, 0x80, 0x01, 0xff, 0x83, 0xff, 0xff, 0x83, 0xe0, 0xff, 0xff, 0xff, 0xff, 0xc1,
	0xf0, 0xff, 0xc1, 0xf0, 0x03, 0xfe, 0x00, 0x03, 0xe0, 0xf8, 0x3f, 0xff, 0x00, 0x01, 0xff, 0x87,
	0xff, 0xff, 0x83, 0xe0, 0xff, 0xff, 0xff, 0xff, 0xc1, 0xf0, 0xff, 0xc1, 0xf0, 0x03, 0xfe, 0x00,
	0x03, 0xe0, 0xf8, 0x3f, 0xff, 0x00, 0x01, 0xff, 0x87, 0xff, 0xff, 0x83, 0xe0, 0xff, 0xff, 0xff,
	0xff, 0xc1, 0xf0, 0xff, 0xc1, 0xf0, 0x03, 0xfe, 0x00, 0x03, 0xe0, 0xf8, 0x3f, 0xff, 0x00, 0x01,
	0xff, 0x87, 0xff, 0xff, 0x83, 0xe0, 0xff, 0xff, 0xff, 0xff, 0xc1, 0xf0, 0xff, 0xc1, 0xf0, 0x03,
	0xfe, 0x00, 0x03, 0xe0, 0xf8, 0x3f, 0xff, 0x00, 0x01, 0xff, 0x87, 0xff, 0xff, 0x83, 0xe0, 0xff,
	0xff, 0xff, 0xff, 0xc1, 0xf0, 0xf8, 0x00, 0x00, 0x7c, 0x01, 0xff, 0x83, 0xe0, 0xff, 0xff, 0xe0,
	0xf8, 0x00, 0x00, 0x7f, 0xe0, 0x00, 0x00, 0x1f, 0x0f, 0xff, 0xff, 0xff, 0xc1, 0xf0, 0xf8, 0x00,
	0x00, 0x7c, 0x01, 0xff, 0x83, 0xe0, 0xff, 0xff, 0xe0, 0xf8, 0x00, 0x00, 0x7f, 0xc0, 0x00, 0x00,
	0x1f, 0x0f, 0xff, 0xff, 0xff, 0xc1, 0xf0, 0xf8, 0x00, 0x00, 0x7c, 0x01, 0xff, 0x83, 0xe0, 0xff,
	0xff, 0xe0, 0xf8, 0x00, 0x00, 0x7f, 0xc0, 0x00, 0x00, 0x1f, 0x0f, 0xff, 0xff, 0xff, 0xc1, 0xf0,
	0xf8, 0x00, 0x00, 0x7c, 0x01, 0xff, 0x83, 0xe0, 0xff, 0xff, 0xe0, 0xf8, 0x00, 0x00, 0x7f, 0xc0,
	0x00, 0x00, 0x1f, 0x0f, 0xff, 0xff, 0xff, 0xc1, 0xf0, 0xf8, 0x00, 0x00, 0x7c, 0x01, 0xff, 0x83,
	0xe0, 0xff, 0xff, 0xe0, 0xf8, 0x00, 0x00, 0x7f, 0xc0, 0x00, 0x00, 0x1f, 0x0f, 0xff, 0xff, 0xff,
	0xff, 0xff, 0xff, 0xff, 0xff, 0x80, 0x01, 0xf0, 0x03, 0xe0, 0x00, 0x7e, 0x00, 0x07, 0xff, 0xff,
	0xff, 0xc1, 0xff, 0xfc, 0x00, 0x0f, 0x83, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x80, 0x01,
	0xf0, 0x03, 0xe0, 0x00, 0x3c, 0x00, 0x07, 0xff, 0xff, 0xff, 0xc1, 0xff, 0xfc, 0x00, 0x0f, 0x83,
	0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x80, 0x01, 0xf0, 0x03, 0xe0, 0x00, 0x3c, 0x00, 0x07,
	0xff, 0xff, 0xff, 0xc1, 0xff, 0xfc, 0x00, 0x0f, 0x83, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
	0x80, 0x01, 0xf0, 0x03, 0xe0, 0x00, 0x3c, 0x00, 0x07, 0xff, 0xff, 0xff, 0xc1, 0xff, 0xfc, 0x00,
	0x0f, 0x83, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x80, 0x01, 0xf0, 0x03, 0xe0, 0x00, 0x3c,
	0x00, 0x07, 0xff, 0xff, 0xff, 0xc1, 0xff, 0xfc, 0x00, 0x0f, 0x83, 0xff, 0xff, 0xc0, 0x00, 0x00,
	0x00, 0x0f, 0xfc, 0x1f, 0xf0, 0x03, 0xe0, 0x07, 0xc0, 0x00, 0xff, 0xfe, 0x0f, 0x87, 0xc1, 0xf0,
	0x7c, 0x1f, 0xf0, 0x03, 0xff, 0xff, 0xc0, 0x00, 0x00, 0x00, 0x0f, 0xfc, 0x1f, 0xf0, 0x03, 0xe0,
	0x07, 0xc0, 0x00, 0xff, 0xfe, 0x0f, 0x87, 0xc1, 0xf0, 0x7c, 0x1f, 0xf0, 0x03, 0xff, 0xff, 0xc0,
	0x00, 0x00, 0x00, 0x0f, 0xfc, 0x1f, 0xf0, 0x03, 0xe0, 0x07, 0xc0, 0x00, 0xff, 0xfe, 0x0f, 0x87,
	0xc1, 0xf0, 0x7c, 0x1f, 0xf0, 0x03, 0xff, 0xff, 0xc0, 0x00, 0x00, 0x00, 0x0f, 0xfc, 0x1f, 0xf0,
	0x03, 0xe0, 0x07, 0xc0, 0x00, 0xff, 0xfe, 0x0f, 0x87, 0xc1, 0xf0, 0x7c, 0x1f, 0xf0, 0x03, 0xff,
	0xff, 0xc0, 0x00, 0x00, 0x00, 0x0f, 0xfc, 0x1f, 0xf0, 0x03, 0xe0, 0x07, 0xc0, 0x00, 0xff, 0xfe,
	0x0f, 0x87, 0xc1, 0xf0, 0x7c, 0x1f, 0xf0, 0x03, 0xff, 0xff, 0xc1, 0xff, 0xff, 0xfe, 0x0f, 0xff,
	0xfe, 0x0f, 0xff, 0xff, 0xff, 0xfc, 0x1f, 0xff, 0xc0, 0x0f, 0xf8, 0x01, 0xff, 0xfc, 0x1f, 0xf0,
	0x03, 0xff, 0xff, 0xc1, 0xff, 0xff, 0xfe, 0x0f, 0xff, 0xfe, 0x0f, 0xff, 0xff, 0xff, 0xfc, 0x1f,
	0xff, 0xc0, 0x0f, 0xf8, 0x01, 0xff, 0xfc, 0x1f, 0xf0, 0x03, 0xff, 0xff, 0xc1, 0xff, 0xff, 0xfe,
	0x0f, 0xff, 0xfe, 0x0f, 0xff, 0xff, 0xff, 0xfc, 0x1f, 0xff, 0xc0, 0x0f, 0xf8, 0x01, 0xff, 0xfc,
	0x1f, 0xf0, 0x03, 0xff, 0xff, 0xc1, 0xff, 0xff, 0xfe, 0x0f, 0xff, 0xfe, 0x0f, 0xff, 0xff, 0xff,
	0xfc, 0x1f, 0xff, 0xc0, 0x0f, 0xf8, 0x01, 0xff, 0xfc, 0x1f, 0xf0, 0x03, 0xff, 0xff, 0xc1, 0xff,
	0xff, 0xfe, 0x0f, 0xff, 0xfe, 0x0f, 0xff, 0xff, 0xff, 0xfc, 0x1f, 0xff, 0xc0, 0x0f, 0xf8, 0x01,
	0xff, 0xfc, 0x1f, 0xf0, 0x03, 0xff, 0xff, 0xc1, 0xf0, 0x00, 0x3e, 0x0f, 0x80, 0x01, 0xff, 0x80,
	0x00, 0x07, 0xc3, 0xff, 0x00, 0x3f, 0xf0, 0x78, 0x00, 0x00, 0x00, 0x00, 0xf0, 0x7f, 0xff, 0xff,
	0xc1, 0xf0, 0x00, 0x3e, 0x0f, 0x80, 0x01, 0xff, 0x80, 0x00, 0x07, 0xc3, 0xff, 0x00, 0x3f, 0xf0,
	0x78, 0x00, 0x00, 0x00, 0x00, 0xf0, 0x7f, 0xff, 0xff, 0xc1, 0xf0, 0x00, 0x3e, 0x0f, 0x80, 0x01,
	0xff, 0x80, 0x00, 0x07, 0xc3, 0xff, 0x00, 0x3f, 0xf0, 0x78, 0x00, 0x00, 0x00, 0x00, 0xf0, 0x7f,
	0xff, 0xff, 0xc1, 0xf0, 0x00, 0x3e, 0x0f, 0x80, 0x01, 0xff, 0x80, 0x00, 0x07, 0xc3, 0xff, 0x00,
	0x3f, 0xf0, 0x78, 0x00, 0x00, 0x00, 0x00, 0xf0, 0x7f, 0xff, 0xff, 0xc1, 0xf0, 0x00, 0x3e, 0x0f,
	0x80, 0x01, 0xff, 0x80, 0x00, 0x07, 0xc3, 0xff, 0x00, 0x3f, 0xf0, 0x78, 0x00, 0x00, 0x00, 0x00,
	0xf8, 0x7f, 0xff, 0xff, 0xc1, 0xf0, 0x00, 0x3e, 0x0f, 0x83, 0xfe, 0x00, 0x7f, 0xe0, 0xff, 0xff,
	0xff, 0xf8, 0x3e, 0x0f, 0x80, 0x3e, 0x00, 0x00, 0x00, 0xff, 0xff, 0xff, 0xff, 0xc1, 0xf0, 0x00,
	0x3e, 0x0f, 0x83, 0xfe, 0x00, 0x7f, 0xe0, 0xff, 0xff, 0xff, 0xf8, 0x3e, 0x0f, 0x80, 0x3e, 0x00,
	0x00, 0x00, 0xff, 0xff, 0xff, 0xff, 0xc1, 0xf0, 0x00, 0x3e, 0x0f, 0x83, 0xfe, 0x00, 0x7f, 0xe0,
	0xff, 0xff, 0xff, 0xf8, 0x3e, 0x0f, 0x80, 0x3e, 0x00, 0x00, 0x00, 0xff, 0xff, 0xff, 0xff, 0xc1,
	0xf0, 0x00, 0x3e, 0x0f, 0x83, 0xfe, 0x00, 0x7f, 0xe0, 0xff, 0xff, 0xff, 0xf8, 0x3e, 0x0f, 0x80,
	0x3e, 0x00, 0x00, 0x00, 0xff, 0xff, 0xff, 0xff, 0xc1, 0xf0, 0x00, 0x3e, 0x0f, 0x83, 0xfe, 0x00,
	0x7f, 0xe0, 0xff, 0xff, 0xff, 0xf8, 0x3e, 0x1f, 0x80, 0x3e, 0x00, 0x00, 0x00, 0xff, 0xff, 0xff,
	0xff, 0xc1, 0xf0, 0x00, 0x3e, 0x0f, 0x80, 0x01, 0xf0, 0x7f, 0xff, 0xff, 0xc0, 0x1f, 0xf8, 0x3f,
	0xff, 0xf8, 0x3e, 0x00, 0x7f, 0xff, 0x0f, 0x83, 0xff, 0xff, 0xc1, 0xf0, 0x00, 0x3e, 0x0f, 0x80,
	0x01, 0xf0, 0x7f, 0xff, 0xff, 0xc0, 0x1f, 0xf8, 0x3f, 0xff, 0xf8, 0x3e, 0x00, 0x7f, 0xff, 0x0f,
	0x83, 0xff, 0xff, 0xc1, 0xf0, 0x00, 0x3e, 0x0f, 0x80, 0x01, 0xf0, 0x7f, 0xff, 0xff, 0xc0, 0x1f,
	0xf8, 0x3f, 0xff, 0xf8, 0x3e, 0x00, 0x7f, 0xff, 0x0f, 0x83, 0xff, 0xff, 0xc1, 0xf0, 0x00, 0x3e,
	0x0f, 0x80, 0x01, 0xf0, 0x7f, 0xff, 0xff, 0xc0, 0x1f, 0xf8, 0x3f, 0xff, 0xf8, 0x3e, 0x00, 0x7f,
	0xff, 0x0f, 0x83, 0xff, 0xff, 0xc1, 0xff, 0xff, 0xfe, 0x0f, 0xff, 0xff, 0xf0, 0x03, 0xff, 0xf8,
	0x01, 0xe0, 0xf8, 0x3e, 0x1f, 0xf8, 0x3e, 0x0f, 0x87, 0xff, 0xff, 0x83, 0xff, 0xff, 0xc1, 0xff,
	0xff, 0xfe, 0x0f, 0xff, 0xff, 0xf0, 0x03, 0xff, 0xf8, 0x03, 0xe0, 0xf8, 0x3e, 0x0f, 0xf8, 0x3e,
	0x0f, 0x83, 0xff, 0xff, 0x83, 0xff, 0xff, 0xc1, 0xff, 0xff, 0xfe, 0x0f, 0xff, 0xff, 0xf0, 0x03,
	0xff, 0xf8, 0x03, 0xe0, 0xf8, 0x3e, 0x0f, 0xf8, 0x3e, 0x0f, 0x83, 0xff, 0xff, 0x83, 0xff, 0xff,
	0xc1, 0xff, 0xff, 0xfe, 0x0f, 0xff, 0xff, 0xf0, 0x03, 0xff, 0xf8, 0x03, 0xe0, 0xf8, 0x3e, 0x0f,
	0xf8, 0x3e, 0x0f, 0x83, 0xff, 0xff, 0x83, 0xff, 0xff, 0xc1, 0xff, 0xff, 0xfe, 0x0f, 0xff, 0xff,
	0xf0, 0x03, 0xff, 0xf8, 0x03, 0xe0, 0xf8, 0x3e, 0x0f, 0xf8, 0x3e, 0x0f, 0x83, 0xff, 0xff, 0x83,
	0xff, 0xff, 0xc0, 0x00, 0x00, 0x00, 0x0f, 0xff, 0xff, 0x0f, 0xfc, 0x1f, 0xff, 0x83, 0xe0, 0x07,
	0xff, 0xf0, 0xff, 0xfe, 0x00, 0x7f, 0xff, 0xf8, 0x03, 0xff, 0xff, 0xc0, 0x00, 0x00, 0x00, 0x0f,
	0xff, 0xfe, 0x0f, 0xfc, 0x1f, 0xff, 0xc3, 0xe0, 0x07, 0xff, 0xf0, 0x7f, 0xfe, 0x00, 0x7f, 0xff,
	0xf0, 0x03, 0xff, 0xff, 0xc0, 0x00, 0x00, 0x00, 0x0f, 0xff, 0xfe, 0x0f, 0xfc, 0x1f, 0xff, 0xc3,
	0xe0, 0x07, 0xff, 0xf0, 0x7f, 0xfe, 0x00, 0x7f, 0xff, 0xf0, 0x03, 0xff, 0xff, 0xc0, 0x00, 0x00,
	0x00, 0x0f, 0xff, 0xfe, 0x0f, 0xfc, 0x1f, 0xff, 0xc3, 0xe0, 0x07, 0xff, 0xf0, 0x7f, 0xfe, 0x00,
	0x7f, 0xff, 0xf0, 0x03, 0xff, 0xff, 0xc0, 0x00, 0x00, 0x00, 0x0f, 0xff, 0xfe, 0x0f, 0xfc, 0x1f,
	0xff, 0xc3, 0xe0, 0x07, 0xff, 0xf0, 0x7f, 0xfe, 0x00, 0x7f, 0xff, 0xf0, 0x03, 0xff, 0xff, 0xff,
	0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
	0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
	0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
	0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
	0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
	0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
	0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
	0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
	0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
	0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
	0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
	0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
	0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
	0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
	0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
	0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff};

// 'wifi_ok', 37x45px
const uint16_t wifi_ok[37][45] PROGMEM = {
	0xff, 0xff, 0xff, 0xff, 0xf8, 0xff, 0xff, 0xff, 0xff, 0xf8, 0xff, 0xff, 0xff, 0xff, 0xf8, 0xff,
	0xff, 0xff, 0xff, 0xf8, 0xff, 0xff, 0xff, 0xff, 0xf8, 0xfe, 0x7f, 0xff, 0xe3, 0xf8, 0xfc, 0x3f,
	0xff, 0xe1, 0xf8, 0xf8, 0x3f, 0xff, 0xe1, 0xf8, 0xf8, 0x7f, 0xff, 0xf0, 0xf8, 0xf0, 0xfb, 0xfe,
	0xf8, 0xf8, 0xf0, 0xe1, 0xfc, 0x78, 0x78, 0xf1, 0xe1, 0xfc, 0x3c, 0x78, 0xe1, 0xc3, 0xfc, 0x3c,
	0x78, 0xe1, 0xc3, 0xfe, 0x1c, 0x38, 0xe3, 0xc7, 0x8e, 0x1c, 0x38, 0xe3, 0xc7, 0x8f, 0x1c, 0x38,
	0xe3, 0xc7, 0x8e, 0x1c, 0x38, 0xe1, 0xc7, 0x8e, 0x1c, 0x38, 0xe1, 0xc3, 0x8e, 0x3c, 0x78, 0xe1,
	0xe1, 0x8c, 0x3c, 0x78, 0xf1, 0xe1, 0x8c, 0x78, 0x78, 0xf0, 0xf3, 0x8c, 0x78, 0x78, 0xf8, 0x7f,
	0x8f, 0xf0, 0xf8, 0xf8, 0x7f, 0x8f, 0xe0, 0xf8, 0xfc, 0x3f, 0x8f, 0xe1, 0xf8, 0xfc, 0x3f, 0x8f,
	0xe3, 0xf8, 0xff, 0xff, 0x8f, 0xff, 0xf8, 0xff, 0xff, 0x8f, 0xff, 0xf8, 0xff, 0xff, 0x8f, 0xff,
	0xf8, 0xff, 0xff, 0x8f, 0xff, 0xf8, 0xff, 0xff, 0x8f, 0xff, 0xf8, 0xff, 0xff, 0x8f, 0xff, 0xf8,
	0xff, 0xff, 0x8f, 0xff, 0xf8, 0xff, 0xff, 0x8f, 0xff, 0xf8, 0xff, 0xff, 0x8f, 0xff, 0xf8, 0xff,
	0xff, 0x8f, 0xff, 0xf8, 0xff, 0xff, 0xff, 0xff, 0xf8, 0xff, 0xff, 0xff, 0xff, 0xf8, 0xff, 0xff,
	0xff, 0xff, 0xf8, 0xff, 0xff, 0xff, 0xff, 0xf8, 0xff, 0xff, 0xff, 0xff, 0xf8, 0xff, 0xff, 0xff,
	0xff, 0xf8, 0xff, 0xff, 0xff, 0xff, 0xf8, 0xff, 0xff, 0xff, 0xff, 0xf8, 0xff, 0xff, 0xff, 0xff,
	0xf8};
