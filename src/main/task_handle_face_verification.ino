
#include <Arduino.h>
#include "FaceScanner.h"
#include <main.h>
extern FaceScanner faceScanner;
extern TFT_eSPI tft;
extern volatile bool enableDateTimeUpdate;
extern int face;
extern String face_string;
extern uint8_t verifyThreshold;
extern uint8_t livenessThreshold;
extern uint8_t facemoduleversion;
// Function declaration for the task
void TaskHandleFaceVerification(void *pvParameters)
{
  // Constants for display coordinates and buffer sizes
  const TickType_t xDelay = pdMS_TO_TICKS(100);  // 100ms polling interval
  const uint8_t EMP_ID_LENGTH = 6;               // Employee ID length
  const uint8_t USERNAME_PART_LENGTH = 20;       // User name part length
  const uint16_t RESULT_DISPLAY_DURATION = 1500; // 2 seconds

  uint16_t userId;   // User ID from verification
  char userName[33]; // 32 chars + null terminator
  bool isAdmin;      // Admin flag

  // Set verification threshold
  faceScanner.setThresholdLevel(verifyThreshold, livenessThreshold);
  int prevFace = 0;

  for (;;)
  {
    if(face != prevFace)
    {
      Serial.println("Face changed from " + String(prevFace) + " to " + String(face));
      prevFace = face;
    }
    else
    {
      //Serial.println("Face remains " + String(face));
    }

    // Check if face verification is enabled
    if (face == 0)
    { // NBP
      if (digitalRead(MOTION_SENSOR_PIN) == HIGH)
      {

        if (faceScanner.verify(false, 10, 0, &userId, userName, &isAdmin))
        {
          // Face verification
          START_BEEP(1.0f);

          // Extract employee ID and name
          char empId[EMP_ID_LENGTH + 1];
          strncpy(empId, userName, EMP_ID_LENGTH);
          empId[EMP_ID_LENGTH] = '\0';

          char userNamePart[USERNAME_PART_LENGTH + 1];
          strncpy(userNamePart, userName + EMP_ID_LENGTH, USERNAME_PART_LENGTH);
          userNamePart[USERNAME_PART_LENGTH] = '\0';

          // Update global employee information
          currentEmpId = String(empId);
          currentEmpName = String(userNamePart);
          isValidEmployee = true; // NBP how to prevent this in card read ???

          // writetotft(int line_number, const char* text)
          writetotft(31, empId);
          writetotft(32, userNamePart);

          // Display verification result
          uint16_t FACEMAX;
          if(facemoduleversion == 2)
          {
            FACEMAX = 100;
          }
          else if(facemoduleversion == 5)
          {
            FACEMAX = 500;
          }
          else if(facemoduleversion == 30)
          {
            FACEMAX = 2000;
          }
          if (userId <= FACEMAX)
          {
            writetotft(33, "Face");
          }
          else
          {
            writetotft(33, "Palm");
          }

          // --- Introduce Delay ---
          // Wait for the user to see the verification result
          vTaskDelay(pdMS_TO_TICKS(RESULT_DISPLAY_DURATION));

          memset(userName, 0, sizeof(userName)); // Clear userName
          showMenuScreen(userId <= 100 ? "face" : "palm");
          face = 0;
        }
        else
        {
          //writetotft(0, "Verification Failed");
          // vTaskDelay(pdMS_TO_TICKS(RESULT_DISPLAY_DURATION));
          // showMenuScreen("face");
          // face = 0;
          ERROR_BEEP(0.5f,2);
          //delay(1000);
          //writetotft(0, "                    ");

        }
      }
    }

    // other cases here...

    else if (face == 1)
    {
      Serial.print("IN FACE ENROLL");
      faceScanner.reset();
      delay(2000);
      Serial.println("Testing enrollSingle command...");
      if (faceScanner.enrollSingle(false, face_string.c_str(), 0, 10, &userId))
      {
        Serial.print("enrollSingle succeeded. User ID: ");
        Serial.println(userId);
      }
      else
      {
        Serial.println("enrollSingle command failed.");
      }
      face = 0;
    }

    else if (face == 2)
    {
      Serial.print("IN USER LIST");
      uint8_t userCount;
      uint16_t userIds[200];
      Serial.println("Testing getAllUserIds command...");
      if (faceScanner.getAllUserIds(fmt, &userCount, userIds))
      {
        Serial.print("User Count: ");
        Serial.println(userCount);
        for (uint8_t i = 0; i < userCount; i++)
        {
          Serial.print("User ID ");
          Serial.print(i);
          Serial.print(": ");
          Serial.println(userIds[i]);
          if (faceScanner.getUserInfo(userIds[i], userName, &isAdmin))
          {
            Serial.print("User Name: ");
            Serial.println(userName);
            Serial.print("Is Admin: ");
            Serial.println(isAdmin ? "Yes" : "No");
          }
        }
      }
      else
      {
        Serial.println("getAllUserIds command failed.");
      }
      face = 0;
    }

    else if (face == 3)
    {
      Serial.print("IN PALM ENROLL");
      faceScanner.reset();
      delay(2000);
      Serial.println("Testing enrollSingle command...");
      if (faceScanner.enrollSingle(false, face_string.c_str(), FACE_DIRECTION_HAND, 10, &userId))
      {
        Serial.print("enrollSingle succeeded. User ID: ");
        Serial.println(userId);
      }
      else
      {
        Serial.println("enrollSingle command failed.");
      }
      face = 0;
    }

    else if (face == 4)
    {
      Serial.printf("Attempting to delete faceId: %d for EMP_ID: %d\n", faceIdForDeletion, EMP_ID);
      if (faceIdForDeletion > 0)
      {
        faceScanner.reset(); // Reset before enrollment
        if (faceScanner.deleteUser(faceIdForDeletion, 0))
        {
          Serial.println("deleteUser from MASTER.csv command succeeded.");
          // Update MASTER.csv to remove the faceId
          updateEnrollmentRecord(1, 0); // 1 indicates face record, 0 clears it
        }
        else
        {
          Serial.println("deleteUser command failed.");
        }
      }
      else
      {
        Serial.println("Error: No valid faceId found for deletion");
      }
      face = 0;
      faceIdForDeletion = 0; // Reset after deletion attempt
    }

    else if (face == 5)
    {
      Serial.println("Testing deleteAllUsers command...");
      faceScanner.reset(); // Reset before enrollment
      if (faceScanner.deleteAllUsers(0))
      {
        Serial.println("deleteAllUsers command succeeded.");
      }
      else
      {
        Serial.println("deleteAllUsers command failed.");
      }
      face = 0;
    }

    else if (face == 6)
    {
      Serial.println("\n=== Starting Face Enrollment (ITG) ===");
      faceScanner.reset(); // Reset before enrollment
      delay(1000);         // Give device time to stabilize

      Serial.println("Testing enrollITG command...");
      uint16_t enrolledUserId;
      isAdmin = false;
      if (faceScanner.enrollITG(isAdmin, face_string.c_str(), FACE_DIRECTION_SINGLE, 1, 0, 10, &enrolledUserId))
      {
        Serial.print("enrollITG FACE succeeded. User ID: ");
        Serial.println(enrolledUserId);

        // Verify the enrollment was successful by trying to get user info
        char userName[33];
        bool isUserAdmin;
        if (faceScanner.getUserInfo(enrolledUserId, userName, &isUserAdmin))
        {
          Serial.println("Enrollment verified successfully");
          updateEnrollmentRecord(1, enrolledUserId); // Update MASTER.csv with face ID
          writetotft(31, "FACE ENROLLED");
          writetotft(32, userName);
        }
        else
        {
          Serial.println("Warning: Enrollment succeeded but verification failed");
        }
      }
      else
      {
        Serial.println("enrollITG command failed.");
        writetotft(31, "Enrollment Failed");
      }
      delay(2000);                           // Allow time to see the result
      memset(userName, 0, sizeof(userName)); // Clear userName
      face = 0;
      card_band();
    }

    else if (face == 7)
    {
      Serial.print("IN FINGER ENROLL");
      delay(2000);
      Serial.println("Testing enroll Finger command...");
      face = 0;
    }

    else if (face == 8)
    {
      Serial.println("Testing enrollITG command...");
      faceScanner.reset(); // Reset before enrollment
      delay(1000);         // Give device time to stabilize

      Serial.println("Testing enrollITG Palm command...");
      uint16_t enrolledUserId;
      isAdmin = false;
      if (faceScanner.enrollITG(isAdmin, face_string.c_str(), FACE_DIRECTION_SINGLE_HAND, 1, 0, 10, &enrolledUserId))
      {
        Serial.print("enrollITG PALM succeeded. User ID: ");
        Serial.println(enrolledUserId);

        // Verify the enrollment was successful by trying to get user info
        // char userName[33];
        bool isUserAdmin;
        if (faceScanner.getUserInfo(enrolledUserId, userName, &isUserAdmin))
        {
          Serial.println("Enrollment verified successfully");
          updateEnrollmentRecord(2, enrolledUserId); // Update MASTER.csv with palm ID
          writetotft(31, "PALM ENROLLED");
          writetotft(32, userName);
          delay(2000);                           // Allow time to see the result
          memset(userName, 0, sizeof(userName)); // Clear userName
        }
        else
        {
          Serial.println("enrollITG PALM command failed.");
        }
        face = 0; //
        card_band();
      }
    }
    else if (face == 9)
    {
      Serial.printf("Attempting to delete palmId: %d for EMP_ID: %d\n", faceIdForDeletion, EMP_ID);
      if (faceIdForDeletion > 0)
      {
        faceScanner.reset(); // Reset before enrollment
        if (faceScanner.deleteUser(faceIdForDeletion, 0))
        {
          //  if (faceScanner.deleteUser(faceIdForDeletion, FACE_DIRECTION_HAND)) {
          Serial.println("deleteUser PALM from MASTER.csv command succeeded.");
          updateEnrollmentRecord(2, 0); // 2 indicates palm record, 0 clears it
        }
        else
        {
          Serial.println("deleteUser PALM command failed.");
        }
      }
      else
      {
        Serial.println("Error: No valid palmId found for deletion");
      }
      face = 0;
      faceIdForDeletion = 0; // Reset after deletion attempt
    }

    else if (face == 11)
    {
      Serial.println("\n=== Starting Card Enrollment ===");
      faceScanner.reset(); // Reset before enrollment
      delay(1000);         // Give device time to stabilize

      Serial.println("Waiting for card scan...");

      // Wait for card to be scanned
      cardScanned = false; // Reset flag
      // Wait until the card is scanned or 5 second timeout 
      unsigned long startTime = xTaskGetTickCount();
      writetotft(31, "Show card to enroll");
      memset(CardCSN, 0, sizeof(CardCSN));

      while (!cardScanned)
      {
        if (xTaskGetTickCount() - startTime > pdMS_TO_TICKS(10000))
        {
          break;
        }
        vTaskDelay(pdMS_TO_TICKS(100));
      }
      if (!cardScanned || strlen(CardCSN) == 0)
      {
        Serial.println("Card scan failed");
        card_band();
        writetotft(31, "Card enroll failed");
        ERROR_BEEP(1.0f,2);
        delay(2000);
        card_band();
        face = 0; // Reset face to 0 after failure
        memset(CardCSN, 0, sizeof(CardCSN)); // Clear CardCSN
        continue;
      }
      START_BEEP(1.0f); // Indicate card scan success
      cardScanned = false; // Reset flag

      Serial.print("Card CSN: ");
      Serial.println(CardCSN);

      // Update enrollment record with card CSN
      updateEnrollmentRecord(4, 0, CardCSN);

      writetotft(31, "CARD ENROLLED");
      delay(2000); // NBP
      memset(userName, 0, sizeof(userName)); // Clear userName
      face = 0;
      card_band();
    }

    // Test getLogfile command
    else if (face == 99)
    { // orig 99
      uint32_t logSize;
      Serial.println("Testing getLogfile command...");
      if (faceScanner.getLogfile(2, userId, &logSize))
      { // get template for userid 1
        Serial.print("Log Size: ");
        Serial.println(logSize);
      }
      else
      {
        Serial.println("getLogfile command failed.");
      }
      face = 100;
      delay(100);
    } // end of 99
    else if (face == 100)
    {
      // Test uploadLogfile command
      uint8_t logData[1024] = {0};
      Serial.println("Testing uploadLogfile command...");
      if (faceScanner.uploadLogfile(0, 1024, logData))
      {
        Serial.println("uploadLogfile command succeeded.");
      }
      else
      {
        Serial.println("uploadLogfile command failed.");
      }
      face = 0;
      delay(1000);
    } // end of 100

    // Rest of your existing face verification code...
    vTaskDelay(xDelay);
  }
}
