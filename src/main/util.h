#ifndef UTIL_H
#define UTIL_H

#include <Arduino.h>
#include <Preferences.h>

/*
Utility functions for getting Preferences
*/

String getPrefString(String group, String key, String defaultValue);
void setPrefString(String group, String key, String value);

int getPrefInt(String group, String key, int defaultValue);
void setPrefInt(String group, String key, int value);

uint32_t getPrefULong(String group, String key, uint32_t defaultValue);
void setPrefULong(String group, String key, uint32_t value);

bool getPrefBool(String group, String key, bool defaultValue);
void setPrefBool(String group, String key, bool value);
static const bool LOG_ENABLED = true;
void Log(const String &message);

#endif // UTIL_H
