/*
TO REMOVE Serial.print from Lib.. It causes timeout in upload and download chunk

*/


#include "FaceScanner.h"
#include <FS.h>
#include <SPIFFS.h>

FaceScanner::FaceScanner() {
    responseBuffer = nullptr;
    streamBuffer = nullptr;
    useStreamBuffer = false; // This variable seems unused in the provided code, but kept for consistency
}

FaceScanner::~FaceScanner() {
    if (responseBuffer) {
        free(responseBuffer);
    }
    if (streamBuffer) {
        free(streamBuffer);
    }
}

bool FaceScanner::begin() {
    Serial1.begin(115200, SERIAL_8N1, RXD1, TXD1);
    delay(100);
    
    // Allocate both regular and streaming buffers
    responseBuffer = (uint8_t*)malloc(MAX_RESPONSE_SIZE);
    streamBuffer = (uint8_t*)malloc(STREAM_BUFFER_SIZE);
    
    if (!responseBuffer || !streamBuffer) {
        Serial.println("✗ Buffer allocation failed");
        return false;
    }
    
    Serial.println("✓ FaceScanner buffers allocated successfully");
    return true;
}


bool FaceScanner::reset() {
    Serial.println("Resetting face scanner module...");
    return sendCommand(MID_RESET, nullptr, 0) && 
           waitForResponse(MID_RESET, nullptr, nullptr, 5000);
}

bool FaceScanner::getStatus(uint8_t* status) {
    if (!status) return false;
    
    if (sendCommand(MID_GETSTATUS, nullptr, 0)) {
        uint8_t response[10]; // Small local buffer for status data
        uint16_t responseSize;
        if (waitForResponse(MID_GETSTATUS, response, &responseSize, 3000)) {
            if (responseSize >= 1) {
                *status = response[0];
                return true;
            }
        }
    }
    return false;
}

bool FaceScanner::snapImage(uint8_t imageNumber, uint8_t quality) {
    uint8_t data[2] = {imageNumber, quality};
    return sendCommand(MID_SNAPIMAGE, data, 2) &&
           waitForResponse(MID_SNAPIMAGE, nullptr, nullptr, 10000);
}

bool FaceScanner::getSavedImageSize(uint8_t imageNumber, uint32_t* imageSize) {
    if (!imageSize) return false;
    
    uint8_t data[1] = {imageNumber};
    if (!sendCommand(MID_GETSAVEDIMAGE, data, 1)) return false;
    
    uint8_t response[16]; // Local buffer for image size data
    uint16_t responseSize;
    
    if (waitForResponse(MID_GETSAVEDIMAGE, response, &responseSize, 1000)) {
        if (responseSize >= 4) {
            uint32_t bigEndian32 = ((uint32_t)response[0] << 24) |
                                   ((uint32_t)response[1] << 16) |
                                   ((uint32_t)response[2] << 8) |
                                   (uint32_t)response[3];
            *imageSize = bigEndian32;
            return true;
        }
    }
    return false;
}

bool FaceScanner::uploadImage(uint32_t offset, uint32_t size, uint8_t* imageData, uint16_t* actualSize) {
    if (!imageData || !actualSize || size == 0 || size > MAX_CHUNK_SIZE) {
        Serial.println("✗ uploadImage: Invalid parameters");
        return false;
    }

    
    // Clear any pending serial data
    clearSerialBuffer();
    
    uint8_t data[8];
    data[0] = (offset >> 24) & 0xFF;
    data[1] = (offset >> 16) & 0xFF;
    data[2] = (offset >> 8) & 0xFF;
    data[3] = offset & 0xFF;
    data[4] = (size >> 24) & 0xFF;
    data[5] = (size >> 16) & 0xFF;
    data[6] = (size >> 8) & 0xFF;
    data[7] = size & 0xFF;
    
    // Add delay if this is first chunk
    if (offset == 0) {
        Serial.println("→ Adding 500ms delay before first chunk upload");
        delay(500);
    }
    
    if (!sendCommand(MID_UPLOADIMAGE, data, 8)) {
        Serial.println("✗ uploadImage: sendCommand failed");
        return false;
    }

    // Ensure command is fully sent
    Serial1.flush();
    
    Serial.println("→ uploadImage: Waiting for stream response...");
    uint32_t timeout = 30000; // Increased timeout to 30s
    if (waitForStreamResponse(MID_UPLOADIMAGE, imageData, size, timeout)) {
        *actualSize = size;
        Serial.println("✓ uploadImage: Success");
        return true;
    }
    
    Serial.println("✗ uploadImage: waitForStreamResponse failed");
    
    // Additional debug - check if any bytes were received
    if (Serial1.available()) {
        Serial.print("→ Serial1 has ");
        Serial.print(Serial1.available());
        Serial.println(" bytes available after failure");
    }
    
    return false;
}

bool FaceScanner::enroll(uint8_t imageNumber, uint16_t faceId) {
    uint8_t data[3];
    data[0] = imageNumber;
    data[1] = (faceId >> 8) & 0xFF;
    data[2] = faceId & 0xFF;
    return sendCommand(MID_ENROLL, data, 3) &&
           waitForResponse(MID_ENROLL, nullptr, nullptr, 5000); 
}

bool FaceScanner::verify(uint8_t imageNumber, uint16_t faceId, uint8_t* score) {
    if (!score) return false;

    uint8_t data[3];
    data[0] = imageNumber;
    data[1] = (faceId >> 8) & 0xFF;
    data[2] = faceId & 0xFF;

    if (sendCommand(MID_VERIFY, data, 3)) {
        uint8_t responseData[64];
        uint16_t responseSize;
        if (waitForResponse(MID_VERIFY, responseData, &responseSize, 5000)) { 
            if (responseSize >= 1) { 
                *score = responseData[0];
                return true;
            }
        }
    }
    return false;
}

bool FaceScanner::deleteUser(uint16_t faceId, uint8_t format) {
    uint8_t data[3];
    data[0] = (faceId >> 8) & 0xFF;
    data[1] = faceId & 0xFF;
    data[2] = format;
    return sendCommand(MID_DELUSER, data, 3) &&
           waitForResponse(MID_DELUSER, nullptr, nullptr, 3000);
}

bool FaceScanner::deleteAllUsers(uint8_t format) {
    uint8_t data[1] = {format};
    return sendCommand(MID_DELALL, data, 1) &&
           waitForResponse(MID_DELALL, nullptr, nullptr, 5000);
}

bool FaceScanner::getUserInfo(uint16_t faceId, uint8_t* userInfoBuffer, uint16_t* actualSize) {
    if (!userInfoBuffer || !actualSize) return false;
    
    uint8_t data[2];
    data[0] = (faceId >> 8) & 0xFF;
    data[1] = faceId & 0xFF;

    if (sendCommand(MID_GETUSERINFO, data, 2)) {
        if (waitForResponse(MID_GETUSERINFO, userInfoBuffer, actualSize, 3000)) {
            return true;
        }
    }
    return false;
}

bool FaceScanner::enrollSingle(bool isAdmin, const char* userName, uint8_t faceDirection, uint8_t timeout, uint16_t* newFaceId) {
    if (!userName || !newFaceId) return false;

    uint8_t data[36];
    data[0] = isAdmin ? 1 : 0;
    memset(&data[1], 0, 32);
    strncpy((char*)&data[1], userName, 31);
    data[33] = faceDirection;
    data[34] = timeout;
    data[35] = 0; // padding

    if (sendCommand(MID_ENROLL_SINGLE, data, sizeof(data))) {
        uint8_t responseData[4];
        uint16_t responseSize;
        if (waitForResponse(MID_ENROLL_SINGLE, responseData, &responseSize, timeout * 1000)) {
            if (responseSize >= 2) {
                *newFaceId = responseData[0] | ((uint16_t)responseData[1] << 8);
                return true;
            }
        }
    }
    return false;
}

bool FaceScanner::getVersion(char* versionBuffer, uint8_t bufferSize) {
    if (!versionBuffer || bufferSize < 33) return false;

    if (sendCommand(MID_GET_VERSION, nullptr, 0)) {
        uint8_t responseData[32];
        uint16_t responseSize;
        if (waitForResponse(MID_GET_VERSION, responseData, &responseSize, 1000)) {
            if (responseSize == 32) {
                memcpy(versionBuffer, responseData, 32);
                versionBuffer[32] = '\0';
                return true;
            }
        }
    }
    return false;
}

bool FaceScanner::enrollITG(bool isAdmin, const char* userName, uint8_t faceDir, uint8_t enrollType, uint8_t enableDuplicate, uint8_t timeout, uint16_t* newFaceId) {
    return enrollItg(userName, isAdmin, faceDir, enrollType, enableDuplicate, timeout, newFaceId);
}

bool FaceScanner::getAllUserIds(uint8_t format, uint8_t* dataBuffer, uint16_t* receivedSize) {
    if (!dataBuffer || !receivedSize) return false;
    
    uint8_t commandData[1] = {format};
    if (sendCommand(MID_GET_ALL_USERID, commandData, 1)) {
        uint8_t responseBuffer[256];
        uint16_t responseSize;
        
        if (waitForResponse(MID_GET_ALL_USERID, responseBuffer, &responseSize, 3000)) {
            Serial.print("Received MID_GET_ALL_USERID response: ");
            Serial.print(responseSize);
            Serial.println(" bytes");
            
            // The module seems to always send extra data regardless of format
            // So we'll parse based on the actual user count
            if (responseSize < 1) return false;
            
            uint8_t userCount = responseBuffer[0];
            Serial.print("  > User count from module: ");
            Serial.println(userCount);
            
            // Calculate minimum valid size
            uint16_t minValidSize = 1; // At least user count byte
            
            if (format == 0) {
                // Long format: 2 bytes per user ID
                minValidSize += userCount * 2;
            } else {
                // Short format: packed bits (ceil(userCount/8) bytes
                minValidSize += (userCount + 7) / 8;
            }
            
            if (responseSize < minValidSize) {
                Serial.print("✗ Response too small. Need at least ");
                Serial.print(minValidSize);
                Serial.print(" bytes, got ");
                Serial.println(responseSize);
                return false;
            }
            
            // Copy valid portion of response
            uint16_t copySize = min(minValidSize, *receivedSize);
            memcpy(dataBuffer, responseBuffer, copySize);
            *receivedSize = copySize;
            
            // Debug print user IDs if in long format
            if (format == 0 && userCount > 0) {
                Serial.print("  > Registered IDs: ");
                for (uint8_t i = 0; i < userCount; i++) {
                    // Based on debug output, the module uses Big Endian format for user IDs
                    uint16_t id = (responseBuffer[1 + i*2] << 8) | responseBuffer[2 + i*2];
                    Serial.print(id);
                    if (i < userCount - 1) Serial.print(", ");
                }
                Serial.println();
            }
            
            return true;
        }
    }
    return false;
}

// New function to get registration data for log_type=2
// This implements the correct two-step process:
// 1. MID_GET_LOGFILE (0x60) to get the size
// 2. MID_UPLOAD_LOGFILE (0x61) to download the actual data
bool FaceScanner::getRegistrationData(uint8_t logType, uint16_t userId, uint8_t* buffer, uint16_t* size) {
    if (!buffer || !size || logType != 2) return false;

    // Step 1: Get the size of the registration data using MID_GET_LOGFILE
    uint32_t dataSize = 0;
    if (!getLogfileSize(logType, userId, &dataSize)) {
        Serial.println("✗ Failed to get registration data size");
        return false;
    }

    Serial.print("→ Registration data size: ");
    Serial.print(dataSize);
    Serial.println(" bytes");

    if (dataSize == 0) {
        Serial.println("✗ No registration data available for this user");
        *size = 0;
        return false;
    }

    if (dataSize > 4096) { // Increase buffer size limit to handle 2K+ bytes
        Serial.println("✗ Registration data too large for buffer");
        return false;
    }

    // Step 2: Download the actual registration data using MID_UPLOAD_LOGFILE
    uint16_t receivedSize = 0;
    if (uploadLogfileChunk(0, dataSize, buffer, &receivedSize)) {
        *size = receivedSize;
        Serial.print("✓ Retrieved registration data: ");
        Serial.print(*size);
        Serial.println(" bytes");

        // Step 3: Save the data to SPIFFS as /userId.bin
        char filename[32];
        snprintf(filename, sizeof(filename), "/%d.bin", userId);

        Serial.print("→ Saving registration data to SPIFFS: ");
        Serial.println(filename);

        // Remove existing file if it exists
        if (SPIFFS.exists(filename)) {
            SPIFFS.remove(filename);
            Serial.println("  > Removed existing file");
        }

        // Create and write to new file
        File file = SPIFFS.open(filename, FILE_WRITE);
        if (file) {
            size_t bytesWritten = file.write(buffer, receivedSize);
            file.close();

            if (bytesWritten == receivedSize) {
                Serial.print("✓ Successfully saved ");
                Serial.print(bytesWritten);
                Serial.print(" bytes to ");
                Serial.println(filename);
            } else {
                Serial.print("✗ File write error. Expected ");
                Serial.print(receivedSize);
                Serial.print(" bytes, wrote ");
                Serial.println(bytesWritten);
            }
        } else {
            Serial.print("✗ Failed to create file: ");
            Serial.println(filename);
        }

        return true;
    } else {
        Serial.println("✗ Failed to download registration data");
        return false;
    }
}

bool FaceScanner::getLogfileSize(uint8_t logType, uint16_t userId, uint32_t* logSize) {
    if (!logSize) return false;

    uint8_t data[3];
    uint16_t dataSize;

    data[0] = logType;
    if (logType == 2) {
        data[1] = (userId >> 8) & 0xFF;  // MSB
        data[2] = userId & 0xFF;         // LSB
        dataSize = 3;
    } else {
        dataSize = 1;
    }

    if (!sendCommand(MID_GET_LOGFILE, data, dataSize)) {
        return false;
    }

    uint8_t response[16];
    uint16_t responseSize;
    uint8_t responseMsgId;

    // Use waitForResponseWithMsgId to handle both MID_GET_LOGFILE and MID_REPLY responses
    if (waitForResponseWithMsgId(&responseMsgId, response, &responseSize, 1000)) {
        if (responseMsgId == MID_GET_LOGFILE || responseMsgId == MID_REPLY) {

            if (responseSize >= 6) {
                // Based on raw response: 0x60 0x00 0x00 0x00 0x08 0x48 0x00
                // The size appears to be in bytes 4-5: 0x08 0x48 = 0x0848 = 2120 bytes
                uint32_t size16bit = ((uint32_t)response[4] << 8) | (uint32_t)response[5];

                if (size16bit > 0 && size16bit < 10000) {
                    *logSize = size16bit;
                    return true;
                } else {
                    Serial.print("✗ Size seems unreasonable: ");
                    Serial.println(size16bit);
                    return false;
                }
            } else {
                Serial.print("✗ Response too small for size data. Need at least 6 bytes, got ");
                Serial.println(responseSize);
            }
        }
    }
    return false;
}


bool FaceScanner::uploadLogfileChunk(uint32_t offset, uint32_t chunkSize, uint8_t* dataBuffer, uint16_t* receivedSize) {
    if (!dataBuffer || !receivedSize || chunkSize == 0 || chunkSize > 4096) {
        Serial.println("✗ Invalid parameters for uploadLogfileChunk");
        return false;
    }

    uint8_t data[8];
    // Keep MSB format as per documentation
    data[0] = (offset >> 24) & 0xFF;    // MSB
    data[1] = (offset >> 16) & 0xFF;
    data[2] = (offset >> 8) & 0xFF;
    data[3] = offset & 0xFF;            // LSB
    data[4] = (chunkSize >> 24) & 0xFF; // MSB
    data[5] = (chunkSize >> 16) & 0xFF;
    data[6] = (chunkSize >> 8) & 0xFF;
    data[7] = chunkSize & 0xFF;         // LSB

    Serial.print("→ Attempting to download chunk at offset ");
    Serial.print(offset);
    Serial.print(", size ");
    Serial.println(chunkSize);
    
    // Try multiple times with increasing timeout
    for (int attempt = 1; attempt <= 3; attempt++) {
        Serial.print("→ Attempt ");
        Serial.print(attempt);
        Serial.println("/3");
        
        if (sendCommand(MID_UPLOAD_LOGFILE, data, 8)) {
            // Increase timeout for larger chunks, with a cap at 20 seconds
            uint32_t timeout = std::min(static_cast<uint32_t>(20000), 
                                      static_cast<uint32_t>(3000) + (chunkSize / 50));
            Serial.print("→ Timeout set to ");
            Serial.print(timeout);
            Serial.println("ms");
            uint8_t responseMsgId;
            uint16_t actualReceivedSize = chunkSize; // Set buffer size limit
            if (waitForResponseWithMsgId(&responseMsgId, dataBuffer, &actualReceivedSize, timeout)) {
                Serial.print("✓ Downloaded ");
                Serial.print(actualReceivedSize);
                Serial.print(" bytes with MsgID=0x");
                Serial.println(responseMsgId, HEX);

                *receivedSize = actualReceivedSize;
                // Accept the response regardless of MsgID since module is responding with 0x2
                return true;
            } else {
                Serial.println("✗ Timeout or failed response, retrying...");
            }
        } else {
            Serial.println("✗ Failed to send MID_UPLOAD_LOGFILE command");
        }
        
        delay(500); // Short delay before retry
    }
    
    Serial.println("✗ Max retries exceeded for uploadLogfileChunk");
    return false;
}



bool FaceScanner::transferFilePacket(uint8_t storeType, uint32_t totalSize, uint32_t offset, uint16_t packetSize, const uint8_t* packetData) {
    if (!packetData && packetSize > 0) return false;
    
    uint16_t headerSize = 11; // store_type(1) + feat_size(4) + offset(4) + psize(2)
    uint16_t totalPayloadSize = headerSize + packetSize;
    uint8_t* payload = (uint8_t*)malloc(totalPayloadSize);
    
    if (!payload) return false;
    
    // Build packet according to s_msg_trans_file_data structure
    payload[0] = storeType;
    
    // feat_size (MSB)
    payload[1] = (totalSize >> 24) & 0xFF;
    payload[2] = (totalSize >> 16) & 0xFF;
    payload[3] = (totalSize >> 8) & 0xFF;
    payload[4] = totalSize & 0xFF;
    
    // offset (MSB)
    payload[5] = (offset >> 24) & 0xFF;
    payload[6] = (offset >> 16) & 0xFF;
    payload[7] = (offset >> 8) & 0xFF;
    payload[8] = offset & 0xFF;
    
    // packet size (MSB)
    payload[9] = (packetSize >> 8) & 0xFF;
    payload[10] = packetSize & 0xFF;
    
    // Copy packet data if any
    if (packetSize > 0 && packetData) {
        memcpy(&payload[headerSize], packetData, packetSize);
    }
    
    bool result = sendCommand(MID_TRANS_FILE_PACKET, payload, totalPayloadSize) &&
                 waitForResponse(MID_TRANS_FILE_PACKET, nullptr, nullptr, 3000);
    
    free(payload);
    return result;
}



bool FaceScanner::enrollFromImage(const char* userName, bool isAdmin, uint8_t imgType, uint8_t timeout, uint16_t* newFaceId) {
    if (!userName || !newFaceId) return false;

    uint8_t data[35];  // admin(1) + user_name(32) + img_type(1) + timeout(1)
    data[0] = isAdmin ? 1 : 0;
    
    // Clear and copy user name (32 bytes)
    memset(&data[1], 0, 32);
    strncpy((char*)&data[1], userName, 31);  // Leave room for null terminator
    
    data[33] = imgType;
    data[34] = timeout;
    
    if (sendCommand(MID_ENROLL_FROM_IMAGE, data, sizeof(data))) {
        uint8_t response[4];
        uint16_t responseSize;
        if (waitForResponse(MID_ENROLL_FROM_IMAGE, response, &responseSize, 5000)) {
            if (responseSize >= 2) {
                // Response ID is in LSB format
                *newFaceId = response[0] | ((uint16_t)response[1] << 8);
                return true;
            }
        }
    }
    return false;
}

void FaceScanner::clearSerialBuffer() {
    while (Serial1.available()) Serial1.read();
}

// === START OF CORRECTED METHOD (unchanged logic) ============================
bool FaceScanner::sendCommand(uint8_t msgId, uint8_t* data,
                              uint16_t dataSize) {
    uint16_t bufferSize = dataSize + 16;          // header + checksum margin
    uint8_t* packet     = static_cast<uint8_t*>(malloc(bufferSize));
    if (!packet) {
        Serial.println("✗ Failed to allocate memory for command packet");
        return false;
    }

    const uint16_t hdr = 5;                       // SYNC SYNC MID LENH LENL
    packet[0] = SYNC_WORD[0];
    packet[1] = SYNC_WORD[1];
    packet[2] = msgId;
    packet[3] = (dataSize >> 8) & 0xFF;
    packet[4] =  dataSize       & 0xFF;

    if (data && dataSize) memcpy(&packet[hdr], data, dataSize);

    uint8_t checksum = 0;
    for (uint16_t i = 2; i < hdr + dataSize; ++i) checksum ^= packet[i];
    packet[hdr + dataSize] = checksum;

    Serial.print("→ Sending command 0x");
    Serial.print(msgId, HEX);
    Serial.print(" (");
    Serial.print(dataSize);
    Serial.println(" bytes)");

    clearSerialBuffer();
    Serial1.write(packet, hdr + dataSize + 1);
    Serial1.flush();
    free(packet);
    return true;
}


/******************************************************************
 * waitForResponseWithMsgId()
 * Generic packet sniffer that gives you the MsgID of every valid
 * packet together with its raw payload.  Used by
 *   getRegistrationData() and getLogfileSize().
 ******************************************************************/
bool FaceScanner::waitForResponseWithMsgId(uint8_t* rcvdMsgId,
                                           uint8_t* payload,
                                           uint16_t* payloadSize,
                                           uint32_t timeoutMs)
{
    if (!rcvdMsgId) return false;

    uint32_t start = millis();
    uint8_t  state = 0;
    uint8_t  msgId = 0;
    uint16_t len   = 0;
    uint16_t idx   = 0;
    uint8_t  xorChk= 0;

    while (millis() - start < timeoutMs) {
        if (!Serial1.available()) { yield(); continue; }

        uint8_t b = Serial1.read();

        switch (state) {
        case 0:  if (b == SYNC_WORD[0]) state = 1; break;
        case 1:  state = (b == SYNC_WORD[1]) ? 2 : 0; break;
        case 2:  msgId = b; xorChk = b; state = 3; break;
        case 3:  len  = b << 8; xorChk ^= b; state = 4; break;
        case 4:  len |= b;      xorChk ^= b;
                 if (len > MAX_RESPONSE_SIZE) { state = 0; break; }
                 idx = 0;
                 state = (len == 0) ? 6 : 5;
                 break;
        case 5:  // data
                 if (idx < MAX_RESPONSE_SIZE) {
                     responseBuffer[idx] = b;
                 }
                 xorChk ^= b;
                 if (++idx >= len) state = 6;
                 break;
        case 6:  // checksum byte
                 if (b == xorChk) {
                     *rcvdMsgId = msgId;
                     if (payload && payloadSize) {
                         uint16_t copy = min(len, *payloadSize);
                         memcpy(payload, responseBuffer, copy);
                         *payloadSize = copy;
                     }
                     return true;
                 }
                 state = 0;                             // bad checksum
                 break;
        }
    }
    Serial.println("✗ waitForResponseWithMsgId timeout");
    return false;
}


bool FaceScanner::waitForResponse(uint8_t expectedMsgId, uint8_t* response,
                                  uint16_t* responseSize, uint32_t timeoutMs) {
    if (!responseBuffer) return false;
    uint32_t startTime = millis();
    uint8_t  state = 0;
    uint8_t  rcvdMsgId = 0;
    uint16_t rcvdDataSize = 0;
    uint16_t bytesReadInPayload = 0;
    uint16_t actualPayloadSizeForCaller = 0;

    while (millis() - startTime < timeoutMs) {
        if (Serial1.available()) {
            uint8_t byte = Serial1.read();
            switch (state) {
            case 0: if (byte == SYNC_WORD[0]) state = 1; break;
            case 1: state = (byte == SYNC_WORD[1]) ? 2 : 0; break;
            case 2:
                if (bytesReadInPayload == 0) { rcvdMsgId = byte; bytesReadInPayload++; }
                else if (bytesReadInPayload == 1) { rcvdDataSize = byte << 8; bytesReadInPayload++; }
                else if (bytesReadInPayload == 2) {
                    rcvdDataSize |= byte; bytesReadInPayload = 0;
                   /*  Serial.print("← Response: MsgID=0x");
                    Serial.print(rcvdMsgId, HEX);
                    Serial.print(", PacketDataSize=");
                    Serial.println(rcvdDataSize); */
                    if (rcvdDataSize > MAX_RESPONSE_SIZE) return false;
                    state = (rcvdMsgId == MID_REPLY) ? 3 : 0;
                } break;
            case 3:
                if (bytesReadInPayload == 0) {
                    uint8_t replyToMsgId = byte;
                    if (replyToMsgId == expectedMsgId) { bytesReadInPayload++; }
                    else { state = 0; }
                } else if (bytesReadInPayload == 1) {
                    uint8_t result = byte; bytesReadInPayload++;
                    actualPayloadSizeForCaller = rcvdDataSize - 2;
                    if (result != MR_SUCCESS) return false;
                    if (actualPayloadSizeForCaller == 0) {
                        if (responseSize) *responseSize = 0;
                        return true;
                    }
                } else {
                    uint16_t bufferIndex = bytesReadInPayload - 2;
                    if (bufferIndex < actualPayloadSizeForCaller)
                        responseBuffer[bufferIndex] = byte;
                    bytesReadInPayload++;
                    if (bytesReadInPayload >= rcvdDataSize) {
                        if (response && responseSize && actualPayloadSizeForCaller > 0) {
                            memcpy(response, responseBuffer, actualPayloadSizeForCaller);
                            *responseSize = actualPayloadSizeForCaller;
                        } else if (responseSize) { *responseSize = 0; }
                        return true;
                    }
                } break;
            } // switch
        }
        yield();
    }
    Serial.println("✗ Response timeout");
    return false;
}

bool FaceScanner::waitForStreamResponse(uint8_t expectedMsgId,
                                        uint8_t* response,
                                        uint16_t expectedSize,
                                        uint32_t timeoutMs) {
    if (!response) return false;
    
    Serial.println("→ waitForStreamResponse: Starting...");
    uint32_t startTime = millis();
    uint8_t  state = 0;
    uint8_t  rcvdMsgId = 0;
    uint16_t rcvdPacketDataSize = 0;
    uint16_t bytesReadForHeader = 0;
    uint16_t totalDataBytesReceived = 0;
    uint32_t lastByteTime = millis();

    while (millis() - startTime < timeoutMs) {
        if (Serial1.available()) {
            uint8_t byte = Serial1.read();
            lastByteTime = millis();
            
         /*    Serial.print("← Received byte: 0x");
            Serial.println(byte, HEX);
             */
			 
            switch (state) {
                case 0: 
                    if (byte == SYNC_WORD[0]) {
                        state = 1;
                 //       Serial.println("  → Got SYNC_WORD[0]");
                    }
                    break;
                    
                case 1:
                    state = (byte == SYNC_WORD[1]) ? 2 : 0;
                    if (state == 2) {
                        bytesReadForHeader = 0;
                 //       Serial.println("  → Got SYNC_WORD[1]");
                    }
                    break;
                    
                case 2:
                    if (bytesReadForHeader == 0) {
                        rcvdMsgId = byte;
                     //   Serial.print("  → MsgID: 0x");
                        Serial.println(rcvdMsgId, HEX);
                    } 
                    else if (bytesReadForHeader == 1) {
                        rcvdPacketDataSize = byte << 8;
                    //    Serial.print("  → Length MSB: 0x");
                        Serial.println(byte, HEX);
                    }
                    else if (bytesReadForHeader == 2) {
                        rcvdPacketDataSize |= byte;
                        bytesReadForHeader = 0;
                        Serial.print("  → Complete Length: ");
                        Serial.println(rcvdPacketDataSize);
                        
                        // Accept both MID_REPLY (0x00) and 0x02 responses
                        if (rcvdMsgId == MID_REPLY || rcvdMsgId == 0x02) {
                            state = (rcvdPacketDataSize > 0) ? 4 : 0;
                        } else {
                            state = 0;
                        }
                    }
                    bytesReadForHeader++;
                    break;
                    
                case 4:
                    if (totalDataBytesReceived < expectedSize) {
                        response[totalDataBytesReceived++] = byte;
                        /* Serial.print("  → Storing data byte ");
                        Serial.print(totalDataBytesReceived);
                        Serial.print("/");
                        Serial.print(expectedSize);
                        Serial.print(": 0x");
                        Serial.println(byte, HEX); */
                        
                        if (totalDataBytesReceived >= expectedSize) {
                            Serial.println("✓ waitForStreamResponse: Complete");
                            return true;
                        }
                    }
                    break;
            }
        } else {
            // If no data received for 500ms, consider it a timeout
            if (millis() - lastByteTime > 500) {
                Serial.println("✗ waitForStreamResponse: Timeout between bytes");
                return false;
            }
            yield();
        }
    }
    
    Serial.println("✗ waitForStreamResponse: Overall timeout");
    return false;
}


/******************************************************************
 * transferUserBinaryFile()
 * Uses MID_TRANS_FILE_PACKET (0x90) command to transfer userId.bin
 * from SPIFFS to module memory in chunks, then uses same command (0x90)
 * with size=0 to move data from memory to permanent storage
 ******************************************************************/
bool FaceScanner::transferUserBinaryFile(uint16_t userId) {
    Serial.print("→ Starting transfer of binary file for userId: ");
    Serial.println(userId);

    // Step 1: Read the binary file from SPIFFS
    char filename[32];
    snprintf(filename, sizeof(filename), "/%d.bin", userId);

    Serial.print("→ Reading file from SPIFFS: ");
    Serial.println(filename);

    if (!SPIFFS.exists(filename)) {
        Serial.print("✗ File does not exist: ");
        Serial.println(filename);
        return false;
    }

    File file = SPIFFS.open(filename, FILE_READ);
    if (!file) {
        Serial.print("✗ Failed to open file: ");
        Serial.println(filename);
        return false;
    }

    size_t fileSize = file.size();
    Serial.print("→ File size: ");
    Serial.print(fileSize);
    Serial.println(" bytes");

    if (fileSize == 0 || fileSize > 4096) {
        Serial.println("✗ Invalid file size");
        file.close();
        return false;
    }

    // Allocate buffer for file data
    uint8_t* fileData = (uint8_t*)malloc(fileSize);
    if (!fileData) {
        Serial.println("✗ Failed to allocate memory for file data");
        file.close();
        return false;
    }

    // Read file content
    size_t bytesRead = file.readBytes((char*)fileData, fileSize);
    file.close();

    if (bytesRead != fileSize) {
        Serial.print("✗ Failed to read complete file. Expected ");
        Serial.print(fileSize);
        Serial.print(" bytes, read ");
        Serial.println(bytesRead);
        free(fileData);
        return false;
    }

    Serial.print("✓ Successfully read ");
    Serial.print(bytesRead);
    Serial.println(" bytes from file");

    // Step 2: Transfer file using MID_TRANS_FILE_PACKET (0x90) in chunks
    Serial.println("→ Transferring file to module using 0x90 command...");

    const uint16_t chunkSize = 256; // Transfer in 256-byte chunks
    uint32_t offset = 0;
    uint8_t storeType = 2; // Assuming store type 2 for face registration data

    bool transferSuccess = true;

    while (offset < fileSize && transferSuccess) {
        uint16_t currentChunkSize = min((uint32_t)chunkSize, fileSize - offset);

        Serial.print("→ Transferring chunk: offset=");
        Serial.print(offset);
        Serial.print(", size=");
        Serial.println(currentChunkSize);

        if (!transferFilePacket(storeType, fileSize, offset, currentChunkSize, &fileData[offset])) {
            Serial.print("✗ Failed to transfer chunk at offset ");
            Serial.println(offset);
            transferSuccess = false;
            break;
        }

        offset += currentChunkSize;
        Serial.print("✓ Transferred ");
        Serial.print(offset);
        Serial.print("/");
        Serial.print(fileSize);
        Serial.println(" bytes");

        delay(100); // Small delay between chunks
    }

    free(fileData);

    if (!transferSuccess) {
        Serial.println("✗ File transfer failed");
        return false;
    }

    Serial.println("✓ File transfer completed successfully");

    // Step 3: Send MID_TRANS_FILE_PACKET (0x90) command with size=0 to move to permanent storage
    Serial.println("→ Moving data from memory to permanent storage using 0x90...");

    // Use transferFilePacket with size=0 to finalize the transfer
    if (transferFilePacket(storeType, 0, 0, 0, nullptr)) {
        Serial.println("✓ Successfully moved data to permanent storage");
        Serial.print("✓ User enrollment completed for userId: ");
        Serial.println(userId);
        return true;
    } else {
        Serial.println("✗ Failed to move data to permanent storage");
        return false;
    }
}
