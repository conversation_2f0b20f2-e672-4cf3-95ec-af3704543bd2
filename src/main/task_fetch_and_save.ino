#include <main.h>
#include <WiFi.h>
/*
const char* serverIP = "************";
const int serverPort = 8000;
const char* filename = "/MASTER.csv"; */

String timeToHTTPDate(time_t time)
{
  char buf[30];
  struct tm *tm = gmtime(&time);
  strftime(buf, sizeof(buf), "%a, %d %b %Y %H:%M:%S GMT", tm);
  return String(buf);
}

void TaskFetchAndSaveCSV(void *pvParameters)
{
  const char *serverIP = "************";
  const int serverPort = 8000;
  const char *filename = "MASTER.csv";
  // Serial.print("NBP.. fetch task");

  for (;;)
  {

    WiFiClient client;
    if (client.connect(serverIP, serverPort))
    {
      client.print("HEAD ");
      client.print(filename);
      client.println(" HTTP/1.1");
      client.print("Host: ");
      client.println(serverIP);
      client.println("Connection: close");
      client.println();
	  

      String serverLastModified = "";
      while (client.connected())
      {
        String line = client.readStringUntil('\n');
        if (line.startsWith("Last-Modified: "))
        {
          serverLastModified = line.substring(15);
          serverLastModified.trim();
          break;
        }
      }
      client.stop();

      if (serverLastModified.length() > 0)
      {
        File file = SPIFFS.open(filename, "r");
        String fileLastModified = "";
        if (file)
        {
          time_t lastWrite = file.getLastWrite();
          fileLastModified = timeToHTTPDate(lastWrite);
          file.close();
        }

        if (!file || serverLastModified != fileLastModified)
        {
          // File is new or modified, download it
          if (client.connect(serverIP, serverPort))
          {
            client.print("GET ");
            client.print(filename);
            client.println(" HTTP/1.1");
            client.print("Host: ");
            client.println(serverIP);
            client.println("Connection: close");
            client.println();

            bool startSaving = false;
            File newFile = SPIFFS.open(filename, "w");
            while (client.available())
            {
              String line = client.readStringUntil('\n');
              if (startSaving)
              {
                newFile.println(line);
                Serial.println(line); // Print each row to serial console
              }
              else if (line == "\r")
              {
                startSaving = true;
              }
            }
            newFile.close();
            Serial.println("File updated successfully");
          }
        }
        else
        {
          Serial.println("SAME FILE");
        }
      }
    }
    else
    {
      Serial.println("NBP..Connection to server failed");
    }
    client.stop();
    vTaskDelay(60000 / portTICK_PERIOD_MS); // Wait for 1 minute before next check
  }
}
