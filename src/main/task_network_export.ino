#include "RealtimePunchExporter.cpp"
#include <ArduinoJson.h>
#include <HTTPClient.h>
#include <main.h>

// Task to handle network export of attendance records
void TaskNetworkExport(void *pvParameters)
{

  String devid = "";
  for (;;)
  {
    if (devid.isEmpty())
    {
      devid = getPrefString("attlog", "device_id", "");
      if (devid.isEmpty())
      {
        Serial.println("No device ID found, cannot export");
        vTaskDelay(10000 / portTICK_PERIOD_MS);
        continue;
      }
    }
    // Only if Pending > 0.... NBP
    // if (Pending > 0) {

    // 1. Get current Tr_Id from preferences
    Preferences prefs;
    prefs.begin("attlog", false);
    uint32_t currentTrId = prefs.getULong("Tr_Id", 1);
    uint32_t exportedTrId = prefs.getULong("ExportedTrId", 0);
    prefs.end();

    // 2. Open attlogs.csv
    File file = SPIFFS.open("/attlogs.csv", "r");
    if (!file || file.size() == 0)
    {
      Serial.println("No attlogs.csv file or empty file");
      vTaskDelay(10000 / portTICK_PERIOD_MS);
      continue;
    }

    // Skip header if exists
    if (file.available())
    {
      String header = file.readStringUntil('\n');
      if (!header.startsWith("Employee ID"))
      {
        // Not a header, rewind
        file.seek(0);
      }
    }

    // 3. Process records
    bool recordsProcessed = false;
    while (file.available())
    {
      String line = file.readStringUntil('\n');
      line.trim();

      // Parse CSV line
      int firstComma = line.indexOf(',');
      int secondComma = line.indexOf(',', firstComma + 1);
      if (firstComma == -1 || secondComma == -1)
        continue;

      String empId = line.substring(0, firstComma);
      String trIdStr = line.substring(firstComma + 1, secondComma);
      uint32_t trId = strtoul(trIdStr.c_str(), NULL, 10);

      // Serial.printf("Current Tr_Id: %lu, Exported TrID: %lu, Tr_Id: %lu \n", currentTrId, exportedTrId, trId);

      prefs.begin("wifi-config", false);
      String apiEndpoint = prefs.getString("api_endpoint", "");
      prefs.end();

      if (apiEndpoint.isEmpty())
      {
        Serial.println("No API endpoint configured...");
        break;
      }

      if (trId < exportedTrId)
        continue; // Skip already exported records
      if (trId > currentTrId)
      { // All records are exported
        Serial.println("All records exported, exiting...");
        file.close();
        break;
      }

      // Parse remaining fields
      String rest = line.substring(secondComma + 1);
      int nextComma = rest.indexOf(',');
      String dateStr = rest.substring(0, nextComma);
      rest = rest.substring(nextComma + 1);
      nextComma = rest.indexOf(',');
      String timeStr = rest.substring(0, nextComma);
      rest = rest.substring(nextComma + 1);
      nextComma = rest.indexOf(',');
      String status = rest.substring(0, nextComma);
      String deviceType = rest.substring(nextComma + 1);

      /*
            Following is how the code RealTimePunchExporter class is used to export punch data
            // Create exporter instance
            RealtimePunchExporter exporter("http://example.com/api/punch");

            // Use the new simplified method with string parameters
            bool success = exporter.exportPunch(
                "EMP12345",          // Employee code
                "20-05-25 09:30",    // Date-time (dd-mm-yy HH:mm)
                "I",                 // In/Out status ("I" or "O")
                "face",              // Verification method
                true                 // Accepted (optional, defaults to true)
            );
            */
      RealtimePunchExporter exporter(apiEndpoint, devid);
      bool success = false;
      for (size_t i = 0; i < 3; i++)
      {
        Serial.println("Attempting to export punch data...");
        Serial.println("Employee ID: " + empId);
        Serial.println("Date: " + dateStr);
        Serial.println("Time: " + timeStr);
        Serial.println("Status: " + status);
        Serial.println("Device Type: " + deviceType);
        // Use the new simplified method with string parameters
        success = exporter.exportPunch(
            empId,                        // Employee code
            dateStr + " " + timeStr,      // Date-time (dd-mm-yy HH:mm)
            status == "I" ? "In" : "Out", // In/Out status ("In" or "Out")
            deviceType,                   // Verification method
            true                          // Accepted (optional, defaults to true)
        );
        if (success)
        {
          Serial.println("Punch data exported successfully");
          // Update Tr_Id in preferences
          prefs.begin("attlog", false);
          prefs.putULong("ExportedTrId", trId + 1);
          prefs.end();
          // Pending = Pending - 1;
          recordsProcessed = true;
          break; // Exit for loop on success
        }
        else
        {
          Serial.println("Failed to export punch data... retrying...");
          vTaskDelay(1000 * (i + 1) / portTICK_PERIOD_MS);
          if (i == 2)
          {
            Serial.println("Failed to export punch data after 3 attempts");
            break; // Exit for loop after 3 attempts
          }
        }
      }
      if (!success)
      {
        Serial.println("Exiting TaskNetworkExport due to failure, we try on next call");
        break; // Exit while loop on failure
      }
      recordsProcessed = true;
    }
    // } //end of if Pending > 0

    file.close();

    if (!recordsProcessed)
    {
      // No records processed, wait longer before next attempt
      vTaskDelay(3 * 1000 / portTICK_PERIOD_MS);
    }
    else
    {
      // Records were processed, check again soon
      vTaskDelay(2 * 1000 / portTICK_PERIOD_MS);
    }
  }
}
