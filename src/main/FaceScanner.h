#ifndef FACESCANNER_H
#define FACESCANNER_H

#include <Arduino.h>


// Pin definitions for UART communication
#define RXD1 42
#define TXD1 41

// Protocol constants
#define SYNC_WORD_0 0xEF
#define SYNC_WORD_1 0xAA
static const uint8_t SYNC_WORD[2] = {SYNC_WORD_0, SYNC_WORD_1};

// Message IDs
#define MID_RESET 0x10
#define MID_GETSTATUS 0x11
#define MID_VERIFY 0x12
#define MID_ENROLL 0x13
#define MID_ENROLL_SINGLE 0x1D
#define MID_SNAPIMAGE 0x16
#define MID_GETSAVEDIMAGE 0x17
#define MID_UPLOADIMAGE 0x18
#define MID_DELUSER 0x20
#define MID_DELALL 0x21
#define MID_GETUSERINFO 0x22
#define MID_GET_ALL_USERID 0x24
#define MID_ENROLL_ITG 0x26
#define MID_GET_VERSION 0x30
#define MID_GET_LOGFILE 0x60        // New command
#define MID_UPLOAD_LOGFILE 0x61     // New command
#define MID_TRANS_FILE_PACKET 0x90  // New command
#define MID_ENROLL_FROM_IMAGE 0x91  // New command
#define MID_REPLY 0x00

// Result codes
#define MR_SUCCESS 0x00

// Buffer sizes - increased for large data transfers
#define MAX_RESPONSE_SIZE 8192
#define STREAM_BUFFER_SIZE 8192
#define MAX_CHUNK_SIZE 4096

// Face direction constants
#define FACE_DIRECTION_SINGLE 0xFC
#define FACE_DIRECTION_HAND 0xFD
#define FACE_DIRECTION_SINGLE_HAND 0xFE

class FaceScanner {
private:
    uint8_t* responseBuffer;
    uint8_t* streamBuffer;
    bool useStreamBuffer; 
    
    // Private methods
    bool sendCommand(uint8_t msgId, uint8_t* data, uint16_t dataSize);
    bool waitForResponse(uint8_t expectedMsgId, uint8_t* response, uint16_t* responseSize, uint32_t timeoutMs);
    bool waitForStreamResponse(uint8_t expectedMsgId, uint8_t* response, uint16_t expectedSize, uint32_t timeoutMs);
    bool waitForResponseWithMsgId(uint8_t* rcvdMsgId, uint8_t* payload, uint16_t* payloadSize, uint32_t timeoutMs);
    void clearSerialBuffer();
    
public:
    FaceScanner();
    ~FaceScanner();
    
    bool begin();
    bool reset();
    bool getStatus(uint8_t* status);
    bool snapImage(uint8_t imageNumber, uint8_t quality);
    bool getSavedImageSize(uint8_t imageNumber, uint32_t* imageSize);
    bool uploadImage(uint32_t offset, uint32_t size, uint8_t* imageData, uint16_t* actualSize);
    bool verify(uint8_t imageNumber, uint16_t faceId, uint8_t* score);
    bool enroll(uint8_t imageNumber, uint16_t faceId);
    bool enrollSingle(const char* userName, bool isAdmin, uint8_t timeout, uint16_t* newFaceId);
    bool deleteUser(uint16_t faceId, uint8_t format = 0);
    bool deleteAllUsers(uint8_t format = 0);
    bool getUserInfo(uint16_t faceId, char* userName, bool* isAdmin);
    bool getVersion(char* versionBuffer, uint8_t bufferSize);
    bool enrollItg(const char* userName, bool isAdmin, uint8_t faceDir, uint8_t enrollType, uint8_t enableDuplicate, uint8_t timeout, uint16_t* newFaceId);
    bool getAllUserIds(uint8_t format, uint8_t* dataBuffer, uint16_t* receivedSize);
    bool setThresholdLevel(uint8_t verifyThreshold, uint8_t livenessThreshold);
    bool verify(bool enableLiveness, uint8_t timeout, uint8_t format, uint16_t* userId, char* userName, bool* isAdmin);
    bool enrollSingle(bool isAdmin, const char* userName, uint8_t faceDirection, uint8_t timeout, uint16_t* newFaceId);
    bool enrollITG(bool isAdmin, const char* userName, uint8_t faceDir, uint8_t enrollType, uint8_t enableDuplicate, uint8_t timeout, uint16_t* newFaceId);

    // NEW features for log_type = 2 ------------------------------------------
    bool getRegistrationData(uint8_t logType, uint16_t userId, uint8_t* buffer, uint16_t* size);
    bool getLogfileSize(uint8_t logType, uint16_t userId, uint32_t* logSize);
    bool uploadLogfileChunk(uint32_t offset, uint32_t chunkSize, uint8_t* dataBuffer, uint16_t* receivedSize);
    bool transferUserBinaryFile(uint16_t userId);
    bool transferFilePacket(uint8_t storeType, uint32_t totalSize, uint32_t offset, uint16_t packetSize, const uint8_t* packetData);
    bool enrollFromImage(const char* userName, bool isAdmin, uint8_t imgType, uint8_t timeout, uint16_t* newFaceId);

};

// Test function declarations
void runFullFaceScannerTest();
void runBackupRestoreTest();

// Global declarations
extern uint8_t registrationDataBuffer[1024];
extern FaceScanner faceScanner;

#endif
