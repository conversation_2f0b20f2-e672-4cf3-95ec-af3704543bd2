// webserver_core.ino
#include "main.h"

// Define WebServer object if it's not defined in the main sketch
// WebServer server(80); // Uncomment if 'server' definition belongs here

// --- Web Server Task ---
void webServerTask(void *parameter)
{
    Serial.println("Web Server Task Started");
    connectWiFi();

    setupServerRoutes(); // Setup all routes
    server.begin();
    Serial.println("HTTP server started");

    while (1)
    {
        server.handleClient();
        vTaskDelay(pdMS_TO_TICKS(10)); // Small delay for cooperative multitasking
    }
}

// --- Timeout Monitoring Task ---
void timeoutTask(void *parameter)
{
    Serial.println("Timeout Monitoring Task Started");
    while (1)
    {
        if (isLoggedIn && (millis() - lastActivityTime > TIMEOUT_DURATION))
        {
            Serial.println("Session timeout - logging out");
            isLoggedIn = false;
            currentPage = 0; // Reset state if needed
                             // resetFlags(); // Call if you have a function to reset
                             // operation states
        }
        vTaskDelay(pdMS_TO_TICKS(1000)); // Check every second
    }
}

// --- Core Route Handlers ---
int countPresentEmployees()
{
    DateTime now = rtc.now();
    char dateStr[11];
    sprintf(dateStr, "%02d-%02d-%02d", now.day(), now.month(), now.year());
    String filename = "/" + String(dateStr) + ".csv";

    Serial.println("Checking attendance file: " + filename);

    if (!SPIFFS.exists(filename))
    {
        Serial.println("Attendance file not found");
        return 0;
    }

    File file = SPIFFS.open(filename, "r");
    if (!file)
    {
        Serial.println("Failed to open attendance file");
        return 0;
    }

    // Track up to 100 unique employee IDs
    const int MAX_PRESENT_EMPLOYEES = 100;
    String seenIds[MAX_PRESENT_EMPLOYEES];
    int count = 0;

    while (file.available() && count < MAX_PRESENT_EMPLOYEES)
    {
        String line = file.readStringUntil('\n');
        line.trim();

        // Skip empty lines
        if (line.length() == 0)
            continue;

        // Split line into fields
        int empIdEnd = line.indexOf(',');
        int nameEnd = line.indexOf(',', empIdEnd + 1);
        int dateEnd = line.indexOf(',', nameEnd + 1);
        int timeEnd = line.indexOf(',', dateEnd + 1);
        int statusEnd = line.indexOf(',', timeEnd + 1);

        if (empIdEnd != -1 && nameEnd != -1 && dateEnd != -1 && timeEnd != -1)
        {
            String status = line.substring(
                timeEnd + 1, statusEnd != -1 ? statusEnd : line.length());
            status.trim();

            if (status == "I")
            {
                String empId = line.substring(0, empIdEnd);
                empId.trim();

                // Check if we've seen this ID before
                bool isNew = true;
                for (int i = 0; i < count; i++)
                {
                    if (seenIds[i] == empId)
                    {
                        isNew = false;
                        break;
                    }
                }

                if (isNew)
                {
                    seenIds[count++] = empId;
                    Serial.println("Found present employee: " + empId);
                }
            }
        }
    }
    file.close();
    Serial.println("Total present employees: " + String(count));
    return count;
}

int countTotalEmployees()
{
    if (!SPIFFS.exists("/MASTER.csv"))
    {
        return 0;
    }

    File file = SPIFFS.open("/MASTER.csv", "r");
    if (!file)
    {
        return 0;
    }

    int count = 0;
    bool firstLine = true;
    while (file.available())
    {
        String line = file.readStringUntil('\n');
        line.trim();

        // Skip empty lines and header
        if (line.length() > 0 && !firstLine)
        {
            // Count only lines with valid employee data (at least EmpId and UserName)
            int comma1 = line.indexOf(',');
            if (comma1 != -1)
            {
                int comma2 = line.indexOf(',', comma1 + 1);
                if (comma2 != -1)
                {
                    count++;
                }
            }
        }
        firstLine = false;
    }
    file.close();
    return count;
}

void handleRoot()
{
    if (isLoggedIn)
    {
        handleMenu1(); // Redirect logged-in users to the main menu
    }
    else
    {
        int presentCount = countPresentEmployees();
        int totalCount = countTotalEmployees();

        String html = "<!DOCTYPE HTML><html><head><title>Dashboard</title>";
        html +=
            "<meta name='viewport' content='width=device-width, initial-scale=1'>";
        html += "<link rel='stylesheet' "
                "href='https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/"
                "css/all.min.css'>";
        html += htmlStyle;
        html += "<style>";
        html += ".dashboard-btn {";
        html +=
            "  display: flex; align-items: center; justify-content: space-between;";
        html += "  padding: 15px; margin: 10px 0; border-radius: 8px;";
        html += "  background-color: rgba(255,255,255,0.2); border: 1px solid "
                "rgba(255,255,255,0.3);";
        html += "  color: #333; text-decoration: none;";
        html += "}";
        html += ".dashboard-btn:hover { background-color: rgba(255,255,255,0.3); }";
        html += ".btn-icon { font-size: 24px; margin-right: 15px; }";
        html += ".btn-text { flex-grow: 1; text-align: left; }";
        html += ".btn-count { font-weight: bold; font-size: 20px; }";
        html += "</style>";
        html += "<script>";
        html += "setTimeout(() => location.reload(), 60000);";
        html += "</script>";
        html += "</head><body><div class='container'>";
        html += "<h1>Employee Dashboard</h1>";

        // Present Employees Button
        html += "<div class='dashboard-btn'>";
        html += "<i class='fas fa-user btn-icon'></i>";
        html += "<div class='btn-text'>PRESENT</div>";
        html += "<div class='btn-count'>" + String(presentCount) + "</div>";
        html += "</div>";

        // Total Employees Button
        html += "<div class='dashboard-btn'>";
        html += "<i class='fas fa-users btn-icon'></i>";
        html += "<div class='btn-text'>TOTAL</div>";
        html += "<div class='btn-count'>" + String(totalCount) + "</div>";
        html += "</div>";

        // Admin Login Button
        html += "<a href='/login' class='dashboard-btn'>";
        html += "<i class='fas fa-lock btn-icon'></i>";
        html += "<div class='btn-text'>Admin Login</div>";
        html += "</a>";

        html += "</div>";

        // Add SPIFFS memory usage (show free space)
        size_t totalBytes = SPIFFS.totalBytes();
        size_t usedBytes = SPIFFS.usedBytes();
        float percentFree = 100.0 - ((usedBytes * 100.0) / totalBytes);

        html += "<div style='display: flex; justify-content: space-between; "
                "margin-top: 20px; padding: 0 15px;'>";
        html += "<div style='font-weight:bold; font-size:medium;'>";
        html += "Storage: " + String(percentFree, 2) + "% free";
        html += "</div>";
        html += "<div>";
        html += "<a href='http://www.printelectronics.com' target='_blank' "
                "style='text-decoration:none; color:inherit;'>";
        html += " &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&"
                "nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&"
                "nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&"
                "nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&"
                "nbsp;&nbsp;&copy; Print Electronics";
        html += "</a>";
        html += "</div>";
        html += "</div>";

        html += "</body></html>";
        server.send(200, "text/html", html);
    }
}

void handleLogin()
{
    if (server.hasArg("username") && server.hasArg("password"))
    {
        preferences.begin("admin-creds", true);
        String storedPass = preferences.getString("PW1", "");
        preferences.end();

        if (server.arg("username") == "admin" &&
            server.arg("password") == storedPass)
        {
            isLoggedIn = true;
            currentPage = 1; // Or manage state differently if needed
            lastActivityTime = millis();
            handleMenu1(); // Go to main menu on successful login (calls function in
                           // menu_main.ino)
        }
        else
        {
            String html = "<!DOCTYPE HTML><html><head><title>Login Failed</title>";
            html += "<meta name='viewport' content='width=device-width, "
                    "initial-scale=1'>";
            html += htmlStyle;
            html += "</head><body><div class='container'>";
            html += "<h1>Login Failed</h1>";
            html += "<p style='color:red;'>Invalid username or password.</p>";
            html += "<form action='/login' method='POST'>";
            html += "Username: <input type='text' name='username' required><br>";
            html += "Password: <input type='password' name='password' required><br>";
            html += "<input type='submit' value='Login' class='button'>";
            html += "</form>";
            html +=
                "<a href='/' class='button back-button'>Try Again</a>"; // Link back
                                                                        // to root
            html += "</div></body></html>";
            server.send(401, "text/html", html); // Unauthorized
        }
    }
    else
    {
        server.send(400, "text/plain", "Missing username or password");
    }
}

void handleLogout()
{
    Serial.println("User logged out.");
    isLoggedIn = false;
    currentPage = 0;
    // resetFlags(); // Reset system state on logout
    server.sendHeader("Location", "/"); // Redirect to login page
    server.send(302, "text/plain", "Logged out");
}

void handleNotFound()
{
    String message = "File Not Found\n\n";
    message += "URI: ";
    message += server.uri();
    message += "\nMethod: ";
    message += (server.method() == HTTP_GET) ? "GET" : "POST";
    message += "\nArguments: ";
    message += server.args();
    message += "\n";
    for (uint8_t i = 0; i < server.args(); i++)
    {
        message += " " + server.argName(i) + ": " + server.arg(i) + "\n";
    }
    server.send(404, "text/plain", message);
}

// --- Server Route Setup ---
void setupServerRoutes()
{
    // Core Routes
    server.on("/", HTTP_GET, handleRoot);
    server.on("/login", HTTP_GET, []()
              {
    String html = "<!DOCTYPE HTML><html><head><title>Login</title>";
    html +=
        "<meta name='viewport' content='width=device-width, initial-scale=1'>";
    html += htmlStyle;
    html += "<script>";
    html += "let timeLeft = 60;";
    html += "function updateTimer() {";
    html += "  timeLeft--;";
    html += "  if(timeLeft <= 0) window.location.href = '/';";
    html += "  setTimeout(updateTimer, 1000);";
    html += "}";
    html += "document.addEventListener('click', () => timeLeft = 60);";
    html += "document.addEventListener('keypress', () => timeLeft = 60);";
    html += "updateTimer();";
    html += "</script>";
    html += "</head><body><div class='container'>";
    html += "<h1>Login</h1>";
    html += "<form action='/login' method='POST'>";
    html += "Username: <input type='text' name='username' required><br>";
    html += "Password: <input type='password' name='password' required><br>";
    html += "<input type='submit' value='Login' class='button'>";
    html += "<div style='margin-top:10px;'>";
    html += "<a href='/forgot-password' style='font-size:12px;'>Forgot "
            "Password?</a>";
    html += "</div>";
    html += "</form></div></body></html>";
    server.send(200, "text/html", html); });
    server.on("/login", HTTP_POST, handleLogin);
    server.on("/logout", HTTP_GET, handleLogout);

    // Password Recovery Routes
    server.on("/forgot-password", HTTP_GET, []()
              {
    String html = "<!DOCTYPE HTML><html><head><title>Password Recovery</title>";
    html +=
        "<meta name='viewport' content='width=device-width, initial-scale=1'>";
    html += htmlStyle;
    html += "</head><body><div class='container'>";
    html += "<h1>Password Recovery</h1>";
    html += "<form action='/verify-security-answer' method='POST'>";
    html +=
        "Security Question: Enter your Mother's Surname before marriage?<br>";
    html += "<input type='text' name='answer' required><br>";
    html += "<input type='submit' value='Verify' class='button'>";
    html += "</form>";
    html += "</div></body></html>";
    server.send(200, "text/html", html); });

    server.on("/verify-security-answer", HTTP_POST, []()
              {
    preferences.begin("admin-creds", true);
    String storedAnswer = preferences.getString("SECQ1", "");
    preferences.end();

    if (server.arg("answer") == storedAnswer) {
      String html = "<!DOCTYPE HTML><html><head><title>Reset Password</title>";
      html += "<meta name='viewport' content='width=device-width, "
              "initial-scale=1'>";
      html += htmlStyle;
      html += "</head><body><div class='container'>";
      html += "<h1>Reset Password</h1>";
      html += "<form action='/reset-password' method='POST'>";
      html +=
          "New Password: <input type='password' name='newpass' required><br>";
      html += "Confirm Password: <input type='password' name='confirmpass' "
              "required><br>";
      html += "<input type='submit' value='Reset' class='button'>";
      html += "</form>";
      html += "</div></body></html>";
      server.send(200, "text/html", html);
    } else {
      String html = "<!DOCTYPE HTML><html><head><title>Recovery Failed</title>";
      html += "<meta name='viewport' content='width=device-width, "
              "initial-scale=1'>";
      html += htmlStyle;
      html += "</head><body><div class='container'>";
      html += "<h1>Recovery Failed</h1>";
      html += "<p style='color:red;'>Incorrect security answer.</p>";
      html += "<a href='/forgot-password' class='button'>Try Again</a>";
      html += "</div></body></html>";
      server.send(401, "text/html", html);
    } });

    server.on("/reset-password", HTTP_POST, []()
              {
    if (server.arg("newpass") == server.arg("confirmpass")) {
      preferences.begin("admin-creds", false);
      preferences.putString("PW1", server.arg("newpass"));
      preferences.end();

      String html = "<!DOCTYPE HTML><html><head><title>Password Reset</title>";
      html += "<meta name='viewport' content='width=device-width, "
              "initial-scale=1'>";
      html += htmlStyle;
      html += "</head><body><div class='container'>";
      html += "<h1>Password Reset Successful</h1>";
      html += "<p>Your password has been updated.</p>";
      html += "<a href='/login' class='button'>Login Now</a>";
      html += "</div></body></html>";
      server.send(200, "text/html", html);
    } else {
      String html = "<!DOCTYPE HTML><html><head><title>Reset Failed</title>";
      html += "<meta name='viewport' content='width=device-width, "
              "initial-scale=1'>";
      html += htmlStyle;
      html += "</head><body><div class='container'>";
      html += "<h1>Reset Failed</h1>";
      html += "<p style='color:red;'>Passwords do not match.</p>";
      html += "<a href='/forgot-password' class='button'>Try Again</a>";
      html += "</div></body></html>";
      server.send(400, "text/html", html);
    } });

    // Menu Navigation Routes (Handlers defined in other files)
    server.on("/menu1", HTTP_GET, handleMenu1);
    server.on("/menu2", HTTP_GET, handleMenu2);
    server.on("/menu3", HTTP_GET, handleMenu3); // Keep or remove if unused

    // Routes Triggering Specific Sub-Menus
    server.on("/M1B2", HTTP_GET,
              handleDeleteMenu); // Delete button on Menu 1 -> Show Delete Menu
    server.on("/M1B4", HTTP_GET,
              handleReportsMenu); // Reports button on Menu 1 -> Show Reports Menu

    // Employee Details Routes
    server.on("/employeeDetails", HTTP_GET,
              handleEmployeeDetails); // Show the form
    server.on("/saveEmpDetails", HTTP_POST,
              handleSaveEmpDetails); // Handle form submission

    // Delete Operation Routes
    server.on("/delete/face", HTTP_GET,
              handleDeleteFace); // Show face delete form
    server.on("/delete/palm", HTTP_GET,
              handleDeletePalm); // Show palm delete form
    server.on("/delete/card", HTTP_GET,
              handleDeleteCard); // Show card delete form
    server.on("/delete/master", HTTP_GET,
              handleDeleteMaster); // Show master delete form
    server.on("/deleteEmployee", HTTP_GET,
              handleDeleteEmployee); // Action: delete single
    server.on("/deleteAllEmployees", HTTP_GET,
              handleDeleteAllEmployees); // Action: delete all

    // Report Routes
    server.on("/viewMaster", HTTP_GET, handleViewMaster);
    server.on("/viewAttendance", HTTP_GET,
              handleViewAttendance); // Shows date select or report
    server.on("/viewTimesheet", HTTP_GET,
              handleViewTimesheet); // Shows date select or report

    // Command Routes (Trigger actions via handleCommand)
    server.on("/M1B1", HTTP_GET,
              []()
              { handleCommand("M1B1"); }); // Enroll Entry Point
    server.on("/M2B1", HTTP_GET,
              []()
              { handleCommand("M2B1"); }); // Enroll Face Type
    server.on("/M2B2", HTTP_GET,
              []()
              { handleCommand("M2B2"); }); // Enroll Palm Type
    server.on("/M2B3", HTTP_GET,
              []()
              { handleCommand("M2B3"); }); // Enroll Finger Type
    server.on("/M2B4", HTTP_GET,
              []()
              { handleCommand("M2B4"); }); // Enroll Card Type
    // Add M3Bx routes here if Menu 3 is used

    server.onNotFound(handleNotFound); // Catch-all for unknown routes
}

// Utility Functions
// And modify the connectWiFi function: for preferences
void connectWiFi()
{
    // First load the credentials
    if (!loadWiFiCredentials())
    {
        Serial.println("Cannot connect - no credentials!");
        return;
    }

    Serial.println("Connecting to WiFi...");
    WiFi.mode(WIFI_STA);
    WiFi.begin(sta_ssid.c_str(), sta_password.c_str());

    int attempts = 0;
    while (WiFi.status() != WL_CONNECTED && attempts < 100)
    {
        delay(500);
        Serial.print(".");
        attempts++;
    }

    if (WiFi.status() == WL_CONNECTED)
    {
        Serial.println("\nWiFi connected!");
        Serial.print("IP address: ");
        Serial.println(WiFi.localIP());

        // Get MAC address
        WiFi.macAddress(wifi_mac);
        Serial.print("MAC Address: ");
        TID = printMacAddress(wifi_mac);
        if (wifi_mac[0] == 0x00 && wifi_mac[1] == 0x00 && wifi_mac[2] == 0x00 &&
            wifi_mac[3] == 0x00 && wifi_mac[4] == 0x00 && wifi_mac[5] == 0x00)
        {
            Serial.println("Invalid MAC address");
        }
        else
        {
            char macstr[12];
            sprintf(macstr, "%02x%02x%02x%02x%02x%02x", wifi_mac[0], wifi_mac[1],
                    wifi_mac[2], wifi_mac[3], wifi_mac[4], wifi_mac[5]);
            String devid = "OANG" + String(macstr);
            setPrefString("attlog", "device_id", devid);
        }

        
        // Setup NTP --- Commented as it is clashing with WiFi connection and panic rebooting
        //Udp.begin(localPort);
        //getTimeFromNTP();
    }
    else
    {
        Serial.println("\nWiFi connection failed!");
		  // Get MAC address from WiFi
  uint8_t ethMac[6];
  WiFi.macAddress(ethMac);
  Ethernet.begin(ethMac); // Initialize Ethernet with MAC address
		    EthernetClient client;
			Serial.print("[DEBUG] Ethernet IP: ");
    Serial.println(Ethernet.localIP());
    }
}

// Then modify the loadWiFiCredentials function:
bool loadWiFiCredentials()
{
    preferences.begin("wifi-config", true); // true = read-only mode
    sta_ssid = preferences.getString("ssid", "");
    sta_password = preferences.getString("password", "");
    preferences.end();

    // Check if credentials exist
    if (sta_ssid.length() > 0)
    {
        Serial.println("Found saved credentials:");
        Serial.print("SSID: ");
        Serial.println(sta_ssid);
        Serial.print("Password length: ");
        Serial.println(sta_password.length());
        return true;
    }

    Serial.println("No saved credentials found!");
    return false;
}
