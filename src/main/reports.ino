// reports.ino
#include "main.h"

// Reports Menu Data
const ButtonInfo reportsMenuButtons[] = {
    {"MASTER", "/viewMaster"},         // Shows Master Report
    {"ATTENDANCE", "/viewAttendance"}, // Shows Attendance Report selection
    {"TIMESHEET", "/viewTimesheet"}    // Shows Timesheet Report selection
};
const char *reportsMenuTitle = "Reports Menu";
const char *reportsMenuBackUrl = "/menu1"; // Back to Main Menu

// --- Reports Menu Handler ---
void handleReportsMenu()
{
  resetCommandState();
  sendMenuPage(reportsMenuTitle, reportsMenuButtons, sizeof(reportsMenuButtons) / sizeof(reportsMenuButtons[0]), reportsMenuBackUrl);
}

// --- Report Display Handlers ---
void handleViewMaster()
{
  if (!isLoggedIn)
  {
    server.sendHeader("Location", "/");
    server.send(302);
    return;
  }
  lastActivityTime = millis();

  if (!SPIFFS.begin(false))
  {
    server.send(500, "text/html", "Failed to mount SPIFFS");
    return;
  }

  File file = SPIFFS.open("/MASTER.csv", "r");
  if (!file)
  {
    String html = generateHtmlHeader("Master List Error");
    html += "<div class='container'><h2>Master List Error</h2>";
    html += "<p>Master file (/MASTER.csv) not found.</p></div>";
    html += generateHtmlFooter("/M1B4"); // Back to Reports Menu
    server.send(404, "text/html", html);
    return;
  }

  String html = generateHtmlHeader("Master List");
  html += "<div class='container'>"; // Use standard container
  html += "<h2>Master List</h2>";

  // Update table headers to match biometric format
  html += "<div style='overflow-x:auto;'>"; // Make table scrollable horizontally
  html += "<table><thead><tr>"
          "<th>EmpId</th>"
          "<th>UserName</th>"
          "<th>FaceId</th>"
          "<th>PalmId</th>"
          "<th>FingerId</th>"
          "<th>CardCSN</th>"
          "</tr></thead><tbody>";

  String line;
  bool isFirstLine = true;
  while (file.available())
  {
    line = file.readStringUntil('\n');
    line.trim();

    // Skip empty lines and header
    if (line.length() == 0 || isFirstLine)
    {
      isFirstLine = false;
      continue;
    }

    // Parse CSV line - more robust parsing
    String parts[6]; // Array to hold the 6 parts
    int partIndex = 0;
    int lastComma = -1;
    for (int i = 0; i < line.length() && partIndex < 6; i++)
    {
      if (line.charAt(i) == ',')
      {
        parts[partIndex++] = line.substring(lastComma + 1, i);
        lastComma = i;
      }
    }
    // Get the last part
    if (partIndex == 5)
    {
      parts[partIndex] = line.substring(lastComma + 1);
    }

    if (partIndex == 5)
    { // Ensure all 6 parts were found
      String empId = parts[0];
      String userName = parts[1];
      String faceId = parts[2];
      String palmId = parts[3];
      String fingerId = parts[4];
      String cardCsn = parts[5];

      html += "<tr>"
              "<td>" +
              empId + "</td>"
                      "<td>" +
              userName + "</td>"
                         "<td>" +
              faceId + "</td>"
                       "<td>" +
              palmId + "</td>"
                       "<td>" +
              fingerId + "</td>"
                         "<td>" +
              cardCsn + "</td>"
                        "</tr>";
    }
    else
    {
      Serial.println("Skipping malformed line in MASTER.csv: " + line);
    }
  }
  file.close();

  html += "</tbody></table></div>";    // Close table body, table, and scrollable div
  html += generateHtmlFooter("/M1B4"); // Back to Reports Menu

  server.send(200, "text/html", html);
}

// Helper for Attendance/Timesheet Date Selection/Display
void handleReportDateSelection(const char *reportTitle, const char *actionUrl)
{
  if (!isLoggedIn)
  {
    server.sendHeader("Location", "/");
    server.send(302);
    return;
  }
  lastActivityTime = millis();

  DateTime now = rtc.now(); // Assumes rtc object is available
  char dateStr[11];
  sprintf(dateStr, "%02d-%02d-%04d", now.day(), now.month(), now.year());

  String html = generateHtmlHeader(reportTitle);
  html += "<div class='container'>"; // Use standard container
  html += "<h2>" + String(reportTitle) + "</h2>";
  html += "<form action='" + String(actionUrl) + "' method='GET'>";
  html += "<div class='input-group'>"; // Use group style for consistency
  html += "<label for='date'>Select Date:</label>";
  // Use standard input styling
  html += "<input type='text' id='date' name='date' placeholder='DD-MM-YYYY' pattern='\\d{2}-\\d{2}-\\d{4}' title='Use format: DD-MM-YYYY' value='" + String(dateStr) + "' required>";
  html += "</div>";
  html += "<button type='submit' class='button'>View Report</button>"; // Standard button
  html += "</form>";
  html += generateHtmlFooter("/M1B4"); // Back to Reports Menu

  server.send(200, "text/html", html);
}

void handleViewAttendance()
{
  if (!isLoggedIn)
  {
    server.sendHeader("Location", "/");
    server.send(302);
    return;
  }

  if (!server.hasArg("date"))
  {
    handleReportDateSelection("Attendance Records", "/viewAttendance");
    return;
  }

  lastActivityTime = millis();
  String dateStr = server.arg("date");
  // Ensure date format is validated on server side if needed
  String filename = "/" + dateStr + ".csv";

  if (!SPIFFS.begin(false))
  {
    server.send(500, "text/html", "Failed to mount SPIFFS");
    return;
  }

  if (!SPIFFS.exists(filename))
  {
    String pageTitle = "Attendance Not Found - " + dateStr;
    String html = generateHtmlHeader(pageTitle.c_str());
    html += "<div class='container'><h2>Attendance Not Found</h2><p>No attendance records found for " + dateStr + ".</p></div>";
    html += generateHtmlFooter("/viewAttendance"); // Back to date selection
    server.send(404, "text/html", html);
    return;
  }

  File file = SPIFFS.open(filename, "r");
  if (!file)
  {
    String pageTitle = "Attendance Error - " + dateStr;
    String html = generateHtmlHeader(pageTitle.c_str());
    html += "<div class='container'><h2>Attendance Error</h2><p>Failed to open attendance file for " + dateStr + ".</p></div>";
    html += generateHtmlFooter("/viewAttendance");
    server.send(500, "text/html", html);
    return;
  }

  String pageTitle = "Attendance Records - " + dateStr;
  String html = generateHtmlHeader(pageTitle.c_str());
  html += "<div class='container'>"; // Use standard container
  html += "<h2>Attendance Records - " + dateStr + "</h2>";
  html += "<div style='overflow-x:auto;'>"; // Make table scrollable
  html += "<table><thead><tr><th>EMP ID</th><th>Name</th><th>Time</th><th>Status</th><th>Device</th></tr></thead><tbody>";

  while (file.available())
  {
    String line = file.readStringUntil('\n');
    line.trim();
    if (line.length() == 0)
      continue;

    // Basic CSV parsing (assumes no commas within fields)
    int idx1 = line.indexOf(',');
    int idx2 = line.indexOf(',', idx1 + 1);
    int idx3 = line.indexOf(',', idx2 + 1);
    int idx4 = line.indexOf(',', idx3 + 1); // Assuming 4 commas for 5 fields initially

    // Adjust parsing if the format is EmpID,Name,Date,Time,Status
    if (idx1 != -1 && idx2 != -1 && idx3 != -1 && idx4 != -1)
    {
      String statusAndDevice = line.substring(idx4 + 1);
      int deviceIdx = statusAndDevice.indexOf(',');

      html += "<tr><td>" + line.substring(0, idx1) + "</td>";    // EMP ID
      html += "<td>" + line.substring(idx1 + 1, idx2) + "</td>"; // Name
      html += "<td>" + line.substring(idx3 + 1, idx4) + "</td>"; // Time
      if (deviceIdx != -1)
      {
        html += "<td>" + statusAndDevice.substring(0, deviceIdx) + "</td>";  // Status
        html += "<td>" + statusAndDevice.substring(deviceIdx + 1) + "</td>"; // Device
      }
      else
      {
        html += "<td>" + statusAndDevice + "</td><td></td>"; // Status, empty Device
      }
      html += "</tr>";
    }
    else
    {
      Serial.println("Skipping malformed line in attendance file: " + line);
    }
  }
  file.close();
  html += "</tbody></table></div>";              // Close table body, table, scroll div
  html += generateHtmlFooter("/viewAttendance"); // Back to date selection

  server.send(200, "text/html", html);
}

// TIMESHEET

void handleViewTimesheet();
String calculateWorkHours(const String &timeIn, const String &timeOut);

void handleViewTimesheet()
{
  if (!isLoggedIn)
  {
    server.sendHeader("Location", "/");
    server.send(302, "text/plain", "");
    return;
  }

  lastActivityTime = millis();

  // If no date specified, show the date selection page
  if (!server.hasArg("date"))
  {
    DateTime now = rtc.now();
    char dateStr[11];
    sprintf(dateStr, "%02d-%02d-%04d", now.day(), now.month(), now.year());

    String html = "<!DOCTYPE HTML><html>"
                  "<head>"
                  "<title>Timesheet Report</title>"
                  "<meta name='viewport' content='width=device-width, initial-scale=1'>"
                  "<style>"
                  "body { font-family: Arial, sans-serif; margin: 0; padding: 20px; background: #f0f0f0; }"
                  ".container { max-width: 600px; margin: 20px auto; padding: 20px; background: white; "
                  "border-radius: 5px; box-shadow: 0 0 10px rgba(0,0,0,0.1); }"
                  "h2 { text-align: center; color: #333; margin-bottom: 20px; }"
                  ".form-group { margin-bottom: 20px; }"
                  "label { display: block; margin-bottom: 5px; color: #666; }"
                  "input[type='text'] { width: 100%; padding: 8px; border: 1px solid #ddd; "
                  "border-radius: 4px; font-size: 16px; }"
                  ".button { display: block; width: 100%; padding: 10px; margin-top: 20px; "
                  "background: #4CAF50; color: white; border: none; border-radius: 4px; "
                  "cursor: pointer; font-size: 16px; }"
                  ".back-btn { background: #f44336; margin-top: 10px; }"
                  "#timer { text-align: center; margin-top: 20px; color: #666; }"
                  "</style>" +
                  String(timerScript) + "</head><body><div class='container'>"
                                        "<h2>Timesheet Report</h2>"
                                        "<form action='/viewTimesheet' method='GET'>"
                                        "<div class='form-group'>"
                                        "<label for='date'>Select Date:</label>"
                                        "<input type='text' id='date' name='date' "
                                        "placeholder='DD-MM-YYYY' pattern='\\d{2}-\\d{2}-\\d{4}' "
                                        "title='Please use format: DD-MM-YYYY' value='" +
                  String(dateStr) + "'>"
                                    "</div>"
                                    "<button type='submit' class='button'>View Timesheet</button>"
                                    "<button type='button' class='button back-btn' onclick='location.href=\"/M1B4\"'>Back</button>"
                                    "</div>"
                                    "<div id='timer'></div>"
                                    "</div></body></html>";

    server.send(200, "text/html", html);
    return;
  }

  String dateStr = server.arg("date");
  String sourceFile = "/" + dateStr + ".csv";

  if (!SPIFFS.exists(sourceFile))
  {
    String html = "<!DOCTYPE HTML><html>"
                  "<head>"
                  "<meta name='viewport' content='width=device-width, initial-scale=1'>"
                  "<style>"
                  "body { font-family: Arial, sans-serif; text-align: center; padding: 20px; }"
                  ".message { color: #f44336; margin: 20px 0; }"
                  ".button { display: inline-block; padding: 10px 20px; background: #4CAF50; "
                  "color: white; text-decoration: none; border-radius: 4px; }"
                  "</style>"
                  "</head>"
                  "<body>"
                  "<div class='message'>No attendance records found for " +
                  dateStr + "</div>"
                            "<a href='/viewTimesheet' class='button'>Back</a>"
                            "</body></html>";

    server.send(200, "text/html", html);
    return;
  }

  // Structure to hold employee timesheet data
  struct EmployeeTime
  {
    String empId;
    String name;
    String timeIn;
    String timeOut;
    String workHours;
    bool used; // Flag to mark if this slot is used

    EmployeeTime()
        : used(false) {} // Constructor to initialize used flag
  };

  // Allocate array in PSRAM
  const int T_MAX_EMPLOYEES = 100; // Adjust based on your needs
  EmployeeTime *timesheet = (EmployeeTime *)ps_malloc(T_MAX_EMPLOYEES * sizeof(EmployeeTime));

  if (!timesheet)
  {
    server.send(500, "text/plain", "Memory allocation failed");
    return;
  }

  // Initialize array
  for (int i = 0; i < T_MAX_EMPLOYEES; i++)
  {
    new (&timesheet[i]) EmployeeTime(); // Placement new to properly initialize objects
  }

  int employeeCount = 0;

  // Read the source file
  File file = SPIFFS.open(sourceFile, "r");
  if (!file)
  {
    free(timesheet);
    server.send(500, "text/plain", "Failed to open source file");
    return;
  }

  // Process each line
  while (file.available())
  {
    String line = file.readStringUntil('\n');
    line.trim();
    if (line.length() == 0)
      continue;

    // Parse CSV line
    int idx1 = line.indexOf(',');
    int idx2 = line.indexOf(',', idx1 + 1);
    int idx3 = line.indexOf(',', idx2 + 1);
    int idx4 = line.indexOf(',', idx3 + 1);

    if (idx1 >= 0 && idx2 >= 0 && idx3 >= 0 && idx4 >= 0)
    {
      String empId = line.substring(0, idx1);
      String name = line.substring(idx1 + 1, idx2);
      String time = line.substring(idx3 + 1, idx4);
      String statusAndDevice = line.substring(idx4 + 1);
      int deviceIdx = statusAndDevice.indexOf(',');
      String status = deviceIdx != -1 ? statusAndDevice.substring(0, deviceIdx) : statusAndDevice;

      status.trim();

      // Find existing employee or empty slot
      int empIndex = -1;
      for (int i = 0; i < T_MAX_EMPLOYEES; i++)
      {
        if (timesheet[i].used && timesheet[i].empId == empId)
        {
          empIndex = i;
          break;
        }
        else if (!timesheet[i].used && empIndex == -1)
        {
          empIndex = i;
        }
      }

      if (empIndex >= 0)
      {
        if (!timesheet[empIndex].used)
        {
          // New employee
          timesheet[empIndex].used = true;
          timesheet[empIndex].empId = empId;
          timesheet[empIndex].name = name;
          if (status == "I")
          {
            timesheet[empIndex].timeIn = time;
          }
          else if (status == "O")
          {
            timesheet[empIndex].timeOut = time;
          }
          employeeCount++;
        }
        else
        {
          // Existing employee
          if (status == "I" && timesheet[empIndex].timeIn.length() == 0)
          {
            timesheet[empIndex].timeIn = time;
          }
          else if (status == "O")
          {
            timesheet[empIndex].timeOut = time;
          }
        }
      }
    }
  }
  file.close();

  // Calculate work hours
  for (int i = 0; i < T_MAX_EMPLOYEES; i++)
  {
    if (timesheet[i].used)
    {
      if (timesheet[i].timeIn.length() > 0 && timesheet[i].timeOut.length() > 0)
      {
        timesheet[i].workHours = calculateWorkHours(timesheet[i].timeIn, timesheet[i].timeOut);
      }
    }
  }

  // Generate HTML response
  String html = "<!DOCTYPE HTML><html>"
                "<head>"
                "<title>Timesheet Report</title>"
                "<meta name='viewport' content='width=device-width, initial-scale=1'>"
                "<style>"
                "body { font-family: 'Courier New', monospace; margin: 0; padding: 20px; background: #f0f0f0; }"
                ".container { max-width: 95%; margin: 20px auto; padding: 20px; background: white; "
                "border-radius: 5px; box-shadow: 0 0 10px rgba(0,0,0,0.1); overflow-x: auto; }"
                "h2 { text-align: center; color: #333; margin-bottom: 20px; }"
                "table { width: 100%; border-collapse: collapse; margin-bottom: 20px; }"
                "th, td { padding: 12px; text-align: left; border-bottom: 1px solid #ddd; }"
                "th { background-color: #f5f5f5; }"
                "tr:hover { background-color: #f9f9f9; }"
                ".back-btn { display: block; width: 100%; padding: 10px; background: #f44336; "
                "color: white; border: none; border-radius: 4px; cursor: pointer; text-align: center; "
                "text-decoration: none; margin-top: 20px; }"
                "</style>"
                "</head>"
                "<body>"
                "<div class='container'>"
                "<h2>Timesheet Report - " +
                dateStr + "</h2>"
                          "<table>"
                          "<tr><th>EMP ID</th><th>Name</th><th>Time In</th><th>Time Out</th><th>Work Hours</th></tr>";

  // Add table rows
  for (int i = 0; i < T_MAX_EMPLOYEES; i++)
  {
    if (timesheet[i].used)
    {
      html += "<tr><td>" + timesheet[i].empId + "</td><td>" + timesheet[i].name + "</td><td>" + timesheet[i].timeIn + "</td><td>" + timesheet[i].timeOut + "</td><td>" + timesheet[i].workHours + "</td></tr>";
    }
  }

  html += "</table>"
          "<a href='/viewTimesheet' class='back-btn'>Back</a>"
          "</div></body></html>";

  // Free the allocated memory
  free(timesheet);

  server.send(200, "text/html", html);
}

String calculateWorkHours(const String &timeIn, const String &timeOut)
{
  if (timeIn.length() == 0 || timeOut.length() == 0)
  {
    return "";
  }

  // Parse time strings (assuming format HH:MM:SS)
  int inHour = timeIn.substring(0, 2).toInt();
  int inMin = timeIn.substring(3, 5).toInt();
  int inSec = timeIn.substring(6, 8).toInt();

  int outHour = timeOut.substring(0, 2).toInt();
  int outMin = timeOut.substring(3, 5).toInt();
  int outSec = timeOut.substring(6, 8).toInt();

  // Convert to seconds
  int inTotal = inHour * 3600 + inMin * 60 + inSec;
  int outTotal = outHour * 3600 + outMin * 60 + outSec;

  // Calculate difference
  int diffSeconds = outTotal - inTotal;
  if (diffSeconds < 0)
  {
    diffSeconds += 24 * 3600; // Add 24 hours if checkout is next day
  }

  // Convert back to HH:MM:SS
  int hours = diffSeconds / 3600;
  int minutes = (diffSeconds % 3600) / 60;
  int seconds = diffSeconds % 60;

  char result[9];
  sprintf(result, "%02d:%02d:%02d", hours, minutes, seconds);
  return String(result);
}
