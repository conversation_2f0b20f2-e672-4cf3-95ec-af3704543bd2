// deletion.ino
#include "main.h"

// Delete Menu Data
const ButtonInfo deleteMenuButtons[] = {
    {"Delete FACE", "/delete/face"},         // Goes to Face Delete Form
    {"Delete PALM", "/delete/palm"},         // Goes to Palm Delete Form
    {"Delete CARD", "/delete/card"},         // New: Goes to Card Delete Form
    {"Delete FROM MASTER", "/delete/master"} // New: Goes to Master Delete Form
};
const char *deleteMenuTitle = "Select Delete Type";
const char *deleteMenuBackUrl = "/menu1"; // Back to Main Menu

// --- Delete Menu Handler ---
void handleDeleteMenu()
{
  resetCommandState();
  sendMenuPage(deleteMenuTitle, deleteMenuButtons, sizeof(deleteMenuButtons) / sizeof(deleteMenuButtons[0]), deleteMenuBackUrl);
}

// --- Delete Operation Handlers ---
void handleDeleteFace()
{
  if (!isLoggedIn)
  {
    server.sendHeader("Location", "/");
    server.send(302);
    return;
  }
  lastActivityTime = millis();
  String page = FPSTR(deleteEmployeePage);
  page.replace("%TYPE%", "FACE");
  page.replace("%STYLE%", FPSTR(htmlStyle));
  page.replace("%TIMER_SCRIPT%", FPSTR(timerScript));
  server.send(200, "text/html", page);
}

void handleDeletePalm()
{
  if (!isLoggedIn)
  {
    server.sendHeader("Location", "/");
    server.send(302);
    return;
  }
  lastActivityTime = millis();
  String page = FPSTR(deleteEmployeePage);
  page.replace("%TYPE%", "PALM");
  page.replace("%STYLE%", FPSTR(htmlStyle));
  page.replace("%TIMER_SCRIPT%", FPSTR(timerScript));
  server.send(200, "text/html", page);
}

// Modify handleDeleteEmployee() to include MASTER deletion
void handleDeleteEmployee()
{
  if (!isLoggedIn)
  {
    server.sendHeader("Location", "/");
    server.send(302);
    return;
  }
  lastActivityTime = millis();

  if (!server.hasArg("type") || !server.hasArg("id"))
  {
    server.send(400, "text/plain", "Missing type or id");
    return;
  }
  String type = server.arg("type");
  int id = server.arg("id").toInt();
  if (id <= 0)
  {
    server.send(400, "text/plain", "Invalid ID");
    return;
  }

  if (type.equalsIgnoreCase("MASTER"))
  {
    // First, verify if the ID exists in MASTER.csv
    bool idExists = false;
    int storedFaceId = 0;
    int storedPalmId = 0;
    String cardCSN = "";
    String recordToDelete;

    File file = SPIFFS.open("/MASTER.csv", "r");
    String newContent;

    if (file)
    {
      Serial.printf("Checking MASTER.csv for EMP_ID: %d with CARD\n", id);
      while (file.available())
      {
        String line = file.readStringUntil('\n');
        line.trim();

        // Parse the line (comma-delimited)
        int firstComma = line.indexOf(',');
        if (firstComma != -1)
        {
          String empIdStr = line.substring(0, firstComma);
          if (empIdStr.toInt() == id)
          {
            idExists = true;

            // Extract card CSN (it's the 5th field)
            int secondComma = line.indexOf(',', firstComma + 1);
            int thirdComma = line.indexOf(',', secondComma + 1);
            int fourthComma = line.indexOf(',', thirdComma + 1);
            int fifthComma = line.indexOf(',', fourthComma + 1);

            if (fourthComma != -1 && fifthComma != -1)
            {
              cardCSN = line.substring(fourthComma + 1, fifthComma);
            }
            break;
          }
        }
      }
      file.close();
    }
    else
    {
      Serial.println("Failed to open MASTER.csv");
      server.send(500, "text/plain", "Failed to access MASTER.csv");
      return;
    }

    if (!idExists)
    {
      Serial.printf("EMP_ID %d not found in MASTER.csv\n", id);
      server.send(404, "text/plain", "Employee ID not found");
      return;
    }

    // Delete face if exists
    if (storedFaceId > 0)
    {
      Serial.printf("Deleting face ID: %d\n", storedFaceId);
      faceIdForDeletion = storedFaceId;
      EMP_ID = id;
      face = 4; // Trigger face deletion
      // Wait for face deletion to complete
      delay(1000); // Give some time for the operation
    }

    // Delete palm if exists
    if (storedPalmId > 0)
    {
      Serial.printf("Deleting palm ID: %d\n", storedPalmId);
      faceIdForDeletion = storedPalmId;
      EMP_ID = id;
      face = 9; // Trigger palm deletion
      // Wait for palm deletion to complete
      delay(1000); // Give some time for the operation
    }

    // Update MASTER.csv with CardCSN cleared but record kept
    File writeFile = SPIFFS.open("/MASTER.csv", "w");
    if (writeFile)
    {
      writeFile.print(newContent);
      writeFile.close();
      Serial.printf("Successfully cleared CardCSN for EMP_ID %d in MASTER.csv\n", id);
      newFileReceived = true; // re-populate LUT in PSRAM
      Serial.println("Set newFileReceived flag to refresh LUT");
    }
    else
    {
      Serial.println("Failed to update MASTER.csv");
      server.send(500, "text/plain", "Failed to update MASTER.csv");
      return;
    }

    // Display a "Processing" page that redirects back
    String response = R"(<!DOCTYPE HTML><html><head><title>Deleting from MASTER...</title>
            <meta name='viewport' content='width=device-width, initial-scale=1'>)" +
                      String(FPSTR(htmlStyle)) +
                      R"(<meta http-equiv='refresh' content='3;url=/delete/master' />
            </head><body><div class='container'><h2>Deleting Employee ID: )" +
                      String(id) + R"( from MASTER</h2>
            <div class='loader'></div><p>Please wait...</p>
        <p>Face ID: )" +
                      String(storedFaceId) +
                      R"(</p>
        <p>Palm ID: )" +
                      String(storedPalmId) +
                      R"(</p>
        <p>Card CSN: Cleared</p>
            </div></body></html>)";
    server.send(200, "text/html", response);
    return;
  }

  // Handle FACE deletion
  else if (type.equalsIgnoreCase("FACE"))
  {
    // Verify if the ID exists in MASTER.csv
    bool idExists = false;
    int storedFaceId = 0;

    File file = SPIFFS.open("/MASTER.csv", "r");

    if (file)
    {
      Serial.printf("Checking MASTER.csv for EMP_ID: %d with FACE\n", id);
      while (file.available())
      {
        String line = file.readStringUntil('\n');
        line.trim();

        // Parse the line
        int firstComma = line.indexOf(',');
        if (firstComma != -1)
        {
          String empIdStr = line.substring(0, firstComma);
          // Convert input ID to 6-digit format for comparison
          char paddedId[7];
          snprintf(paddedId, sizeof(paddedId), "%06d", id);
          if (empIdStr.equals(paddedId))
          {
            idExists = true;

            // Extract faceId
            int secondComma = line.indexOf(',', firstComma + 1);
            int thirdComma = line.indexOf(',', secondComma + 1);

            if (secondComma != -1 && thirdComma != -1)
            {
              String faceIdStr = line.substring(secondComma + 1, thirdComma);
              storedFaceId = faceIdStr.toInt();
            }
            break;
          }
        }
      }
      file.close();
    }
    else
    {
      Serial.println("Failed to open MASTER.csv");
      server.send(500, "text/plain", "Failed to access MASTER.csv");
      return;
    }

    if (!idExists)
    {
      Serial.printf("EMP_ID %d not found in MASTER.csv\n", id);
      server.send(404, "text/plain", "Employee ID not found");
      return;
    }

    // Delete face if exists (but don't remove from MASTER.csv)
    if (storedFaceId > 0)
    {
      Serial.printf("Deleting face ID: %d (keeping record in MASTER)\n", storedFaceId);
      faceIdForDeletion = storedFaceId;
      EMP_ID = id;
      face = 4; // Trigger face deletion
      // Wait for face deletion to complete
      delay(1000); // Give some time for the operation
    }
    else
    {
      Serial.println("No face ID found for this employee");
      server.send(404, "text/plain", "No face record found for this employee");
      return;
    }

    // Display a "Processing" page that redirects back
    String response = R"(<!DOCTYPE HTML><html><head><title>Deleting Face...</title>
            <meta name='viewport' content='width=device-width, initial-scale=1'>)" +
                      String(FPSTR(htmlStyle)) +
                      R"(<meta http-equiv='refresh' content='3;url=/delete/face' />
            </head><body><div class='container'><h2>Deleting Face for Employee ID: )" +
                      String(id) + R"(</h2>
            <div class='loader'></div><p>Please wait...</p>
            <p>Face ID: )" +
                      String(storedFaceId) + R"(</p>
            </div></body></html>)";
    server.send(200, "text/html", response);
    return;
  }
  // Handle PALM deletion
  else if (type.equalsIgnoreCase("PALM"))
  {
    // Verify if the ID exists in MASTER.csv
    bool idExists = false;
    int storedPalmId = 0;

    File file = SPIFFS.open("/MASTER.csv", "r");

    if (file)
    {
      Serial.printf("Checking MASTER.csv for EMP_ID: %d with PALM\n", id);
      while (file.available())
      {
        String line = file.readStringUntil('\n');
        line.trim();

        // Parse the line
        int firstComma = line.indexOf(',');
        if (firstComma != -1)
        {
          String empIdStr = line.substring(0, firstComma);
          if (empIdStr.toInt() == id)
          {
            idExists = true;

            // Extract palmId
            int secondComma = line.indexOf(',', firstComma + 1);
            int thirdComma = line.indexOf(',', secondComma + 1);
            int fourthComma = line.indexOf(',', thirdComma + 1);

            if (thirdComma != -1 && fourthComma != -1)
            {
              String palmIdStr = line.substring(thirdComma + 1, fourthComma);
              storedPalmId = palmIdStr.toInt();
            }
            break;
          }
        }
      }
      file.close();
    }
    else
    {
      Serial.println("Failed to open MASTER.csv");
      server.send(500, "text/plain", "Failed to access MASTER.csv");
      return;
    }

    if (!idExists)
    {
      Serial.printf("EMP_ID %d not found in MASTER.csv\n", id);
      server.send(404, "text/plain", "Employee ID not found");
      return;
    }

    // Delete palm if exists (but don't remove from MASTER.csv)
    if (storedPalmId > 0)
    {
      Serial.printf("Deleting palm ID: %d (keeping record in MASTER)\n", storedPalmId);
      faceIdForDeletion = storedPalmId;
      EMP_ID = id;
      face = 9; // Trigger palm deletion
      // Wait for palm deletion to complete
      delay(1000); // Give some time for the operation
    }
    else
    {
      Serial.println("No palm ID found for this employee");
      server.send(404, "text/plain", "No palm record found for this employee");
      return;
    }

    // Display a "Processing" page that redirects back
    String response = R"(<!DOCTYPE HTML><html><head><title>Deleting Palm...</title>
            <meta name='viewport' content='width=device-width, initial-scale=1'>)" +
                      String(FPSTR(htmlStyle)) +
                      R"(<meta http-equiv='refresh' content='3;url=/delete/palm' />
            </head><body><div class='container'><h2>Deleting Palm for Employee ID: )" +
                      String(id) + R"(</h2>
            <div class='loader'></div><p>Please wait...</p>
            <p>Palm ID: )" +
                      String(storedPalmId) + R"(</p>
            </div></body></html>)";
    server.send(200, "text/html", response);
    return;
  }

  // Handle CARD deletion
  else if (type.equalsIgnoreCase("CARD"))
  {
    // Verify if the ID exists in MASTER.csv
    bool idExists = false;
    String cardCSN = "";
    bool isFirstLine = true;

    File file = SPIFFS.open("/MASTER.csv", "r");

    if (file)
    {
      Serial.printf("Checking MASTER.csv for EMP_ID: %d with CARD\n", id);
      while (file.available())
      {
        String line = file.readStringUntil('\n');
        line.trim();

        // Skip header line
        if (isFirstLine)
        {
          isFirstLine = false;
          continue;
        }

        // Parse the line (comma-delimited)
        int firstComma = line.indexOf(',');
        if (firstComma != -1)
        {
          String empIdStr = line.substring(0, firstComma);
          if (empIdStr.toInt() == id)
          {
            idExists = true;

            // CardCSN is the 6th column (after 5 commas)
            int commaCount = 0;
            int lastCommaPos = 0;
            for (int i = 0; i < line.length(); i++)
            {
              if (line.charAt(i) == ',')
              {
                commaCount++;
                if (commaCount == 5)
                {
                  cardCSN = line.substring(i + 1);
                  // Remove any trailing commas/newlines
                  int endPos = cardCSN.indexOf(',');
                  if (endPos != -1)
                  {
                    cardCSN = cardCSN.substring(0, endPos);
                  }
                  break;
                }
                lastCommaPos = i;
              }
            }
            break;
          }
        }
      }
      file.close();
    }
    else
    {
      Serial.println("Failed to open MASTER.csv");
      server.send(500, "text/plain", "Failed to access MASTER.csv");
      return;
    }

    if (!idExists)
    {
      Serial.printf("EMP_ID %d not found in MASTER.csv\n", id);
      server.send(404, "text/plain", "Employee ID not found");
      return;
    }

    // Delete card if exists and update MASTER.csv
    if (cardCSN.length() > 0)
    {
      Serial.printf("Deleting card CSN: %s\n", cardCSN.c_str());
      EMP_ID = id;
      // face = 11; // Trigger card deletion

      // Read and update MASTER.csv to clear CardCSN
      File file = SPIFFS.open("/MASTER.csv", "r");
      String newContent;
      if (file)
      {
        bool isFirstLine = true;
        while (file.available())
        {
          String line = file.readStringUntil('\n');
          line.trim();

          if (isFirstLine)
          {
            newContent += line + "\n";
            isFirstLine = false;
            continue;
          }

          int firstComma = line.indexOf(',');
          if (firstComma != -1)
          {
            String empIdStr = line.substring(0, firstComma);
            if (empIdStr.toInt() == id)
            {
              // Rebuild line with empty CardCSN (5th field)
              int fieldEnds[6];
              fieldEnds[0] = firstComma;
              for (int i = 1; i < 6; i++)
              {
                fieldEnds[i] = line.indexOf(',', fieldEnds[i - 1] + 1);
              }

              if (fieldEnds[4] != -1)
              {
                String newLine = line.substring(0, fieldEnds[4] + 1) + (fieldEnds[5] != -1 ? line.substring(fieldEnds[5]) : "");
                newContent += newLine + "\n";
              }
              else
              {
                newContent += line + "\n";
              }
            }
            else
            {
              newContent += line + "\n";
            }
          }
        }
        file.close();

        // Write updated content back to MASTER.csv
        File writeFile = SPIFFS.open("/MASTER.csv", "w");
        if (writeFile)
        {
          writeFile.print(newContent);
          writeFile.close();
          Serial.println("Updated MASTER.csv with cleared CardCSN");
          Serial.println("New content:\n" + newContent);
          newFileReceived = true; // re-populate LUT in PSRAM
        }
      }
    }
    else
    {
      Serial.println("No card CSN found for this employee");
      server.send(404, "text/plain", "No card record found for this employee");
      return;
    }

    // Display a "Processing" page that redirects back
    String response = R"(<!DOCTYPE HTML><html><head><title>Deleting Card...</title>
            <meta name='viewport' content='width=device-width, initial-scale=1'>)" +
                      String(FPSTR(htmlStyle)) +
                      R"(<meta http-equiv='refresh' content='3;url=/delete/card' />
            </head><body><div class='container'><h2>Deleting Card for Employee ID: )" +
                      String(id) + R"(</h2>
            <div class='loader'></div><p>Please wait...</p>
            <p>Card CSN: )" +
                      cardCSN + R"(</p>
            </div></body></html>)";
    server.send(200, "text/html", response);
    return;
  }

  // If we get here, the type is invalid
  server.send(400, "text/plain", "Invalid deletion type");
}

// Add new handler for Master delete form
void handleDeleteMaster()
{
  if (!isLoggedIn)
  {
    server.sendHeader("Location", "/");
    server.send(302);
    return;
  }
  lastActivityTime = millis();
  String page = FPSTR(deleteEmployeePage);
  page.replace("%TYPE%", "MASTER");
  page.replace("%STYLE%", FPSTR(htmlStyle));
  page.replace("%TIMER_SCRIPT%", FPSTR(timerScript));
  server.send(200, "text/html", page);
}

void handleDeleteCard()
{
  if (!isLoggedIn)
  {
    server.sendHeader("Location", "/");
    server.send(302);
    return;
  }
  lastActivityTime = millis();
  String page = FPSTR(deleteEmployeePage);
  page.replace("%TYPE%", "CARD");
  page.replace("%STYLE%", FPSTR(htmlStyle));
  page.replace("%TIMER_SCRIPT%", FPSTR(timerScript));
  server.send(200, "text/html", page);
}

void handleDeleteAllEmployees()
{
  if (!isLoggedIn)
  {
    server.sendHeader("Location", "/");
    server.send(302);
    return;
  }
  lastActivityTime = millis();

  if (!server.hasArg("type"))
  {
    server.send(400, "text/plain", "Missing type");
    return;
  }
  String type = server.arg("type");

  // Set state for the main loop/scanner task to perform deletion
  if (type.equalsIgnoreCase("FACE"))
  {
    face = 5; // Signal delete all faces
  }
  else if (type.equalsIgnoreCase("PALM"))
  {
    face = 10; // Signal delete all palms (Assign a unique code)
  }
  else
  {
    server.send(400, "text/plain", "Invalid deletion type");
    return;
  }
  // EMP_ID is not needed for "delete all"

  Serial.printf("Web request: Delete ALL %s employees\n", type.c_str());

  // Display a "Processing" page that redirects back
  String redirectUrl = type;
  redirectUrl.toLowerCase();
  String response = R"(<!DOCTYPE HTML><html><head><title>Deleting All...</title>
        <meta name='viewport' content='width=device-width, initial-scale=1'>)" +
                    String(FPSTR(htmlStyle)) + // Include style
                    R"(<meta http-equiv='refresh' content='5;url=/delete/)" + redirectUrl + R"(' /> <!-- Redirect after 5 secs -->
        </head><body><div class='container'><h2>Deleting ALL )" +
                    type + R"( Employees</h2>
        <div class='loader'></div><p>Please wait, this may take some time...</p></div></body></html>)";
  server.send(200, "text/html", response);

  // The actual deletion happens in the background task based on 'face' state
}
